{"name": "loanmanager", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration development", "build:prod": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "extract-i18n": "node scripts/extract-i18n-strings.js", "update-translations": "node scripts/extract-i18n-strings.js", "postinstall": "echo 'Installation completed with legacy-peer-deps'"}, "private": true, "dependencies": {"@angular/animations": "^19.2.14", "@angular/common": "^19.2.14", "@angular/compiler": "^19.2.14", "@angular/core": "^19.2.14", "@angular/forms": "^19.2.14", "@angular/platform-browser": "^19.2.14", "@angular/platform-browser-dynamic": "^19.2.14", "@angular/router": "^19.2.14", "@fortawesome/fontawesome-free": "^6.7.2", "@zxing/library": "^0.21.3", "angular-confirmation-popover": "^7.0.0", "bootstrap": "^5.3.0", "bootstrap-icons": "^1.13.1", "chart.js": "^4.5.0", "file-saver": "^2.0.5", "ngx-bootstrap": "^19.0.2", "ngx-chips": "3.0.0", "ngx-print": "3.1.0", "ngx-dropzone": "^3.1.0", "ngx-toastr": "19.0.0", "protractor": "^7.0.0", "rxjs": "~7.8.0", "tslib": "^2.8.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.15", "@angular/cli": "^19.2.15", "@angular/compiler-cli": "^19.2.14", "@types/jasmine": "~5.1.0", "@types/node": "^22.0.0", "jasmine-core": "~5.4.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "^5.8.3"}}