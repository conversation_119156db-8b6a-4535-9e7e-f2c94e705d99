/* Global styles - Using Bootstrap only */

/* Custom CSS variables for consistent theming with <PERSON><PERSON><PERSON> */
:root {
  --theme-primary: #590c30;
  --theme-secondary: #46dad3;
}

/* Minimal custom styles - use Bootstrap classes instead */

.font-helvetica{
  font-family: Helvetica Neue, Helvetica, Arial
}


input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.btn-theme {
  color: #ffffff;
  background-color: #590c30;
  border-color: #600c30;
}

.btn-theme:hover {
  background-color: #fff;
  color: #620c30;
  border-color: #000000;
}

.btn-theme:focus, .btn-theme.focus {
  box-shadow: 0 0 0 .2rem #590c30
}

.btn-theme.disabled, .btn-theme:disabled {
  color: #fff;
  background-color: #590c30;
  border-color: #650c30;
}

.btn-theme:not(:disabled):not(.disabled):active, .btn-theme:not(:disabled):not(.disabled).active, .show > .btn-theme.dropdown-toggle {
  color: #fff;
  background-color: #590c30;
  border-color: #000
}

.btn-theme:not(:disabled):not(.disabled):active:focus, .btn-theme:not(:disabled):not(.disabled).active:focus, .show > .btn-theme.dropdown-toggle:focus {
  box-shadow: 0 0 0 .2rem #590c30
}

.btn-outline-theme {
  color: #590c30;
  background-color: transparent;
  background-image: none;
  border-color: #590c30
}

.btn-outline-theme:hover {
  color: #fff;
  background-color: #590c30;
  border-color: #590c30
}

.btn-outline-theme:focus, .btn-outline-primary.focus {
  box-shadow: 0 0 0 .2rem #590c30
}

.btn-outline-theme.disabled, .btn-outline-primary:disabled {
  color: #590c30;
  background-color: transparent
}

.btn-outline-theme:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active, .show > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #590c30;
  border-color: #590c30
}

.btn-outline-theme:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 .2rem #590c30
}

.btn-warning:hover{
  color: #590c30;
  background-color: #fff;
}

.ng-valid[required], .ng-valid.required  {
  border-left: 5px solid #42A948; /* green */
}

.ng-invalid:not(form).form-control {
  border-left: 5px solid #a94442; /* red */
}
