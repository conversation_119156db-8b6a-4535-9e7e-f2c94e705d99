import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {P404Component, P500Component} from './error';
import {LoginComponent} from './home/<USER>/component/login/login.component';
import {HomeComponent} from './home/<USER>';
import {AuthGuard} from './guard/auth.guard';
import {HeaderComponent} from './home/<USER>/layout/header/header.component';
import {FooterComponent} from './home/<USER>/layout/footer/footer.component';
import {RightSidebarComponent} from './home/<USER>/layout/right-sidebar/right-sidebar.component';
import {P401Component} from './error/401.component';
import {StarterComponent} from './home/<USER>/starter/starter.component';


import {CheckLoanStatusComponent} from "./home/<USER>/borrower/component/check-loan-status/check-loan-status.component";
import {DashboardComponent} from './home/<USER>/dashboard/dashboard.component';

export const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'home/starter'
  },
  {
    path: '404',
    component: P404Component
  },
  {
    path: '500',
    component: P500Component
  },
  {
    path: '401',
    component: P401Component
  },
  {
    path: 'login',
    component: LoginComponent
  },
  {
    path: 'check_loan',
    component: CheckLoanStatusComponent,
  },
  {
    path: 'home',
    component: HomeComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: 'starter',
        component: StarterComponent,
      },
      {
        path: 'dashboard',
        component: DashboardComponent,
      },
      {
        path: 'admin',
        loadChildren: () => import('./home/<USER>/admin.module').then(a => a.AdminModule)
      },
      {
        path: 'business',
        loadChildren: () => import ('./home/<USER>/business/business.module').then(b => b.BusinessModule)
      },
      {
        path: 'hr',
        loadChildren: () => import('./home/<USER>/hr/hr.module').then(a => a.HrModule)
      },
      {
        path: 'borrower',
        loadChildren: () => import('./home/<USER>/borrower/borrower.module').then(b => b.BorrowerModule)
      },
      {
        path: 'report',
        loadChildren: () => import('./home/<USER>/report/report.module').then(b => b.ReportModule)
      }
    ]
  }];

export const routeParams = [HeaderComponent, FooterComponent, RightSidebarComponent, P401Component, P404Component, P500Component,
  HomeComponent, LoginComponent, StarterComponent, CheckLoanStatusComponent, DashboardComponent];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})

export class AppRoutingModule {
}

