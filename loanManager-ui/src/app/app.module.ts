import {NgModule} from '@angular/core';
import {AppRoutingModule, routeParams} from './app-routing.module';
import {AppComponent} from './app.component';
import {CommonModule} from '@angular/common';
import {BrowserModule} from '@angular/platform-browser';

import {CoreModule} from './home/<USER>/core.module';
import {AdminModule} from './home/<USER>/admin.module';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {ReportModule} from "./home/<USER>/report/report.module";
import {ModalModule} from "ngx-bootstrap/modal";
import {BusinessModule} from "./home/<USER>/business/business.module";
import {BorrowerModule} from "./home/<USER>/borrower/borrower.module";
import {HrModule} from "./home/<USER>/hr/hr.module";

@NgModule({
  declarations: [
    AppComponent,
    routeParams
  ],
  imports: [
    CommonModule,
    BrowserAnimationsModule,
    BrowserModule,
    AppRoutingModule,
    CoreModule,
    AdminModule,
    BorrowerModule,
    HrModule,
    ReportModule,
    BusinessModule,
    ModalModule.forRoot()
  ],
  bootstrap: [AppComponent],
})

export class AppModule {
}














