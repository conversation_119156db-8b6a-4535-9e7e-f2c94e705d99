import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { BorrowerReportData } from '../components/borrower-report/borrower-report.component';
import { CoreApiConstants } from '../../../core/core-constants';

@Injectable({
  providedIn: 'root'
})
export class BorrowerReportService {

  constructor(private http: HttpClient) { }

  getBorrowerReport(): Observable<BorrowerReportData[]> {
    return this.http.get<BorrowerReportData[]>(CoreApiConstants.API_URL + 'report/borrower');
  }

  getBorrowerReportByDateRange(startDate: string, endDate: string): Observable<BorrowerReportData[]> {
    const params = new HttpParams()
      .set('startDate', startDate)
      .set('endDate', endDate);
    return this.http.get<BorrowerReportData[]>(CoreApiConstants.API_URL + 'report/borrower/dateRange', { params });
  }

  getBorrowerReportByStatus(status: string): Observable<BorrowerReportData[]> {
    const params = new HttpParams().set('status', status);
    return this.http.get<BorrowerReportData[]>(CoreApiConstants.API_URL + 'report/borrower/status', { params });
  }

}
