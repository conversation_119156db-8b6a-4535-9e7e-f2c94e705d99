import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { IncomeReportData } from '../components/income-report/income-report.component';
import { CoreApiConstants } from '../../../core/core-constants';

@Injectable({
  providedIn: 'root'
})
export class IncomeReportService {

  constructor(private http: HttpClient) { }

  getIncomeReport(startDate: string, endDate: string, reportType: string = 'daily'): Observable<IncomeReportData[]> {
    const params = new HttpParams()
      .set('startDate', startDate)
      .set('endDate', endDate)
      .set('reportType', reportType);
    return this.http.get<IncomeReportData[]>(CoreApiConstants.API_URL + 'report/income', { params });
  }

  getMonthlyIncomeReport(year: number, month: number): Observable<IncomeReportData[]> {
    const params = new HttpParams()
      .set('year', year.toString())
      .set('month', month.toString());
    return this.http.get<IncomeReportData[]>(CoreApiConstants.API_URL + 'report/income/monthly', { params });
  }

  getYearlyIncomeReport(year: number): Observable<IncomeReportData[]> {
    const params = new HttpParams().set('year', year.toString());
    return this.http.get<IncomeReportData[]>(CoreApiConstants.API_URL + 'report/income/yearly', { params });
  }

}
