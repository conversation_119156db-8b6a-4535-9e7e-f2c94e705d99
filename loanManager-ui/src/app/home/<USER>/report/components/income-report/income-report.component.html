<div class="container-fluid py-4">
  <!-- Header Section -->
  <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-3">
    <h2 class="h4 mb-2 mb-md-0">
      <i class="fas fa-chart-area me-2"></i> Income Report
    </h2>
    <div>
      <button class="btn btn-outline-primary btn-sm me-2" (click)="refreshData()" [disabled]="loading">
        <i class="fas fa-sync-alt" [class.fa-spin]="loading"></i> Refresh
      </button>
      <button class="btn btn-success btn-sm" (click)="exportReport()">
        <i class="fas fa-download"></i> Export
      </button>
    </div>
  </div>
  <p class="text-muted mb-4">
    Detailed income analysis showing revenue streams, expenses, and profitability metrics.
  </p>

  <!-- Analytics Cards -->
  <div class="row g-3 mb-4">
    <div class="col-6 col-md-3">
      <div class="card text-center border-primary h-100">
        <div class="card-body">
          <i class="fas fa-dollar-sign fa-2x text-primary mb-2"></i>
          <h4 class="card-title mb-1">{{analytics.totalRevenue | number:'1.2-2'}}</h4>
          <p class="card-text text-muted mb-0">Total Revenue</p>
        </div>
      </div>
    </div>
    <div class="col-6 col-md-3">
      <div class="card text-center border-danger h-100">
        <div class="card-body">
          <i class="fas fa-minus-circle fa-2x text-danger mb-2"></i>
          <h4 class="card-title mb-1">{{analytics.totalExpenses | number:'1.2-2'}}</h4>
          <p class="card-text text-muted mb-0">Total Expenses</p>
        </div>
      </div>
    </div>
    <div class="col-6 col-md-3">
      <div class="card text-center border-success h-100">
        <div class="card-body">
          <i class="fas fa-chart-line fa-2x mb-2" [ngClass]="{'text-success': analytics.netProfit >= 0, 'text-danger': analytics.netProfit < 0}"></i>
          <h4 class="card-title mb-1" [ngClass]="{'text-success': analytics.netProfit >= 0, 'text-danger': analytics.netProfit < 0}">
            {{analytics.netProfit | number:'1.2-2'}}
          </h4>
          <p class="card-text text-muted mb-0">Net Profit</p>
        </div>
      </div>
    </div>
    <div class="col-6 col-md-3">
      <div class="card text-center border-warning h-100">
        <div class="card-body">
          <i class="fas fa-percentage fa-2x mb-2" [ngClass]="{'text-success': analytics.profitMargin >= 0, 'text-danger': analytics.profitMargin < 0}"></i>
          <h4 class="card-title mb-1" [ngClass]="{'text-success': analytics.profitMargin >= 0, 'text-danger': analytics.profitMargin < 0}">
            {{analytics.profitMargin | number:'1.1-1'}}%
          </h4>
          <p class="card-text text-muted mb-0">Profit Margin</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Additional Analytics -->
  <div class="row g-3 mb-4">
    <div class="col-md-6">
      <div class="card h-100">
        <div class="card-header d-flex align-items-center">
          <i class="fas fa-calendar-day me-2"></i>
          <h6 class="mb-0">Daily Averages</h6>
        </div>
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <span>Average Daily Income:</span>
            <span class="fw-semibold">{{analytics.averageDailyIncome | number:'1.2-2'}}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card h-100">
        <div class="card-header d-flex align-items-center">
          <i class="fas fa-trophy me-2"></i>
          <h6 class="mb-0">Performance Highlights</h6>
        </div>
        <div class="card-body">
          <div class="d-flex justify-content-between mb-2">
            <span>Highest Income Day:</span>
            <span class="fw-semibold">{{analytics.highestIncomeDay | date:'mediumDate'}}</span>
          </div>
          <div class="d-flex justify-content-between">
            <span>Lowest Income Day:</span>
            <span class="fw-semibold">{{analytics.lowestIncomeDay | date:'mediumDate'}}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters Card -->
  <div class="card mb-4">
    <div class="card-header d-flex align-items-center">
      <i class="fas fa-filter me-2"></i>
      <h6 class="mb-0">Report Filters</h6>
    </div>
    <div class="card-body">
      <form (ngSubmit)="applyFilters()" class="row g-3">
        <div class="col-md-3">
          <label for="dateFrom" class="form-label">Date From</label>
          <input
            type="date"
            id="dateFrom"
            class="form-control form-control-sm"
            [(ngModel)]="dateFrom"
            name="dateFrom"
          >
        </div>
        <div class="col-md-3">
          <label for="dateTo" class="form-label">Date To</label>
          <input
            type="date"
            id="dateTo"
            class="form-control form-control-sm"
            [(ngModel)]="dateTo"
            name="dateTo"
          >
        </div>
        <div class="col-md-3">
          <label for="reportType" class="form-label">Report Type</label>
          <select
            id="reportType"
            class="form-select form-select-sm"
            [(ngModel)]="reportType"
            name="reportType"
          >
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
          </select>
        </div>
        <div class="col-md-3 d-flex align-items-end gap-2">
          <button type="submit" class="btn btn-primary btn-sm flex-grow-1">
            <i class="fas fa-search me-1"></i> Apply
          </button>
          <button type="button" class="btn btn-outline-secondary btn-sm flex-grow-1" (click)="clearFilters()">
            <i class="fas fa-times me-1"></i> Clear
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Income Summary -->
  <div class="card mb-4">
    <div class="card-header d-flex align-items-center">
      <i class="fas fa-chart-pie me-2"></i>
      <h6 class="mb-0">Income Breakdown</h6>
    </div>
    <div class="card-body">
      <div class="row g-3">
        <div class="col-6 col-md-3">
          <div class="d-flex justify-content-between">
            <span>Loan Payments:</span>
            <span class="text-primary fw-semibold">{{getTotalLoanPayments() | number:'1.2-2'}}</span>
          </div>
        </div>
        <div class="col-6 col-md-3">
          <div class="d-flex justify-content-between">
            <span>Interest Earned:</span>
            <span class="text-success fw-semibold">{{getTotalInterestEarned() | number:'1.2-2'}}</span>
          </div>
        </div>
        <div class="col-6 col-md-3">
          <div class="d-flex justify-content-between">
            <span>Penalty Charges:</span>
            <span class="text-warning fw-semibold">{{getTotalPenaltyCharges() | number:'1.2-2'}}</span>
          </div>
        </div>
        <div class="col-6 col-md-3">
          <div class="d-flex justify-content-between">
            <span>Other Income:</span>
            <span class="text-info fw-semibold">{{getTotalOtherIncome() | number:'1.2-2'}}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Income Data Table -->
  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h6 class="mb-0">
        <i class="fas fa-table me-2"></i> Income Details
      </h6>
      <span class="badge bg-primary">{{incomeData.length}} records</span>
    </div>
    <div class="card-body p-0">
      <!-- Loading Spinner -->
      <div *ngIf="loading" class="d-flex flex-column align-items-center py-5">
        <div class="spinner-border text-primary" role="status" aria-hidden="true"></div>
        <span class="visually-hidden">Loading...</span>
        <p class="mt-3 mb-0 text-muted">Loading income report...</p>
      </div>

      <!-- Income Table -->
      <div *ngIf="!loading && incomeData.length > 0" class="table-responsive">
        <table class="table table-hover mb-0">
          <thead class="table-light">
          <tr>
            <th>Date</th>
            <th>Loan Payments</th>
            <th>Interest Earned</th>
            <th>Penalty Charges</th>
            <th>Other Income</th>
            <th>Total Income</th>
            <th>Expenses</th>
            <th>Net Income</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let item of incomeData | slice:(page-1)*pageSize:page*pageSize">
            <td>{{item.date | date:'mediumDate'}}</td>
            <td class="text-primary">{{item.loanPayments | number:'1.2-2'}}</td>
            <td class="text-success">{{item.interestEarned | number:'1.2-2'}}</td>
            <td class="text-warning">{{item.penaltyCharges | number:'1.2-2'}}</td>
            <td class="text-info">{{item.otherIncome | number:'1.2-2'}}</td>
            <td class="fw-semibold">{{item.totalIncome | number:'1.2-2'}}</td>
            <td class="text-danger">{{item.expenses | number:'1.2-2'}}</td>
            <td [ngClass]="{'text-success': item.netIncome >= 0, 'text-danger': item.netIncome < 0}" class="fw-semibold">
              {{item.netIncome | number:'1.2-2'}}
            </td>
          </tr>
          </tbody>
        </table>
      </div>

      <!-- No Data Message -->
      <div *ngIf="!loading && incomeData.length === 0" class="text-center py-5 text-muted">
        <i class="fas fa-chart-area fa-3x mb-3"></i>
        <h5>No Income Data Found</h5>
        <p>No income data available for the selected date range. Try adjusting the filters.</p>
      </div>

      <!-- Pagination -->
      <div *ngIf="!loading && incomeData.length > pageSize" class="d-flex justify-content-center my-3">
        <pagination
          [totalItems]="collectionSize"
          [(ngModel)]="page"
          [maxSize]="pageSize"
          [itemsPerPage]="pageSize"
          (pageChanged)="pageChanged($event)">
        </pagination>
      </div>
    </div>
  </div>
</div>
