import { Component, OnInit } from '@angular/core';
import { IncomeReportService } from '../../service/income-report.service';

export interface IncomeReportData {
  date: string;
  loanPayments: number;
  interestEarned: number;
  penaltyCharges: number;
  otherIncome: number;
  totalIncome: number;
  expenses: number;
  netIncome: number;
}

export interface IncomeAnalytics {
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  averageDailyIncome: number;
  highestIncomeDay: string;
  lowestIncomeDay: string;
  profitMargin: number;
}

@Component({
  standalone: false,
  selector: 'app-income-report',
  templateUrl: './income-report.component.html',
  styleUrls: ['./income-report.component.css']
})
export class IncomeReportComponent implements OnInit {

  incomeData: IncomeReportData[] = [];
  analytics: IncomeAnalytics = {
    totalRevenue: 0,
    totalExpenses: 0,
    netProfit: 0,
    averageDailyIncome: 0,
    highestIncomeDay: '',
    lowestIncomeDay: '',
    profitMargin: 0
  };

  // Filter options
  dateFrom: string = '';
  dateTo: string = '';
  reportType: string = 'daily'; // daily, weekly, monthly

  // Pagination
  page: number = 1;
  pageSize: number = 10;
  collectionSize: number = 0;

  loading: boolean = false;

  constructor(private incomeReportService: IncomeReportService) {
    // Set default date range (last 30 days)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

    this.dateTo = today.toISOString().split('T')[0];
    this.dateFrom = thirtyDaysAgo.toISOString().split('T')[0];
  }

  ngOnInit(): void {
    this.loadIncomeReport();
  }

  loadIncomeReport(): void {
    if (!this.dateFrom || !this.dateTo) {
      return;
    }

    this.loading = true;
    this.incomeReportService.getIncomeReport(this.dateFrom, this.dateTo, this.reportType).subscribe({
      next: (data: IncomeReportData[]) => {
        this.incomeData = data || [];
        this.collectionSize = this.incomeData.length;
        this.calculateAnalytics();
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading income report:', error);
        this.loading = false;
      }
    });
  }

  calculateAnalytics(): void {
    if (this.incomeData.length === 0) {
      this.analytics = {
        totalRevenue: 0,
        totalExpenses: 0,
        netProfit: 0,
        averageDailyIncome: 0,
        highestIncomeDay: '',
        lowestIncomeDay: '',
        profitMargin: 0
      };
      return;
    }

    this.analytics.totalRevenue = this.incomeData.reduce((sum, item) => sum + item.totalIncome, 0);
    this.analytics.totalExpenses = this.incomeData.reduce((sum, item) => sum + item.expenses, 0);
    this.analytics.netProfit = this.analytics.totalRevenue - this.analytics.totalExpenses;
    this.analytics.averageDailyIncome = this.analytics.totalRevenue / this.incomeData.length;

    // Find highest and lowest income days
    const sortedByIncome = [...this.incomeData].sort((a, b) => b.totalIncome - a.totalIncome);
    this.analytics.highestIncomeDay = sortedByIncome[0]?.date || '';
    this.analytics.lowestIncomeDay = sortedByIncome[sortedByIncome.length - 1]?.date || '';

    // Calculate profit margin
    this.analytics.profitMargin = this.analytics.totalRevenue > 0
      ? (this.analytics.netProfit / this.analytics.totalRevenue) * 100
      : 0;
  }

  applyFilters(): void {
    this.page = 1;
    this.loadIncomeReport();
  }

  clearFilters(): void {
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

    this.dateTo = today.toISOString().split('T')[0];
    this.dateFrom = thirtyDaysAgo.toISOString().split('T')[0];
    this.reportType = 'daily';
    this.loadIncomeReport();
  }

  exportReport(): void {
    // Implementation for exporting to CSV/Excel
    console.log('Export functionality to be implemented');
  }

  pageChanged(event: any): void {
    this.page = event.page;
  }

  refreshData(): void {
    this.loadIncomeReport();
  }

  getTotalLoanPayments(): number {
    return this.incomeData.reduce((sum, item) => sum + item.loanPayments, 0);
  }

  getTotalInterestEarned(): number {
    return this.incomeData.reduce((sum, item) => sum + item.interestEarned, 0);
  }

  getTotalPenaltyCharges(): number {
    return this.incomeData.reduce((sum, item) => sum + item.penaltyCharges, 0);
  }

  getTotalOtherIncome(): number {
    return this.incomeData.reduce((sum, item) => sum + item.otherIncome, 0);
  }
}
