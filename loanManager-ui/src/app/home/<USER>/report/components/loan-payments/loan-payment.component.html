<div class="container-fluid py-4">
  <!-- Header Section -->
  <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-3">
    <h2 class="h4 mb-2 mb-md-0">
      <i class="fas fa-file-invoice-dollar me-2"></i> Payment Report
    </h2>
    <div>
      <button class="btn btn-outline-primary btn-sm" (click)="loadTodayPayments()">
        <i class="fas fa-sync-alt"></i> Refresh
      </button>
    </div>
  </div>
  <p class="text-muted mb-4">
    View and analyze payment transactions across different date ranges.
  </p>

  <!-- Filter Section -->
  <div class="card mb-4">
    <div class="card-body">
      <form class="row g-3 align-items-end" (ngSubmit)="searchByDateRange()">
        <div class="col-md-3">
          <label for="fromDate" class="form-label">From Date</label>
          <input
            [(ngModel)]="fromDate"
            bsDatepicker
            [bsConfig]="{dateInputFormat: 'YYYY-MM-DD' }"
            autocomplete="off"
            placeholder="Select from date"
            class="form-control"
            name="fromDate"
            id="fromDate"
            type="text"
          >
        </div>
        <div class="col-md-3">
          <label for="toDate" class="form-label">To Date</label>
          <input
            [(ngModel)]="toDate"
            bsDatepicker
            [bsConfig]="{dateInputFormat: 'YYYY-MM-DD' }"
            autocomplete="off"
            placeholder="Select to date"
            class="form-control"
            name="toDate"
            id="toDate"
            type="text"
          >
        </div>
        <div class="col-md-3">
          <button type="submit" class="btn btn-primary w-100">
            <i class="fas fa-search me-1"></i> Search
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Summary Section -->
  <div *ngIf="payments.length > 0" class="mb-4">
    <div class="card border-primary">
      <div class="card-body d-flex justify-content-between align-items-center">
        <div>
          <h4 class="mb-1">{{totalAmount | number:'1.2-2'}}</h4>
          <p class="mb-0 text-muted">Total Payment Amount</p>
        </div>
        <i class="fas fa-money-bill-wave fa-2x text-primary"></i>
      </div>
    </div>
  </div>

  <!-- Payments Table -->
  <div class="card">
    <div class="card-body p-0">
      <div *ngIf="payments.length === 0" class="text-center py-5 text-muted">
        <i class="fas fa-inbox fa-3x mb-3"></i>
        <h5>No payments found</h5>
        <p>Try adjusting your date range or check back later.</p>
      </div>

      <div *ngIf="payments.length > 0" class="table-responsive">
        <table class="table table-hover mb-0">
          <thead class="table-light">
          <tr>
            <th>Date</th>
            <th>Loan No</th>
            <th>Loan Plan</th>
            <th>Borrower</th>
            <th>Payment Amount</th>
            <th>Route</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let payment of payments; let i = index">
            <td>{{payment.dateTime | date:'mediumDate'}}</td>
            <td><strong>{{payment.loanNo}}</strong></td>
            <td>{{payment.loanPlan}}</td>
            <td>{{payment.borrowerName}}</td>
            <td>{{payment.amount | number:'1.2-2'}}</td>
            <td>
              <span class="badge bg-info">{{payment.routeName}}</span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
