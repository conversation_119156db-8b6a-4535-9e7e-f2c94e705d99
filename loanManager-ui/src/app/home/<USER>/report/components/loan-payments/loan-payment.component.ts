import {Component, OnInit} from '@angular/core';
import {BsModalRef} from "ngx-bootstrap/modal";
import {Payment} from "../../../business/model/payment";
import {LoanPaymentService} from "../../../business/service/loanPaymentService";

@Component({
  standalone: false,
  selector: 'app-manage-loan',
  templateUrl: './loan-payment.component.html',
  styleUrls: ['./loan-payment.component.css']
})
export class LoanPaymentComponent implements OnInit {

  modalRef: BsModalRef;
  payments: Array<Payment> = [];

  totalAmount: number;

  fromDate: Date;
  toDate: Date;

  constructor(private paymentService: LoanPaymentService) {
  }

  ngOnInit(): void {
    this.loadTodayPayments();
  }

  loadTodayPayments() {
    this.payments = [];
    this.paymentService.findTodayPayments().subscribe((data: any) => {
      this.payments = data;
      this.calcTotalAmount();
    });
  }

  searchByDateRange() {
    this.payments = [];
    this.paymentService.findPaymentsByDateRange(this.fromDate.toLocaleDateString(),
      this.toDate.toLocaleDateString()).subscribe((data: any) => {
      if (data != null) {
        this.payments = data;
        this.calcTotalAmount();
      }
    });
  }

  calcTotalAmount() {
    this.totalAmount = 0;
    for (let payment of this.payments) {
      this.totalAmount = this.totalAmount + payment.amount;
    }
  }

}
