<div class="container-fluid py-4">
  <!-- Header Section -->
  <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-3">
    <h2 class="h4 mb-2 mb-md-0">
      <i class="fas fa-chart-bar me-2"></i> Borrower Report
    </h2>
    <div>
      <button class="btn btn-outline-primary btn-sm me-2" (click)="refreshData()" [disabled]="loading">
        <i class="fas fa-sync-alt" [class.fa-spin]="loading"></i> Refresh
      </button>
      <button class="btn btn-success btn-sm" (click)="exportReport()">
        <i class="fas fa-download"></i> Export
      </button>
    </div>
  </div>
  <p class="text-muted mb-4">
    Comprehensive report showing borrower information, loan history, and payment status.
  </p>

  <!-- Summary Cards -->
  <div class="row g-3 mb-4">
    <div class="col-6 col-md-3">
      <div class="card text-center border-primary h-100">
        <div class="card-body">
          <i class="fas fa-users fa-2x text-primary mb-2"></i>
          <h4 class="card-title mb-1">{{totalBorrowers}}</h4>
          <p class="card-text text-muted mb-0">Total Borrowers</p>
        </div>
      </div>
    </div>
    <div class="col-6 col-md-3">
      <div class="card text-center border-danger h-100">
        <div class="card-body">
          <i class="fas fa-money-bill-wave fa-2x text-danger mb-2"></i>
          <h4 class="card-title mb-1">{{totalLoanAmount | number:'1.2-2'}}</h4>
          <p class="card-text text-muted mb-0">Total Loan Amount</p>
        </div>
      </div>
    </div>
    <div class="col-6 col-md-3">
      <div class="card text-center border-success h-100">
        <div class="card-body">
          <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
          <h4 class="card-title mb-1">{{totalPaidAmount | number:'1.2-2'}}</h4>
          <p class="card-text text-muted mb-0">Total Paid</p>
        </div>
      </div>
    </div>
    <div class="col-6 col-md-3">
      <div class="card text-center border-warning h-100">
        <div class="card-body">
          <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
          <h4 class="card-title mb-1">{{totalOutstandingAmount | number:'1.2-2'}}</h4>
          <p class="card-text text-muted mb-0">Outstanding Amount</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters Card -->
  <div class="card mb-4">
    <div class="card-header d-flex align-items-center">
      <i class="fas fa-filter me-2"></i>
      <h6 class="mb-0">Filter Options</h6>
    </div>
    <div class="card-body">
      <form (ngSubmit)="applyFilters()" class="row g-3">
        <div class="col-md-3">
          <label for="searchTerm" class="form-label">Search</label>
          <input
            type="text"
            id="searchTerm"
            class="form-control form-control-sm"
            [(ngModel)]="searchTerm"
            (keyup)="applyFilters()"
            placeholder="Name, NIC, Phone..."
            name="searchTerm"
          />
        </div>
        <div class="col-md-2">
          <label for="statusFilter" class="form-label">Status</label>
          <select
            id="statusFilter"
            class="form-select form-select-sm"
            [(ngModel)]="statusFilter"
            (change)="applyFilters()"
            name="statusFilter"
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="defaulted">Defaulted</option>
          </select>
        </div>
        <div class="col-md-2">
          <label for="dateFrom" class="form-label">Date From</label>
          <input
            type="date"
            id="dateFrom"
            class="form-control form-control-sm"
            [(ngModel)]="dateFrom"
            (change)="applyFilters()"
            name="dateFrom"
          />
        </div>
        <div class="col-md-2">
          <label for="dateTo" class="form-label">Date To</label>
          <input
            type="date"
            id="dateTo"
            class="form-control form-control-sm"
            [(ngModel)]="dateTo"
            (change)="applyFilters()"
            name="dateTo"
          />
        </div>
        <div class="col-md-3 d-flex align-items-end">
          <button type="button" class="btn btn-outline-secondary btn-sm w-100" (click)="clearFilters()">
            <i class="fas fa-times me-1"></i> Clear Filters
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Borrowers Table -->
  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h6 class="mb-0">
        <i class="fas fa-table me-2"></i> Borrower Details
      </h6>
      <span class="badge bg-primary">{{filteredBorrowers.length}} borrowers</span>
    </div>
    <div class="card-body p-0">
      <!-- Loading Spinner -->
      <div *ngIf="loading" class="d-flex flex-column align-items-center py-5">
        <div class="spinner-border text-primary" role="status" aria-hidden="true"></div>
        <span class="visually-hidden">Loading...</span>
        <p class="mt-3 mb-0 text-muted">Loading borrower report...</p>
      </div>

      <!-- Borrowers Table -->
      <div *ngIf="!loading && filteredBorrowers.length > 0" class="table-responsive">
        <table class="table table-hover mb-0">
          <thead class="table-light">
          <tr>
            <th>Name</th>
            <th>NIC</th>
            <th>Contact</th>
            <th>Total Loans</th>
            <th>Loan Amount</th>
            <th>Paid Amount</th>
            <th>Outstanding</th>
            <th>Last Loan</th>
            <th>Status</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let borrower of filteredBorrowers | slice:(page-1)*pageSize:page*pageSize">
            <td>
              <strong>{{borrower.firstName}} {{borrower.lastName}}</strong><br />
              <small class="text-muted">{{borrower.address}}</small>
            </td>
            <td>{{borrower.nic}}</td>
            <td>{{borrower.phoneNumber}}</td>
            <td>
              <span class="badge bg-info">{{borrower.totalLoans}}</span>
            </td>
            <td>{{borrower.totalAmount | number:'1.2-2'}}</td>
            <td class="text-success">{{borrower.totalPaid | number:'1.2-2'}}</td>
            <td class="text-danger">{{borrower.totalOutstanding | number:'1.2-2'}}</td>
            <td>{{borrower.lastLoanDate | date:'mediumDate'}}</td>
            <td>
                <span class="badge" [ngClass]="getStatusBadgeClass(borrower.status)">
                  {{borrower.status | titlecase}}
                </span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>

      <!-- No Data Message -->
      <div *ngIf="!loading && filteredBorrowers.length === 0" class="text-center py-5 text-muted">
        <i class="fas fa-users fa-3x mb-3"></i>
        <h5>No Borrowers Found</h5>
        <p>No borrowers match your current filter criteria. Try adjusting the filters.</p>
      </div>

      <!-- Pagination -->
      <div *ngIf="!loading && filteredBorrowers.length > pageSize" class="d-flex justify-content-center my-3">
        <pagination
          [totalItems]="collectionSize"
          [(ngModel)]="page"
          [maxSize]="pageSize"
          [itemsPerPage]="pageSize"
          (pageChanged)="pageChanged($event)">
        </pagination>
      </div>
    </div>
  </div>
</div>
