import { Component, OnInit } from '@angular/core';
import { BorrowerReportService } from '../../service/borrower-report.service';

export interface BorrowerReportData {
  id: string;
  firstName: string;
  lastName: string;
  nic: string;
  phoneNumber: string;
  address: string;
  totalLoans: number;
  totalAmount: number;
  totalPaid: number;
  totalOutstanding: number;
  lastLoanDate: string;
  status: string;
}

@Component({
  standalone: false,
  selector: 'app-borrower-report',
  templateUrl: './borrower-report.component.html',
  styleUrls: ['./borrower-report.component.css']
})
export class BorrowerReportComponent implements OnInit {

  borrowers: BorrowerReportData[] = [];
  filteredBorrowers: BorrowerReportData[] = [];

  // Filter options
  searchTerm: string = '';
  statusFilter: string = '';
  dateFrom: string = '';
  dateTo: string = '';

  // Pagination
  page: number = 1;
  pageSize: number = 10;
  collectionSize: number = 0;

  // Summary data
  totalBorrowers: number = 0;
  totalLoanAmount: number = 0;
  totalPaidAmount: number = 0;
  totalOutstandingAmount: number = 0;

  loading: boolean = false;

  constructor(private borrowerReportService: BorrowerReportService) { }

  ngOnInit(): void {
    this.loadBorrowerReport();
  }

  loadBorrowerReport(): void {
    this.loading = true;
    this.borrowerReportService.getBorrowerReport().subscribe({
      next: (data: BorrowerReportData[]) => {
        this.borrowers = data || [];
        this.filteredBorrowers = [...this.borrowers];
        this.collectionSize = this.borrowers.length;
        this.calculateSummary();
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading borrower report:', error);
        this.loading = false;
      }
    });
  }

  applyFilters(): void {
    this.filteredBorrowers = this.borrowers.filter(borrower => {
      const matchesSearch = !this.searchTerm ||
        borrower.firstName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        borrower.lastName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        borrower.nic.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        borrower.phoneNumber.includes(this.searchTerm);

      const matchesStatus = !this.statusFilter || borrower.status === this.statusFilter;

      const matchesDateRange = this.checkDateRange(borrower.lastLoanDate);

      return matchesSearch && matchesStatus && matchesDateRange;
    });

    this.collectionSize = this.filteredBorrowers.length;
    this.page = 1;
    this.calculateSummary();
  }

  checkDateRange(date: string): boolean {
    if (!this.dateFrom && !this.dateTo) return true;

    const borrowerDate = new Date(date);
    const fromDate = this.dateFrom ? new Date(this.dateFrom) : null;
    const toDate = this.dateTo ? new Date(this.dateTo) : null;

    if (fromDate && borrowerDate < fromDate) return false;
    if (toDate && borrowerDate > toDate) return false;

    return true;
  }

  calculateSummary(): void {
    this.totalBorrowers = this.filteredBorrowers.length;
    this.totalLoanAmount = this.filteredBorrowers.reduce((sum, b) => sum + b.totalAmount, 0);
    this.totalPaidAmount = this.filteredBorrowers.reduce((sum, b) => sum + b.totalPaid, 0);
    this.totalOutstandingAmount = this.filteredBorrowers.reduce((sum, b) => sum + b.totalOutstanding, 0);
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.statusFilter = '';
    this.dateFrom = '';
    this.dateTo = '';
    this.applyFilters();
  }

  exportReport(): void {
    // Implementation for exporting to CSV/Excel
    console.log('Export functionality to be implemented');
  }

  getStatusBadgeClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'badge-success';
      case 'inactive':
        return 'badge-secondary';
      case 'defaulted':
        return 'badge-danger';
      default:
        return 'badge-primary';
    }
  }

  pageChanged(event: any): void {
    this.page = event.page;
  }

  refreshData(): void {
    this.loadBorrowerReport();
  }
}
