<div class="container-fluid py-4">
  <!-- Header Section -->
  <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-3">
    <h2 class="h4 mb-2 mb-md-0">
      <i class="fas fa-chart-line me-2"></i> Loan Report
    </h2>
    <div>
      <button class="btn btn-outline-primary btn-sm me-2" (click)="ngOnInit()">
        <i class="fas fa-sync-alt"></i> Refresh
      </button>
      <button class="btn btn-success btn-sm" (click)="exportReport()">
        <i class="fas fa-download"></i> Export
      </button>
    </div>
  </div>
  <p class="text-muted mb-4">
    Comprehensive loan report with filtering and analysis capabilities.
  </p>

  <!-- Filters Card -->
  <div class="card mb-4">
    <div class="card-header d-flex align-items-center">
      <i class="fas fa-filter me-2"></i>
      <h6 class="mb-0">Filter Options</h6>
    </div>
    <div class="card-body">
      <div class="row g-3">
        <div class="col-md-4">
          <label for="statusFilter" class="form-label">Filter By Status</label>
          <select
            class="form-select"
            name="loanPlan"
            id="statusFilter"
            #statusMod="ngModel"
            [(ngModel)]="selectedStatus"
            (ngModelChange)="findByStatus()"
          >
            <option value="">All Status</option>
            <option *ngFor="let status of statusList" [ngValue]="status">
              {{ status.value }}
            </option>
          </select>
        </div>
      </div>
    </div>
  </div>

  <!-- Loans Table -->
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h6 class="mb-0">
        <i class="fas fa-table me-2"></i> Loans Report
      </h6>
      <span class="badge bg-primary">{{loans?.length || 0}} loans</span>
    </div>
    <div class="card-body p-0">
      <div class="table-responsive">
        <table class="table table-hover mb-0">
          <thead class="table-light">
          <tr>
            <th>Borrower Name</th>
            <th>Loan Plan</th>
            <th>Loan Amount</th>
            <th>Balance</th>
            <th>Status</th>
            <th>Loan Date</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let loan of loans; let i = index">
            <td>
              <strong>{{ loan.borrower?.name || 'N/A' }}</strong>
              <small class="d-block text-muted" *ngIf="loan.borrower?.nic">{{ loan.borrower.nic }}</small>
            </td>
            <td>{{ loan.loanPlan?.name || 'N/A' }}</td>
            <td>{{ loan.loanAmount | number:'1.2-2' }}</td>
            <td>{{ loan.balance | number:'1.2-2' }}</td>
            <td>
                <span class="badge" [ngClass]="getStatusBadgeClass(loan.status?.value)">
                  {{ loan.status?.value || "N/A" }}
                </span>
            </td>
            <td>{{ loan.dateTime | date:'mediumDate' }}</td>
          </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <nav class="mt-3 d-flex justify-content-center">
        <pagination
          class="pagination pagination-sm"
          [totalItems]="collectionSize"
          [(ngModel)]="page"
          [maxSize]="15"
          [boundaryLinks]="true"
          (pageChanged)="pageChanged($event)"
        >
        </pagination>
      </nav>
    </div>
  </div>

  <!-- Summary Card -->
  <div class="card">
    <div class="card-header d-flex align-items-center">
      <i class="fas fa-chart-bar me-2"></i>
      <h6 class="mb-0">Loan Summary</h6>
    </div>
    <div class="card-body">
      <div class="row g-3">
        <div class="col-md-6 d-flex justify-content-between">
          <span>Total Loan Amount:</span>
          <span class="text-primary fw-semibold">{{ totalAmount | number: '1.2-2' }}</span>
        </div>
        <div class="col-md-6 d-flex justify-content-between">
          <span>Total Amount to Collect:</span>
          <span class="text-success fw-semibold">{{ totalAmountToCollect | number: '1.2-2' }}</span>
        </div>
      </div>
    </div>
  </div>
</div>
