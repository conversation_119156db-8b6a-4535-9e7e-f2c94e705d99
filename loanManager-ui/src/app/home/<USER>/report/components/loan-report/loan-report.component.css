/* Modern Container */
.modern-container {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Page Header */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
}

.page-title i {
  margin-right: 10px;
  color: #ffd700;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.header-actions .btn {
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.header-actions .btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: white;
}

.page-description {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

/* Filters Card */
.filters-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 20px;
}

.filters-header {
  background: #f8f9fa;
  padding: 15px 20px;
  border-bottom: 1px solid #dee2e6;
}

.filters-header h6 {
  margin: 0;
  font-weight: 600;
  color: #495057;
}

.filters-header i {
  margin-right: 8px;
  color: #667eea;
}

.filters-body {
  padding: 20px;
}

/* Table Card */
.table-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 20px;
}

.table-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h6 {
  margin: 0;
  font-weight: 600;
  color: #495057;
}

.table-header i {
  margin-right: 8px;
  color: #667eea;
}

.table-body {
  padding: 20px;
}

/* Summary Card */
.summary-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.summary-header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 15px 20px;
}

.summary-header h6 {
  margin: 0;
  font-weight: 600;
}

.summary-header i {
  margin-right: 8px;
}

.summary-body {
  padding: 20px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f8f9fa;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-weight: 600;
  color: #495057;
}

.summary-value {
  font-weight: 700;
  font-size: 1.1rem;
  color: #2c3e50;
}

/* Table Styles */
.table {
  margin-bottom: 0;
}

.table th {
  background-color: #f8f9fa;
  border-top: none;
  font-weight: 600;
  color: #495057;
  padding: 15px 12px;
}

.table td {
  padding: 15px 12px;
  vertical-align: middle;
}

.table-hover tbody tr:hover {
  background-color: rgba(102, 126, 234, 0.05);
}

/* Form Controls */
.form-group {
  margin-bottom: 15px;
}

.form-group label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}

.form-control {
  border-radius: 8px;
  border: 1px solid #dee2e6;
  padding: 10px 15px;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Badges */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
}

.badge-primary {
  background-color: #667eea;
}

.badge-success {
  background-color: #28a745;
}

.badge-info {
  background-color: #17a2b8;
}

.badge-warning {
  background-color: #ffc107;
  color: #212529;
}

.badge-danger {
  background-color: #dc3545;
}

.badge-secondary {
  background-color: #6c757d;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* Buttons */
.btn {
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn i {
  margin-right: 6px;
}

.btn-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.btn-outline-primary {
  border-color: #667eea;
  color: #667eea;
}

.btn-outline-primary:hover {
  background-color: #667eea;
  border-color: #667eea;
}

/* Utility Classes */
.padding-left-0 {
  padding-left: 0 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-container {
    padding: 15px;
  }

  .page-header {
    padding: 20px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .table-responsive {
    font-size: 0.875rem;
  }
}
