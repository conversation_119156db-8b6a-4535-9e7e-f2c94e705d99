import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {LoanPaymentComponent} from "./components/loan-payments/loan-payment.component";
import {LoanReportComponent} from "./components/loan-report/loan-report.component";
import {BorrowerReportComponent} from "./components/borrower-report/borrower-report.component";
import {IncomeReportComponent} from "./components/income-report/income-report.component";

const routes: Routes = [
  {
    path: 'loan',
    component: LoanReportComponent
  },
  {
    path: 'payment',
    component: LoanPaymentComponent
  },
  {
    path: 'borrower',
    component: BorrowerReportComponent
  },
  {
    path: 'income',
    component: IncomeReportComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class ReportRoutingModule {
}

export const reportRouteParams = [LoanPaymentComponent, LoanReportComponent, BorrowerReportComponent, IncomeReportComponent];
