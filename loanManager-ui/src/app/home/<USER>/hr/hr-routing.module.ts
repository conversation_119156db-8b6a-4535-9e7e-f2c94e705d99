import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {CollectorComponent} from "./components/collector/new-collector/collector.component";
import {ManageCollectorComponent} from "./components/collector/manage-collector/manage-collector.component";

const routes: Routes = [
  {
    path: 'new_collector',
    component: CollectorComponent
  },
  {
    path: 'manage_collector',
    component: ManageCollectorComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class HrRoutingModule {
}

export const hrRouteParams = [CollectorComponent, ManageCollectorComponent];

