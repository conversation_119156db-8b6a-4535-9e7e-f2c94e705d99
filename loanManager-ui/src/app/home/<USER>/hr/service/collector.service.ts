import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {HrApiConstants} from "../hr-constants";

@Injectable({
  providedIn: 'root'
})
export class CollectorService {
  constructor(private http: HttpClient) {
  }

  public save(collector, images: File[]) {
    const formData = new FormData();
    formData.append('collector', JSON.stringify(collector));
    for (let i = 0; i < images.length; i++) {
      formData.append("images", images[i]);
    }
    return this.http.post<any>(HrApiConstants.SAVE_COLLECTOR, formData);
  }

  public findAll(page, pageSize) {
    return this.http.get(HrApiConstants.GET_COLLECTORS, {params: {page: page, pageSize: pageSize}});
  }

  public delete(id) {
    return this.http.delete(HrApiConstants.DELETE_COLLECTOR, {params: {id: id}});
  }

  findAllInactive(page, pageSize) {
    return this.http.get(HrApiConstants.GET_COLLECTORS_INACTIVE, {params: {page: page, pageSize: pageSize}});
  }

  checkNicNumber(nicNumber) {
    return this.http.get(HrApiConstants.COLLECTOR_CHECK, {params: {nicNumber: nicNumber}});
  }

  public findByNameLike(name) {
    return this.http.get(HrApiConstants.SEARCH_COLLECTOR_BY_NAME_LIKE, {params: {name: name}})
  }

  findByNameLikeInactive(name) {
    return this.http.get(HrApiConstants.SEARCH_COLLECTOR_BY_NAME_LIKE_INACTIVE, {params: {name: name}})
  }

  public findByNicNumberLike(nicNumber) {
    return this.http.get(HrApiConstants.SEARCH_COLLECTOR_BY_NIC_NUMBER_LIKE, {params: {nicNumber: nicNumber}})
  }

  findByNicNumberLikeInactive(nicNumber) {
    return this.http.get(HrApiConstants.SEARCH_COLLECTOR_BY_NIC_NUMBER_LIKE_INACTIVE, {params: {nicNumber: nicNumber}})
  }

}

