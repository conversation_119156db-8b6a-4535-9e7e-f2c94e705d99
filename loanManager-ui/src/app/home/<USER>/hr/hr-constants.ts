import {environment} from '../../../../environments/environment';

export class HrApiConstants {

  public static API_URL = environment.apiUrl;

  public static SAVE_COLLECTOR = HrApiConstants.API_URL + 'collector/save';
  public static GET_COLLECTORS = HrApiConstants.API_URL + 'collector/findAll';
  public static DELETE_COLLECTOR = HrApiConstants.API_URL + 'collector/delete';
  public static SEARCH_COLLECTOR_BY_NIC_NUMBER_LIKE = HrApiConstants.API_URL + 'collector/search_by_nicNumber';
  public static SEARCH_COLLECTOR_BY_NAME_LIKE = HrApiConstants.API_URL + 'collector/search_by_name';
  public static SEARCH_COLLECTOR_BY_NAME_LIKE_INACTIVE = HrApiConstants.API_URL + 'collector/search_inactive_by_name';
  public static GET_COLLECTORS_INACTIVE = HrApiConstants.API_URL + 'collector/findAllInactive';
  public static SEARCH_COLLECTOR_BY_NIC_NUMBER_LIKE_INACTIVE = HrApiConstants.API_URL + 'collector/search_inactive_by_nicNumber';
  public static COLLECTOR_CHECK = HrApiConstants.API_URL + 'collector/checkNumber';

}
