<div class="card">
  <div class="card-header bg-primary">
    <strong>NEW COLLECTOR</strong>
  </div>

  <div class="card-body">
    <form #manageCollectorForm="ngForm" (ngSubmit)="saveCollector(); manageCollectorForm.reset()">
      <div class="row g-3">
        <div class="col-md-3">
          <label for="nicNumber" class="form-label">NIC Number</label>
          <input type="text"
                 id="nicNumber"
                 name="nicNumber"
                 class="form-control"
                 [(ngModel)]="collector.nicNumber"
                 #nic="ngModel"
                 required
                 (keyup)="checkNicNo()"
                 [class.is-invalid]="nic.invalid && nic.touched"
                 placeholder="NIC Number" />
          <div *ngIf="nic.invalid && nic.touched" class="invalid-feedback">
            NIC Number is required
          </div>
          <small *ngIf="nicNoAvailability" class="text-danger d-block mt-1">
            * This NIC Number already added
          </small>
        </div>

        <div class="col-md-6">
          <label for="dName" class="form-label">Name</label>
          <input type="text"
                 id="dName"
                 name="dName"
                 class="form-control"
                 [(ngModel)]="collector.name"
                 #dName="ngModel"
                 required
                 [class.is-invalid]="dName.invalid && dName.touched"
                 placeholder="Collector Name" />
          <div *ngIf="dName.invalid && dName.touched" class="invalid-feedback">
            Collector Name is required
          </div>
        </div>

        <div class="col-md-3">
          <label for="contactNo" class="form-label">Contact No</label>
          <input type="text"
                 id="contactNo"
                 name="contactNo"
                 class="form-control"
                 [(ngModel)]="collector.contactNo"
                 #contactNo="ngModel"
                 required
                 [class.is-invalid]="contactNo.invalid && contactNo.touched"
                 placeholder="Contact Number" />
          <div *ngIf="contactNo.invalid && contactNo.touched" class="invalid-feedback">
            Contact Number is required
          </div>
        </div>
      </div>

      <div class="row g-3 mt-2">
        <div class="col-md-9">
          <label for="address" class="form-label">Address</label>
          <input type="text"
                 id="address"
                 name="address"
                 class="form-control"
                 [(ngModel)]="collector.address"
                 placeholder="Address" />
        </div>

        <div class="col-md-3">
          <label for="routeSelect" class="form-label">Route</label>
          <select id="routeSelect"
                  name="routeSelect"
                  class="form-select"
                  [(ngModel)]="selectedRoute"
                  #routeSelect="ngModel"
                  required
                  (ngModelChange)="setSelectedRout()">
            <option value="" disabled selected>Select Route No</option>
            <option *ngFor="let type of routeList" [ngValue]="type">{{ type.name }}</option>
          </select>
        </div>
      </div>

      <div class="row mt-3">
        <div class="col-12">
          <label class="form-label">Upload Photos</label>
          <ngx-dropzone (change)="onSelect($event)" [maxFileSize]="2e+6">
            <ngx-dropzone-label>
              <div class="text-center p-4">
                <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                <p class="mb-1">Drop images here or click to browse</p>
                <small class="text-muted">Maximum file size: 2MB</small>
              </div>
            </ngx-dropzone-label>

            <ngx-dropzone-image-preview
              *ngFor="let f of files"
              [file]="f"
              [removable]="true"
              (removed)="onRemove($event)">
              <ngx-dropzone-label>{{ f.name }} ({{ f.type }})</ngx-dropzone-label>
            </ngx-dropzone-image-preview>
          </ngx-dropzone>
        </div>
      </div>

      <div class="row mt-3">
        <div class="col-12">
          <div class="form-check">
            <input class="form-check-input"
                   type="checkbox"
                   id="activeCheck"
                   name="activeCheck"
                   [(ngModel)]="collector.active" />
            <label class="form-check-label" for="activeCheck">Active</label>
          </div>
        </div>
      </div>

      <div class="row mt-3">
        <div class="col text-end">
          <button type="button" class="btn btn-warning me-2" (click)="clear()">Clear</button>
          <button type="submit" class="btn btn-success"
                  [disabled]="!manageCollectorForm.form.valid || selectedCollector != null">Save
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
