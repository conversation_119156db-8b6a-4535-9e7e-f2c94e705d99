import {Component, OnInit} from '@angular/core';
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {Collector} from "../../../model/collector";
import {MetaData} from "../../../../../core/model/metaData";
import {CollectorService} from "../../../service/collector.service";
import {MetaDataService} from "../../../../../core/service/metaData.service";
import {NotificationService} from "../../../../../core/service/notification.service";
import {CollectorComponent} from "../new-collector/collector.component";

@Component({
  standalone: false,
  selector: 'app-manage-collector',
  templateUrl: './manage-collector.component.html',
  styleUrls: ['./manage-collector.component.css']
})
export class ManageCollectorComponent implements OnInit {
  isCollectorSelected: boolean;
  collector: Collector;
  keyCollectorSearch: string;
  keyNicNumberSearch: string;
  CollectorSearchList: Array<Collector>;
  inActiveFilter: boolean;
  selectedRow: number;
  collectionSize;
  page;
  pageSize;
  active: boolean;
  selectedCollector: Collector;
  modalRef: BsModalRef;
  modalRefViewImages: BsModalRef;
  licenseID: string;
  collectorCount: number;

  constructor(
    private modalService: BsModalService,
    private collectorService: CollectorService,
    private metaDataService: MetaDataService,
    private notificationService: NotificationService
  ) {
  }

  ngOnInit(): void {
    this.collector = new Collector();
    this.selectedCollector = new Collector();
    this.selectedCollector = null;
    this.selectedRow = null;
    this.isCollectorSelected = false;
    this.page = 1;
    this.pageSize = 10;
    this.findAll();
    this.inActiveFilter = false;
  }

  findAll() {
    this.collectorService.findAll(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.CollectorSearchList = data.content;
      this.collectionSize = data.totalPages * this.pageSize;
      this.collectorCount = data.totalElements;
    });
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findCollectors();
  }

  loadNicNumber() {
    if (!this.inActiveFilter) {
      this.collectorService.findByNicNumberLike(this.keyNicNumberSearch).subscribe((data: Array<Collector>) => {
        return this.CollectorSearchList = data;
      });
    } else {
      this.collectorService.findByNicNumberLikeInactive(this.keyNicNumberSearch).subscribe((data: Array<Collector>) => {
        return this.CollectorSearchList = data;
      });
    }
  }

  setSelectedNicNumber(event) {
    this.collector = event.item;
  }

  loadCollectors() {
    if (!this.inActiveFilter) {
      this.collectorService.findByNameLike(this.keyCollectorSearch).subscribe((data: Array<Collector>) => {
        return this.CollectorSearchList = data;
      });
    } else {
      this.collectorService.findByNameLikeInactive(this.keyCollectorSearch).subscribe((data: Array<Collector>) => {
        return this.CollectorSearchList = data;
      });
    }
  }

  setSelectedCollector(event) {
    this.collector = event.item;
  }

  findCollectors() {
    if (!this.inActiveFilter) {
      this.findAll();
    } else {
      this.findAllInActives();
    }
  }

  findAllInActives() {
    this.collectorService.findAllInactive(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.CollectorSearchList = data.content;
      this.collectionSize = data.totalPages * this.pageSize;
    });
  }

  showAll() {
    this.findAll();
  }

  collectorDetail(collector: Collector, index) {
    this.selectedCollector = collector;
    this.collector = this.selectedCollector;
    this.selectedRow = index;
  }

  showDetails() {
    this.modalRef = this.modalService.show(CollectorComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalRef.content.collector = this.selectedCollector;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalRef.content.loadFiles();
    this.modalRef.content.isCollectorSelected = true;
    this.modalService.onHide.subscribe(() => {
      this.findAll();
    });
  }

  decline(): void {
    this.modalRef.hide();
    this.ngOnInit();
  }

}
