import {Component, OnInit} from '@angular/core';
import {Collector} from "../../../model/collector";
import {BsModalRef} from "ngx-bootstrap/modal";
import {NotificationService} from "../../../../../core/service/notification.service";
import {CollectorService} from "../../../service/collector.service";
import {MetaData} from "../../../../../core/model/metaData";
import {MetaDataService} from "../../../../../core/service/metaData.service";
import {FileService} from "../../../../../core/service/file.service";
import {Route} from "../../../../business/model/route";
import {RouteService} from "../../../../business/service/route.service";

@Component({
  standalone: false,
  selector: 'app-collector',
  templateUrl: './collector.component.html',
  styleUrls: ['./collector.component.css']
})
export class CollectorComponent implements OnInit {
  collector: Collector;
  keyCollectorSearch: string;
  selectedRow: number;
  active: boolean;
  nicNoAvailability = false;
  selectedCollector: Collector;
  modalRef: BsModalRef;
  selectedRoute: Route;
  routeList: Array<Route> = [];
  files: File[] = [];

  constructor(private collectorService: CollectorService,
              private notificationService: NotificationService,
              private metaDataService: MetaDataService,
              private routeService: RouteService,
              private fileService: FileService) {
  }

  ngOnInit(): void {
    this.collector = new Collector();
    this.collector.active = true;
    this.active = true;
    this.selectedCollector = null;
    this.selectedRow = null;
    this.loadRoute();
  }

  saveCollector() {
    this.collectorService.save(this.collector, this.files).subscribe(result => {
      if (result.code === 200) {
        this.notificationService.showSuccess(result.message);
        this.ngOnInit();
      } else {
        this.notificationService.showSuccess(result.code);
      }
    });
  }

  clear() {
    this.ngOnInit();
    this.keyCollectorSearch = '';
  }

  setSelectedRout() {
    this.collector.route = new Route();
    this.collector.route.id = this.selectedRoute.id;
  }

  loadRoute() {
    this.routeService.findAllForSelect().subscribe((data: Array<Route>) => {
      return this.routeList = data;
    });
  }

  checkNicNo() {
    this.collectorService.checkNicNumber(this.collector.nicNumber).subscribe((res: boolean) => {
      this.nicNoAvailability = res;
    });
  }

  onSelect(event) {
    this.files.push(...event.addedFiles);
  }

  onRemove(event) {
    this.files.splice(this.files.indexOf(event), 1);
  }

  loadFiles() {
    for (let url of this.collector.imageUrls) {
      this.fileService.getFile(url).subscribe((result: File) => {
        this.files.push(result);
      });
    }
  }

}
