<div class="container-fluid py-4">
  <!-- Header Section -->
  <div class="mb-4">
    <div class="d-flex justify-content-between align-items-center mb-2">
      <h2 class="h4 fw-bold">
        <i class="fas fa-users-cog me-2"></i>
        Manage Collectors
      </h2>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-primary btn-sm" (click)="ngOnInit()">
          <i class="fas fa-sync-alt"></i> Refresh
        </button>
        <button class="btn btn-success btn-sm" routerLink="../new_collector">
          <i class="fas fa-user-plus"></i> New Collector
        </button>
      </div>
    </div>
    <p class="text-muted mb-0">
      Search, view, and manage all loan collectors in the system.
    </p>
  </div>

  <!-- Search Card -->
  <div class="card shadow-sm">
    <div class="card-header">
      <h6 class="mb-0">
        <i class="fas fa-search me-2"></i> Search Collectors
      </h6>
    </div>
    <div class="card-body">
      <div class="row g-3 align-items-center mb-4">
        <div class="col-md-4">
          <input [(ngModel)]="keyNicNumberSearch"
                 [typeahead]="CollectorSearchList"
                 (typeaheadLoading)="loadNicNumber()"
                 (typeaheadOnSelect)="setSelectedNicNumber($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadOptionField="nicNumber"
                 placeholder="Search NIC Number"
                 autocomplete="off"
                 required
                 class="form-control"
                 name="collector" />
        </div>

        <div class="col-md-4">
          <input [(ngModel)]="keyCollectorSearch"
                 [typeahead]="CollectorSearchList"
                 (typeaheadLoading)="loadCollectors()"
                 (typeaheadOnSelect)="setSelectedCollector($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadOptionField="name"
                 placeholder="Search By Name"
                 autocomplete="off"
                 required
                 class="form-control"
                 name="collector" />
        </div>

        <div class="col-md-2 d-flex align-items-center">
          <div class="form-check ms-auto">
            <input class="form-check-input" type="checkbox" id="check1" name="check1"
                   [(ngModel)]="inActiveFilter" (change)="findCollectors()">
            <label class="form-check-label" for="check1">Inactive collectors</label>
          </div>
        </div>

        <div class="col-md-2 d-flex justify-content-end">
          <button type="button" class="btn btn-info">View All</button>
        </div>
      </div>

      <div class="table-responsive">
        <table class="table table-striped table-bordered align-middle text-center mb-4">
          <thead>
          <tr>
            <th>NIC Number</th>
            <th>Name</th>
            <th>Address</th>
            <th>Contact No</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let collector of CollectorSearchList; let i = index"
              (click)="collectorDetail(collector, i)"
              [class.table-active]="i === selectedRow"
              style="cursor:pointer;">
            <td>{{ collector.nicNumber || 'N/A' }}</td>
            <td>{{ collector.name || 'N/A' }}</td>
            <td>{{ collector.address || 'N/A' }}</td>
            <td>{{ collector.contactNo || 'N/A' }}</td>
          </tr>
          </tbody>
        </table>
      </div>

      <div class="d-flex justify-content-center mb-3">
        <pagination
          [totalItems]="collectionSize"
          [(ngModel)]="page"
          [maxSize]="pageSize"
          [itemsPerPage]="pageSize"
          (pageChanged)="pageChanged($event)">
        </pagination>
      </div>

      <div class="d-flex justify-content-between align-items-center">
        <p class="mb-0 fw-semibold">Total Collectors: {{ collectorCount }}</p>
        <button class="btn btn-primary" (click)="showDetails()" [disabled]="!selectedCollector">
          Update
        </button>
      </div>
    </div>
  </div>
</div>
