import {NgModule} from '@angular/core';
import {Routes, RouterModule} from '@angular/router';
import {CreateUserComponent} from './component/create-user/create-user.component';
import {UserManagementComponent} from './component/user-management/user-management.component';
import {ManageUserPermissionsComponent} from './component/user-permissions/manage-user-permissions.component';
import {BusinessInfoComponent} from "./component/business-info/business.component";
import {SettingsManagementComponent} from "./component/settings-management/settings-management.component";

const routes: Routes = [
  {path: 'new_user', component: CreateUserComponent},
  {path: 'manage_users', component: UserManagementComponent},
  {path: 'user_permissions', component: ManageUserPermissionsComponent},
  {path: 'company_detail', component: BusinessInfoComponent},
  {path: 'settings', component: SettingsManagementComponent},
  ];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AdminRoutingModule {
}

export const adminRouteParams = [CreateUserComponent, UserManagementComponent, ManageUserPermissionsComponent,
  BusinessInfoComponent, SettingsManagementComponent];
