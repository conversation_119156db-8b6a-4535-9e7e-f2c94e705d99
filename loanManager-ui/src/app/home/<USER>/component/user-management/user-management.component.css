/* User Management Container */
.user-management-container {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Header Section */
.page-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
}

.page-title i {
  margin-right: 10px;
  color: #ffd700;
}

.header-actions .btn {
  margin-left: 10px;
}

.page-description {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

/* Content Card */
.content-card {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

/* Search Section */
.search-section {
  margin-bottom: 30px;
}

.search-section .input-group {
  max-width: 400px;
}

/* Table Section */
.table-section {
  margin-bottom: 30px;
}

.table {
  margin-bottom: 0;
}

.table th {
  background-color: #f8f9fa;
  border-top: none;
  font-weight: 600;
  color: #495057;
  padding: 15px;
}

.table td {
  padding: 15px;
  vertical-align: middle;
}

.clickable-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clickable-row:hover {
  background-color: #f8f9fa;
}

.table-active {
  background-color: #e3f2fd !important;
}

/* Action Section */
.action-section {
  border-top: 1px solid #dee2e6;
  padding-top: 20px;
}

.action-section .btn {
  padding: 10px 20px;
  font-weight: 500;
}

.action-section .btn i {
  margin-right: 5px;
}

/* Badge Styling */
.badge {
  font-size: 12px;
  padding: 6px 12px;
}

.badge-primary {
  background-color: #007bff;
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-management-container {
    padding: 15px;
  }

  .page-header {
    padding: 20px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .page-title {
    font-size: 24px;
    margin-bottom: 15px;
  }

  .content-card {
    padding: 20px;
  }

  .search-section .input-group {
    max-width: 100%;
  }

  .search-section .row .col-md-2 {
    margin-top: 10px;
  }
}

/* Legacy active row styling for compatibility */
.table tr.active td {
  background-color: #e3f2fd !important;
  color: #495057;
}
