<div class="container-fluid py-4">

  <!-- <PERSON> Header -->
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h4 class="mb-0">
      <i class="bi bi-people-fill me-2"></i>
      User Management
    </h4>
    <button class="btn btn-outline-primary btn-sm" (click)="findAllUsers()">
      <i class="bi bi-arrow-clockwise me-1"></i> Refresh
    </button>
  </div>
  <p class="text-muted mb-4">Manage user accounts and permissions for the loan management system.</p>

  <!-- Search Section -->
  <div class="row g-2 align-items-center mb-4">
    <div class="col-md-6">
      <div class="input-group">
        <span class="input-group-text bg-white">
          <i class="bi bi-search"></i>
        </span>
        <input type="text" [(ngModel)]="search" class="form-control" id="search" placeholder="Enter username">
      </div>
    </div>
    <div class="col-md-2">
      <button type="button" class="btn btn-primary w-100" (click)="searchUser()">
        <i class="bi bi-search me-1"></i> Search
      </button>
    </div>
  </div>

  <!-- Users Table -->
  <div class="table-responsive mb-4">
    <table class="table table-hover align-middle">
      <thead class="table-light">
      <tr>
        <th>Full Name</th>
        <th>Username</th>
        <th>Email</th>
        <th>User Role</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let user of users; let i = index"
          (click)="userDetail(user, i)"
          [class.table-active]="i === selectedRow"
          class="cursor-pointer">
        <td><strong>{{ user.firstName + ' ' + user.lastName }}</strong></td>
        <td>{{ user.username }}</td>
        <td>{{ user.email }}</td>
        <td>
            <span class="badge bg-primary me-1" *ngFor="let role of user.userRoles">
              {{ role.name }}
            </span>
        </td>
      </tr>
      </tbody>
    </table>
  </div>

  <!-- Action Button -->
  <div class="text-end">
    <button class="btn btn-primary" (click)="editUser(templateEditUser)" [disabled]="!isUserSelected">
      <i class="bi bi-pencil-square me-1"></i> Edit User
    </button>
  </div>
</div>

<!-- Modal Template -->
<ng-template #templateEditUser>
  <div class="modal-header">
    <h5 class="modal-title">Edit User</h5>
    <button type="button" class="btn-close" aria-label="Close" (click)="modalRef.hide()"></button>
  </div>
  <div class="modal-body">
    <app-create-user></app-create-user>
  </div>
</ng-template>
