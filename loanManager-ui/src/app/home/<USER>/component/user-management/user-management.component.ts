import {Component, OnInit, TemplateRef} from '@angular/core';
import {User} from '../../model/user';
import {Role} from '../../model/role';
import {NotificationService} from '../../../core/service/notification.service';
import {UserService} from '../../service/user.service';
import {RoleService} from '../../service/role.service';
import {BsModalRef, ModalOptions} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';
import {Permission} from '../../model/permission';
import {PermissionService} from '../../service/permission.service';
import {Module} from '../../model/module';
import {CreateUserComponent} from "../create-user/create-user.component";

@Component({
  standalone: false,
  selector: 'app-user',
  templateUrl: './user-management.component.html',
  styleUrls: ['./user-management.component.css']
})
export class UserManagementComponent implements OnInit {

  search: any;
  user = new User();
  users: Array<User> = [];
  userRoles: Array<Role> = [];
  confirmPassword: string;
  selectedRow: number;
  setClickedRow: Function;
  modalRef: BsModalRef;
  availablePerms: Array<Permission> = [];
  selectedPerms: Array<Permission> = [];
  modules: Array<Module> = [];
  selectedUser: User;
  isUserSelected: boolean;

  ngOnInit() {
    this.user = new User();
    this.selectedUser = new User();
    this.user.userRoles = [];
    this.isUserSelected = false;
    this.findAllUsers();
    this.findAllRole();
    this.getEnabledModulesForUser();
  }

  constructor(private userService: UserService, private notificationService: NotificationService,
              private roleService: RoleService, private permService: PermissionService,
              private modalService: BsModalService) {
  }

  getEnabledModulesForUser() {
    this.permService.getEnabledModules(this.user.username).subscribe((result: Array<Module>) => {
      this.modules = result;
    });
  }

  findAllUsers() {
    this.userService.findAll().subscribe((data: Array<User>) => {
      this.users = data;
    });
  }

  findAllRole() {
    this.roleService.findAll().subscribe((data: Array<Role>) => {
      this.userRoles = data;
    });
  }

  selectRole(item: Role) {
    let available = false;
    for (const i of this.user.userRoles) {
      if (i.id === item.id) {
        available = true;
      }
    }
    if (!available) {
      this.user.userRoles.push(item);
    }
  }

  userDetail(selectedItem: User, index) {
    this.selectedRow = index;
    this.selectedUser = selectedItem;
    this.isUserSelected = true;
    this.user.password = 'NOCHNG';
    this.confirmPassword = 'NOCHNG';
  }

  searchUser() {
    this.users = [];
    this.userService.searchByName(this.search).subscribe((data: User) => {
      this.users.push(data);
    });
  }

  editUser(templateEditUser: TemplateRef<any>) {
    this.modalRef = this.modalService.show(CreateUserComponent, <ModalOptions>{class: 'modal-lg'});
    this.modalRef.content.user = this.selectedUser;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalRef.content.user.password = 'NOCHNG';
    this.modalRef.content.confirmPassword = 'NOCHNG';
    this.modalRef.content.isPasswordMatch = true;
    this.modalRef.content.permissions = this.selectedUser.permissions;
    this.modalService.onHide.subscribe(() => {
      this.ngOnInit();
    });
  }
}

