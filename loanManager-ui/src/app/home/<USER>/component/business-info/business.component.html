<div class="card shadow-sm border-0">
  <div class="card-header bg-primary text-white">
    <h5 class="mb-0">Company Details</h5>
  </div>
  <div class="card-body">
    <form #companyForm="ngForm" (ngSubmit)="saveCompany(); companyForm.reset()">
      <div class="row g-3">
        <div class="col-md-6">
          <label for="company_name" class="form-label">Company Name</label>
          <input
            required
            #company_name="ngModel"
            type="text"
            id="company_name"
            name="company_name"
            [(ngModel)]="company.name"
            class="form-control"
            [class.is-invalid]="company_name.invalid && company_name.touched"
            placeholder="Enter Company Name"
          />
          <div class="invalid-feedback">*Company Name is required</div>
        </div>

        <div class="col-md-6">
          <label for="company_slogan" class="form-label">Company Slogan (Optional)</label>
          <input
            type="text"
            id="company_slogan"
            name="company_slogan"
            [(ngModel)]="company.slogan"
            class="form-control"
            placeholder="Enter Company Slogan"
          />
        </div>

        <div class="col-md-4">
          <label for="company_contact_1" class="form-label">Contact 1</label>
          <input
            required
            #company_contact_1="ngModel"
            type="text"
            id="company_contact_1"
            name="company_contact_1"
            [(ngModel)]="company.telephone1"
            pattern="^\d{10}$"
            class="form-control"
            [class.is-invalid]="company_contact_1.invalid && company_contact_1.touched"
            placeholder="Enter Contact 1"
          />
          <div class="invalid-feedback">*Contact 1 is required</div>
        </div>

        <div class="col-md-4">
          <label for="company_contact_2" class="form-label">Contact 2</label>
          <input
            type="text"
            id="company_contact_2"
            name="company_contact_2"
            [(ngModel)]="company.telephone2"
            pattern="^\d{10}$"
            class="form-control"
            #company_contact_2="ngModel"
            placeholder="Enter Contact 2"
          />
        </div>

        <div class="col-md-4">
          <label for="company_contact_3" class="form-label">Contact 3</label>
          <input
            type="text"
            id="company_contact_3"
            name="company_contact_3"
            [(ngModel)]="company.telephone3"
            pattern="^\d{10}$"
            class="form-control"
            #company_contact_3="ngModel"
            placeholder="Enter Contact 3"
          />
        </div>

        <div class="col-md-12">
          <label for="address2" class="form-label">Address</label>
          <input
            type="text"
            id="address2"
            name="address2"
            #address2="ngModel"
            [(ngModel)]="company.fullAddress"
            class="form-control"
            [class.is-invalid]="address2.invalid && address2.touched"
            placeholder="Enter Address Line 2"
          />
          <div class="invalid-feedback">*Address is required</div>
        </div>

        <div class="col-md-6">
          <label for="company_email" class="form-label">Company Email</label>
          <input
            #company_email="ngModel"
            type="text"
            id="company_email"
            name="company_email"
            [(ngModel)]="company.email"
            class="form-control"
            pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$"
            [class.is-invalid]="company_email.invalid && company_email.touched"
            placeholder="Enter Company Email"
          />
          <div class="invalid-feedback">*Company Email is required</div>
        </div>

        <div class="col-md-6">
          <label for="regNo" class="form-label">Company Register No</label>
          <input
            #regNo="ngModel"
            type="text"
            id="regNo"
            name="regNo"
            [(ngModel)]="company.regNo"
            class="form-control"
            [class.is-invalid]="regNo.invalid && regNo.touched"
            placeholder="Enter Register No"
          />
          <div class="invalid-feedback">*Register no is required</div>
        </div>

        <div class="col-md-6">
          <label class="form-label">Company Logo</label>
          <input
            type="file"
            class="form-control"
            #logo
            (change)="getFiles($event)"
            accept="image/*"
            multiple
          />
          <div class="mt-2">
            <img [src]="imageFile" width="220px" height="100px" class="img-thumbnail" />
          </div>
        </div>

        <div class="col-12 text-end">
          <button type="submit" [disabled]="!companyForm.form.valid" class="btn btn-success mt-3">
            <i class="fa fa-dot-circle-o"></i> Save
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
