import {Component, OnInit, ViewChild} from '@angular/core';
import {DomSanitizer} from "@angular/platform-browser";
import {Company} from "../../../core/model/company";
import {CompanyService} from "../../../core/service/company.service";
import {NotificationService} from "../../../core/service/notification.service";

@Component({
  standalone: false,
  selector: 'app-company',
  templateUrl: './business.component.html'
})

export class BusinessInfoComponent implements OnInit {

  @ViewChild('logo', {static: true}) logo;
  company: Company;
  image: string;
  files: any;
  imageFile: any;

  constructor(private companyService: CompanyService, private notificationService: NotificationService,
              private sanitizer: DomSanitizer) {
  }

  ngOnInit() {
    this.company = new Company();
    this.findCompany();
  }

  getFiles(event: any) {

    if (event.target.files && event.target.files[0]) {
      this.files = event.target.files;
      var reader = new FileReader();

      reader.readAsDataURL(event.target.files[0]); // read file as data url

      reader.onload = (event: any) => { // called once readAsDataURL is completed
        this.imageFile = event.target.result;
        reader.onload = this._handleReaderLoaded.bind(this);
        reader.readAsBinaryString(this.files[0]);
      }
    }
  }

  _handleReaderLoaded(readerEvt) {
    let binaryString = readerEvt.target.result;
    this.image = btoa(binaryString);
  }

  saveCompany() {
    this.companyService.save(this.company).subscribe(result => {
      this.saveLogo();
      this.notificationService.showSuccess(result);
      this.ngOnInit();
    });
  }

  saveLogo() {
    this.companyService.saveLogo(this.image).subscribe(result => {
      this.notificationService.showSuccess(result);
      this.ngOnInit();
    });
  }

  findCompany() {
    this.companyService.findCompany().subscribe((data: Company) => {
      this.company = data;
      this.imageFile = this.sanitizer.bypassSecurityTrustResourceUrl('data:image/jpg;base64,'
        + this.company.logo);
    });
  }

}

