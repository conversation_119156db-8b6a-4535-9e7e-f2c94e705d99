<div class="card shadow-sm border-0">
  <div class="card-header bg-primary text-white">
    <h5 class="mb-0">User Registration</h5>
  </div>
  <div class="card-body">
    <form (ngSubmit)="saveUser(); userForm.reset()" #userForm="ngForm" class="needs-validation" novalidate>
      <div class="row g-3">
        <div class="col-md-6">
          <label class="form-label">User Role</label>
          <select class="form-select" name="userRole" [(ngModel)]="userRole" (change)="selectRole(userRole)">
            <option value="" disabled selected>Select a role</option>
            <option *ngFor="let ur of userRoles" [ngValue]="ur">{{ ur.name }}</option>
          </select>
          <tag-input
            [(ngModel)]="user.userRoles"
            [identifyBy]="'id'"
            [displayBy]="'name'"
            name="userRoles"
            [hideForm]="true"
            [placeholder]="'Selected roles'"
            (onRemove)="removeFromUserRoles($event)">
          </tag-input>
        </div>

        <div class="col-md-6">
          <label for="firstName" class="form-label">First Name <span class="text-danger">*</span></label>
          <input
            type="text"
            required
            #firstName="ngModel"
            class="form-control"
            id="firstName"
            name="firstName"
            [(ngModel)]="user.firstName"
            [class.is-invalid]="firstName.invalid && firstName.touched"
            placeholder="Enter first name" />
          <div class="invalid-feedback">First Name is required</div>
        </div>

        <div class="col-md-6">
          <label for="lastName" class="form-label">Last Name <span class="text-danger">*</span></label>
          <input
            type="text"
            required
            #lastName="ngModel"
            class="form-control"
            id="lastName"
            name="lastName"
            [(ngModel)]="user.lastName"
            [class.is-invalid]="lastName.invalid && lastName.touched"
            placeholder="Enter last name" />
          <div class="invalid-feedback">Last Name is required</div>
        </div>

        <div class="col-md-6">
          <label for="userName" class="form-label">Username <span class="text-danger">*</span></label>
          <input
            type="text"
            required
            #userName="ngModel"
            class="form-control"
            id="userName"
            name="userName"
            [(ngModel)]="user.username"
            (keyup)="checkUserName()"
            placeholder="Enter username"
            [class.is-invalid]="(userName.invalid && userName.touched) || userAvailability" />
          <div class="invalid-feedback">Username is required</div>
          <div *ngIf="userAvailability" class="text-danger small mt-1">
            This username is already taken
          </div>
        </div>

        <div class="col-md-6">
          <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
          <input
            type="email"
            required
            #email="ngModel"
            class="form-control"
            id="email"
            name="email"
            [(ngModel)]="user.email"
            placeholder="Enter email address"
            pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$"
            [class.is-invalid]="email.invalid && email.touched" />
          <div class="invalid-feedback">Please enter a valid email address</div>
        </div>

        <div class="col-md-6">
          <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
          <input
            type="password"
            required
            #password="ngModel"
            class="form-control"
            id="password"
            name="password"
            [(ngModel)]="user.password"
            minlength="6"
            maxlength="6"
            size="6"
            placeholder="Enter 6 character password"
            [class.is-invalid]="password.invalid && password.touched" />
          <div class="invalid-feedback">Password must be exactly 6 characters</div>
          <div class="form-text">Password must be exactly 6 characters</div>
        </div>

        <div class="col-md-6">
          <label for="confirmPassword" class="form-label">Confirm Password <span class="text-danger">*</span></label>
          <input
            type="password"
            class="form-control"
            id="confirmPassword"
            name="confirmPassword"
            maxlength="6"
            [(ngModel)]="confirmPassword"
            placeholder="Re-enter password"
            required
            (ngModelChange)="checkPassword()"
            [class.is-invalid]="user.password !== confirmPassword && confirmPassword" />
          <div class="invalid-feedback">Passwords do not match</div>
        </div>

        <div class="col-md-6">
          <label class="form-label">Modules</label>
          <select class="form-select" name="moduleList" [(ngModel)]="selectedModule" (change)="findPermsForModule()">
            <option value="" disabled selected>Select a module</option>
            <option *ngFor="let mod of modules" [ngValue]="mod">{{ mod.name }}</option>
          </select>
        </div>

        <div class="col-md-5">
          <label class="form-label">Permissions</label>
          <select class="form-select" name="permissionList" [(ngModel)]="selectedPermission">
            <option value="" disabled selected>Select a permission</option>
            <option *ngFor="let perm of selectedPermissions" [ngValue]="perm">{{ perm.name }}</option>
          </select>
        </div>

        <div class="col-md-2 d-flex align-items-end">
          <button
            type="button"
            class="btn btn-success w-100"
            (click)="addPermissions(selectedPermission)"
            [disabled]="!selectedPermission">
            Add
          </button>
        </div>

        <div class="col-md-5 d-flex align-items-end">
          <div class="form-check form-switch">
            <input
              class="form-check-input"
              type="checkbox"
              id="active"
              name="active"
              [(ngModel)]="user.active" />
            <label class="form-check-label" for="active">Active User</label>
          </div>
        </div>

        <div class="col-md-12">
          <label class="form-label">Assigned Permissions</label>
          <tag-input
            [(ngModel)]="permissions"
            [identifyBy]="'id'"
            [displayBy]="'name'"
            name="modules"
            [hideForm]="true"
            [placeholder]="'Selected permissions'">
          </tag-input>
        </div>
      </div>

      <div class="d-flex justify-content-end mt-4">
        <button type="button" class="btn btn-secondary me-2" (click)="clearForm()">Clear</button>
        <button
          type="submit"
          class="btn btn-primary"
          [disabled]="!userForm.form.valid || !isPasswordMatch">
          Save User
        </button>
      </div>
    </form>
  </div>
</div>
