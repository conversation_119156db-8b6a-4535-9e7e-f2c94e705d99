# Application Settings Management

This is the **single, comprehensive interface** for managing all application settings in the Loan Manager system. All authorized users can access and modify settings through this unified component.

## Features

### 🎯 **Core Functionality**
- **View All Settings**: Display all active application settings in a clean table format
- **Real-time Updates**: Update settings instantly with immediate feedback
- **Search & Filter**: Find specific settings by key or description
- **Type-aware Inputs**: Different input types based on setting data type (Boolean, String, Integer, Double)

### 🔧 **Setting Types Supported**
- **BOOLEAN**: Toggle switches for true/false values
- **STRING**: Text input fields for string values
- **INTEGER**: Number inputs for whole numbers
- **DOUBLE**: Number inputs with decimal support

### 📋 **Current Settings Available**
- **USE_NEXT_BUSINESS_DAY**: Controls whether loan due dates skip weekends and holidays

## Usage

### Accessing the Component
1. Navigate to the Admin section in the application
2. Look for "Settings Management" in the navigation menu
3. The component will automatically load all active settings

### Updating Settings
1. Locate the setting you want to modify
2. For boolean settings: Use the toggle switch
3. For text/number settings: Edit the value in the input field
4. Click the "Update" button to save changes
5. Success/error messages will appear at the top of the page

### Search Functionality
- Use the search box to filter settings by key or description
- Search is case-insensitive and searches both setting keys and descriptions

## Technical Details

### Components Structure
```
settings-management/
├── settings-management.component.ts    # Main component logic
├── settings-management.component.html  # Template with Bootstrap styling
├── settings-management.component.css   # Custom styles
└── README.md                          # This documentation
```

### Dependencies
- **Angular**: Core framework
- **Bootstrap**: UI styling and components
- **Font Awesome**: Icons
- **RxJS**: Reactive programming for HTTP calls

### API Integration
The component integrates with the following backend endpoints:
- `GET /settings/findAllActive` - Retrieve all active settings
- `PUT /settings/updateSetting` - Update a specific setting
- `POST /settings/createDefaults` - Initialize default settings

### Models
```typescript
interface Settings {
  id?: string;
  settingKey: string;
  settingValue: string;
  description: string;
  dataType: string; // 'STRING' | 'BOOLEAN' | 'INTEGER' | 'DOUBLE'
  active: boolean;
}
```

## Styling

The component uses Bootstrap 5 classes with custom CSS enhancements:
- **Responsive Design**: Works on all screen sizes
- **Modern UI**: Clean cards with subtle shadows and hover effects
- **Color Coding**: Different badge colors for different setting types
- **Interactive Elements**: Smooth transitions and hover states

## Error Handling

- **Network Errors**: Displays user-friendly error messages
- **Validation**: Prevents invalid data entry
- **Loading States**: Shows spinners during API calls
- **Auto-dismiss**: Success/error messages automatically disappear

## Future Enhancements

Potential improvements for future versions:
- **Setting Categories**: Group settings by functionality
- **Setting History**: Track changes over time
- **Bulk Operations**: Update multiple settings at once
- **Setting Validation**: Custom validation rules per setting
- **Import/Export**: Backup and restore settings configurations

## Related Components

- **Holiday Management**: Manages holidays that affect business day calculations (located in Business module)
- **User Management**: Admin functions for user accounts
- **Loan Management**: Where the settings take effect (loan due date calculations)
