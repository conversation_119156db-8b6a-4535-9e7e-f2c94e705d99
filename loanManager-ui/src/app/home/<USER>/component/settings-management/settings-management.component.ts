import { Component, OnInit } from '@angular/core';
import { Settings } from '../../model/settings';
import { SettingsService } from '../../service/settings.service';

@Component({
  standalone: false,
  selector: 'app-settings-management',
  templateUrl: './settings-management.component.html',
  styleUrls: ['./settings-management.component.css']
})
export class SettingsManagementComponent implements OnInit {

  settings: Settings[] = [];
  filteredSettings: Settings[] = [];
  searchTerm: string = '';
  loading: boolean = false;
  error: string = '';
  success: string = '';

  // Setting display configurations
  settingDisplayConfig = {
    'SKIP_HOLIDAYS_FOR_DUE_DATE': {
      name: 'Skip Holidays for Due Dates',
      description: 'When enabled, loan due dates that fall on holidays will be moved to the next working day',
      help: 'This only affects holidays configured in Holiday Management. Weekends are not skipped.'
    }
  };

  constructor(private settingsService: SettingsService) { }

  ngOnInit(): void {
    this.loadSettings();
    this.createDefaultSettings();
  }

  loadSettings(): void {
    this.loading = true;
    this.error = '';

    this.settingsService.findAllActive().subscribe({
      next: (data) => {
        this.settings = data || [];
        this.filteredSettings = [...this.settings];
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading settings:', error);
        this.error = 'Failed to load settings. Please try again.';
        this.loading = false;
      }
    });
  }

  createDefaultSettings(): void {
    this.settingsService.createDefaultSettings().subscribe({
      next: (response) => {
        console.log('Default settings created:', response);
      },
      error: (error) => {
        console.error('Error creating default settings:', error);
      }
    });
  }

  filterSettings(): void {
    if (!this.searchTerm.trim()) {
      this.filteredSettings = [...this.settings];
    } else {
      const term = this.searchTerm.toLowerCase();
      this.filteredSettings = this.settings.filter(setting =>
        setting.settingKey.toLowerCase().includes(term) ||
        setting.description.toLowerCase().includes(term)
      );
    }
  }

  updateSetting(setting: Settings): void {
    this.loading = true;
    this.error = '';
    this.success = '';

    this.settingsService.updateSetting(setting.settingKey, setting.settingValue).subscribe({
      next: (response) => {
        const settingName = this.getSettingDisplayName(setting.settingKey);
        this.success = `${settingName} updated successfully!`;
        this.loading = false;
        setTimeout(() => this.success = '', 3000);
      },
      error: (error) => {
        console.error('Error updating setting:', error);
        this.error = 'Failed to update setting. Please try again.';
        this.loading = false;
        // Revert the setting value on error
        setting.settingValue = setting.settingValue === 'true' ? 'false' : 'true';
        setTimeout(() => this.error = '', 5000);
      }
    });
  }

  saveSetting(setting: Settings): void {
    this.loading = true;
    this.error = '';
    this.success = '';

    this.settingsService.save(setting).subscribe({
      next: (response) => {
        this.success = 'Setting saved successfully!';
        this.loadSettings(); // Reload the list
        this.loading = false;
        setTimeout(() => this.success = '', 3000);
      },
      error: (error) => {
        console.error('Error saving setting:', error);
        this.error = 'Failed to save setting. Please try again.';
        this.loading = false;
        setTimeout(() => this.error = '', 5000);
      }
    });
  }

  getBooleanValue(value: string): boolean {
    return value.toLowerCase() === 'true';
  }

  setBooleanValue(setting: Settings, value: boolean): void {
    setting.settingValue = value.toString();
  }

  getDisplayValue(setting: Settings): string {
    if (setting.dataType === 'BOOLEAN') {
      return setting.settingValue.toLowerCase() === 'true' ? 'Enabled' : 'Disabled';
    }
    return setting.settingValue;
  }

  getSettingDisplayName(settingKey: string): string {
    return this.settingDisplayConfig[settingKey]?.name || settingKey;
  }

  getSettingDescription(settingKey: string): string {
    return this.settingDisplayConfig[settingKey]?.description || 'No description available';
  }

  getSettingHelp(settingKey: string): string {
    return this.settingDisplayConfig[settingKey]?.help || 'No additional help available';
  }
}
