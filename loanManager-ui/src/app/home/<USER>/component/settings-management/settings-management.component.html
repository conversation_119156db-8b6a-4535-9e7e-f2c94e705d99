<div class="container-fluid">
  <!-- Page Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h2 class="mb-1">
            <i class="fas fa-cogs me-2 text-primary"></i>
            Application Settings
          </h2>
          <p class="text-muted mb-0">Configure loan management system settings and preferences</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Alert Messages -->
  <div class="row mb-3" *ngIf="error || success">
    <div class="col-12">
      <div class="alert alert-danger alert-dismissible fade show" *ngIf="error">
        <i class="fas fa-exclamation-triangle me-2"></i>
        {{ error }}
        <button type="button" class="btn-close" (click)="error = ''"></button>
      </div>
      <div class="alert alert-success alert-dismissible fade show" *ngIf="success">
        <i class="fas fa-check-circle me-2"></i>
        {{ success }}
        <button type="button" class="btn-close" (click)="success = ''"></button>
      </div>
    </div>
  </div>

  <!-- Search and Filter -->
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="input-group">
        <span class="input-group-text">
          <i class="fas fa-search"></i>
        </span>
        <input
          type="text"
          class="form-control"
          placeholder="Search settings by key or description..."
          [(ngModel)]="searchTerm"
          (input)="filterSettings()"
        />
      </div>
    </div>
  </div>

  <!-- Settings List -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm border-0">
        <div class="card-header bg-primary text-white d-flex align-items-center">
          <i class="fas fa-list me-2"></i>
          <h5 class="mb-0">Application Settings</h5>
          <span class="badge bg-light text-dark ms-auto">{{ filteredSettings.length }} settings</span>
        </div>
        <div class="card-body p-0">
          <!-- Loading Spinner -->
          <div class="text-center p-4" *ngIf="loading">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading settings...</p>
          </div>

          <!-- Settings Table -->
          <div class="table-responsive" *ngIf="!loading">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th scope="col" class="fw-semibold">Setting</th>
                  <th scope="col" class="fw-semibold">Description</th>
                  <th scope="col" class="fw-semibold">Status</th>
                  <th scope="col" class="fw-semibold text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let setting of filteredSettings; let i = index">
                  <td>
                    <div class="fw-semibold">{{ getSettingDisplayName(setting.settingKey) }}</div>
                  </td>
                  <td>
                    <div class="text-muted">{{ getSettingDescription(setting.settingKey) }}</div>
                  </td>
                  <td>
                    <!-- Boolean Toggle for all settings -->
                    <div class="form-check form-switch">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        [checked]="getBooleanValue(setting.settingValue)"
                        (change)="setBooleanValue(setting, $event.target.checked); updateSetting(setting)"
                        [id]="'switch-' + setting.id"
                        [disabled]="loading"
                      />
                      <label class="form-check-label fw-semibold" [for]="'switch-' + setting.id">
                        <span class="badge" [ngClass]="getBooleanValue(setting.settingValue) ? 'bg-success' : 'bg-secondary'">
                          {{ getBooleanValue(setting.settingValue) ? 'Enabled' : 'Disabled' }}
                        </span>
                      </label>
                    </div>
                  </td>
                  <td class="text-center">
                    <i class="fas fa-info-circle text-info"
                       [title]="getSettingHelp(setting.settingKey)"
                       data-bs-toggle="tooltip"></i>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Empty State -->
            <div class="text-center p-4" *ngIf="filteredSettings.length === 0 && !loading">
              <i class="fas fa-cogs fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">No Settings Found</h5>
              <p class="text-muted">
                <span *ngIf="searchTerm">No settings match your search criteria.</span>
                <span *ngIf="!searchTerm">No settings are currently configured.</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Settings Info -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-info">
        <div class="card-header bg-info text-white">
          <i class="fas fa-info-circle me-2"></i>
          Quick Settings Guide
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6 class="fw-bold">Due Date Calculation Options:</h6>
              <ul class="list-unstyled">
                <li><i class="fas fa-calendar-alt text-secondary me-2"></i><strong>Disabled (Default):</strong> Due date = Today + Payment Frequency Days</li>
                <li><i class="fas fa-calendar-check text-success me-2"></i><strong>Enabled:</strong> Due date = Next working day (tomorrow, skipping holidays)</li>
                <li><i class="fas fa-info-circle text-info me-2"></i>Auto payment scheduling always uses loan plan frequency + skip holidays</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h6 class="fw-bold">Examples:</h6>
              <ul class="list-unstyled">
                <li><span class="badge bg-secondary me-2">Disabled</span> Weekly loan → Due in 7 days</li>
                <li><span class="badge bg-secondary me-2">Disabled</span> Monthly loan → Due in 30 days</li>
                <li><span class="badge bg-success me-2">Enabled</span> Any loan → Due tomorrow (skip holidays)</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
