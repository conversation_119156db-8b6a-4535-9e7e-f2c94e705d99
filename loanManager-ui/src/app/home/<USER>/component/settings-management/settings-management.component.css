.card {
  border-radius: 10px;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
  background-color: #f8f9fa;
}

.table td {
  vertical-align: middle;
  border-color: #e9ecef;
}

.form-check-input:checked {
  background-color: #28a745;
  border-color: #28a745;
}

.form-control-sm {
  font-size: 0.875rem;
}

.badge {
  font-size: 0.75rem;
  font-weight: 500;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.alert {
  border-radius: 8px;
  border: none;
}

.input-group-text {
  background-color: #f8f9fa;
  border-color: #ced4da;
}

.spinner-border {
  width: 2rem;
  height: 2rem;
}

code {
  background-color: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.form-switch .form-check-input {
  width: 2em;
  height: 1em;
}

.form-switch .form-check-input:focus {
  box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);
}

.table-responsive {
  border-radius: 0 0 10px 10px;
}

.card-header {
  border-radius: 10px 10px 0 0 !important;
}

.text-primary {
  color: #007bff !important;
}

.bg-primary {
  background-color: #007bff !important;
}

.border-info {
  border-color: #17a2b8 !important;
}

.bg-info {
  background-color: #17a2b8 !important;
}
