import {Component, EventEmitter} from '@angular/core';
import {NotificationService} from '../../../core/service/notification.service';
import {PermissionService} from '../../service/permission.service';
import {Module} from '../../model/module';
import {Permission} from '../../model/permission';
import {BsModalRef} from "ngx-bootstrap/modal";
import {User} from "../../model/user";

@Component({
  standalone: false,
  selector: 'app-manage-user-permissions',
  templateUrl: './manage-user-permissions.component.html',
  styleUrls: ['./manage-user-permissions.component.css']
})

export class ManageUserPermissionsComponent {

  modules: Array<Module> = [];
  availablePerms: Array<Permission> = [];
  desktopPerms: Array<Permission> = [];
  selectedPerm: Permission = new Permission();
  module = new Module();
  modalRef: BsModalRef;
  user: User;

  public event: EventEmitter<any> = new EventEmitter();

  constructor(private permService: PermissionService, private notificationService: NotificationService) {
  }

  ngOnInit() {
    this.user = JSON.parse(localStorage.getItem('currentUser'));
    this.getAllModulesForUser();
  }

  getAllModulesForUser() {
    this.permService.getEnabledModules(this.user.username).subscribe((result: Array<Module>) => {
      this.modules = result;
    })
  }

  getPermsForModule(module: Module) {
    this.permService.findPermsByModule(this.module.id).subscribe((result: Array<Permission>) => {
      this.availablePerms = result;
    })
  }

  addToDesktop(perm) {
    if (null != perm.id) {
      this.desktopPerms.push(perm);
    } else {
      this.notificationService.showError('Please Select a Permission')
    }
  }

  close() {
    this.event.emit(this.desktopPerms);
    this.modalRef.hide();
  }
}
