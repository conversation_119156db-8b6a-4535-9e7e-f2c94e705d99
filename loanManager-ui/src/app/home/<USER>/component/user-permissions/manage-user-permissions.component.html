<div class="card">
  <div class="card-body">
    <div class="row g-3">
      <!-- Module Dropdown -->
      <div class="col-md-4">
        <label for="enableModules" class="form-label">Enabled Modules</label>
        <select
          id="enableModules"
          class="form-select"
          name="module"
          [(ngModel)]="module"
          (change)="getPermsForModule(module)"
        >
          <option value="" disabled selected>Select Module</option>
          <option *ngFor="let module of modules" [ngValue]="module">{{ module.name }}</option>
        </select>
      </div>

      <!-- Permissions Dropdown -->
      <div class="col-md-4">
        <label for="addedPerms" class="form-label">Enabled Permissions</label>
        <select
          id="addedPerms"
          class="form-select"
          [(ngModel)]="selectedPerm"
        >
          <option value="" disabled selected>Select Permission</option>
          <option *ngFor="let perm of availablePerms" [ngValue]="perm">{{ perm.name }}</option>
        </select>
      </div>

      <!-- Add Button -->
      <div class="col-md-4 d-flex align-items-end">
        <button
          class="btn btn-success w-100"
          (click)="addToDesktop(selectedPerm)"
          [disabled]="!selectedPerm"
        >
          Add
        </button>
      </div>
    </div>

    <!-- Desktop Permissions Tags -->
    <div class="row mt-4">
      <div class="col-12">
        <h5 class="mb-2">Current Desktop Permissions</h5>
        <tag-input
          [(ngModel)]="desktopPerms"
          [identifyBy]="'id'"
          [displayBy]="'name'"
          theme="dark"
          [editable]="false"
          [allowDupes]="false"
        ></tag-input>
      </div>
    </div>

    <!-- Done Button -->
    <div class="row mt-4">
      <div class="col-12 text-end">
        <button
          class="btn btn-primary"
          (click)="close()">
          Done
        </button>
      </div>
    </div>
  </div>
</div>
