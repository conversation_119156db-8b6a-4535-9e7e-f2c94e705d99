<div class="min-vh-100 d-flex align-items-center justify-content-center">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-md-6 col-lg-4">
        <div class="card shadow">
          <div class="card-header bg-primary text-white text-center py-4">
            <div class="mb-3">
              <i class="fas fa-coins fa-3x text-warning"></i>
            </div>
            <h4 class="mb-1">Smart Loan Manager</h4>
            <small class="opacity-75">Professional Loan Management</small>
          </div>

          <div class="card-body p-4">
            <form #loginForm="ngForm" (ngSubmit)="login()">
              <div class="text-center mb-4">
                <h5 class="mb-1">Welcome Back!</h5>
                <p class="text-muted small">Sign in to continue</p>
              </div>

              <div class="mb-3">
                <label for="username" class="form-label">Username</label>
                <div class="input-group">
                  <span class="input-group-text"><i class="fas fa-user"></i></span>
                  <input
                    type="text"
                    id="username"
                    class="form-control"
                    placeholder="Enter username"
                    autocomplete="username"
                    [(ngModel)]="username"
                    name="username"
                    required>
                </div>
              </div>

              <div class="mb-4">
                <label for="password" class="form-label">Password</label>
                <div class="input-group">
                  <span class="input-group-text"><i class="fas fa-lock"></i></span>
                  <input
                    type="password"
                    id="password"
                    class="form-control"
                    placeholder="Enter password"
                    autocomplete="current-password"
                    [(ngModel)]="password"
                    required
                    name="password">
                </div>
              </div>

              <div class="d-grid">
                <button
                  type="submit"
                  class="btn btn-primary"
                  [disabled]="!loginForm.form.valid || isLoading">
                  <i class="fas fa-sign-in-alt me-2" *ngIf="!isLoading"></i>
                  <i class="fas fa-spinner fa-spin me-2" *ngIf="isLoading"></i>
                  {{isLoading ? 'Signing In...' : 'Sign In'}}
                </button>
              </div>
            </form>
          </div>

          <div class="card-footer text-center text-muted py-3">
            <small>Powered by <strong>viganana.com</strong> | Version 24.01</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
