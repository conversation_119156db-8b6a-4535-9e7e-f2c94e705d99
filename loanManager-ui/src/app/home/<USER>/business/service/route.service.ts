import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {BusinessConstants} from "../business-constants";

@Injectable({
  providedIn: 'root'
})

export class RouteService {

  constructor (private http: HttpClient) {
  }

  save (brand) {
    return this.http.post<any>(BusinessConstants.SAVE_ROUTE, brand);
  }

  public findAll (page, pageSize) {
    return this.http.get(BusinessConstants.GET_ROUTES, {params: {page: page, pageSize: pageSize}});

  }

  public findAllForSelect() {
    return this.http.get(BusinessConstants.FIND_ALL_ROUTES_FOR_SELECT);
  }

  public findByName (name) {
    return this.http.get(BusinessConstants.SEARCH_ROUTE, {params: {name: name}});
  }

}
