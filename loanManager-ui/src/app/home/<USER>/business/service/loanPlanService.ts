import {Injectable} from "@angular/core";
import {HttpClient} from "@angular/common/http";
import {BusinessConstants} from "../business-constants";
import {LoanPlan} from "../model/loanPlan";

@Injectable({
  providedIn: 'root'
})
export class LoanPlanService {

  constructor(private http: HttpClient) {
  }

  public findAllLoanPlan() {
    return this.http.get(BusinessConstants.FIND_ALL_LOAN_PLAN);
  }

  public findPlansByStatus(status: boolean) {
    return this.http.get(BusinessConstants.FIND_LOAN_PLAN_BY_STATUS, {params: {status: status}});
  }

  public save(loanPlan: LoanPlan) {
    return this.http.post(BusinessConstants.SAVE_LOAN_PLAN, loanPlan);
  }

}
