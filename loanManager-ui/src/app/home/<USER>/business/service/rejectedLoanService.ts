import {Injectable} from "@angular/core";
import {HttpClient} from "@angular/common/http";
import {RejectedLoan} from "../model/rejectedLoan";
import {BusinessConstants} from "../business-constants";

@Injectable({
  providedIn: 'root'
})
export class RejectedLoanService {

  constructor(private http: HttpClient) {
  }

  public getAllRejectedLoans(page: number, pageSize: number) {
    return this.http.get<any>(BusinessConstants.GET_REJECTED_LOANS, {
      params: {
        page: page.toString(),
        size: pageSize.toString()
      }
    });
  }

  public searchByCustomerName(customerName: string) {
    return this.http.get<RejectedLoan[]>(BusinessConstants.SEARCH_REJECTED_LOANS, {
      params: {
        customerName: customerName
      }
    });
  }

  public searchByCustomerNic(customerNic: string) {
    return this.http.get<RejectedLoan[]>(BusinessConstants.SEARCH_REJECTED_LOANS, {
      params: {
        customerNic: customerNic
      }
    });
  }

  public searchByTelephone(telephone: string) {
    return this.http.get<RejectedLoan[]>(BusinessConstants.SEARCH_REJECTED_LOANS, {
      params: {
        telephone: telephone
      }
    });
  }
}
