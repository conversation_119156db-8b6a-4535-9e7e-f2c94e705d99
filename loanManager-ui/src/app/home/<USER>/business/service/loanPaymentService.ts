import {Injectable} from "@angular/core";
import {HttpClient} from "@angular/common/http";
import {BusinessConstants} from "../business-constants";

@Injectable({
  providedIn: 'root'
})
export class LoanPaymentService {

  constructor(private http: HttpClient) {

  }

  public findTodayPayments(): any {
    return this.http.get(BusinessConstants.FIND_TODAY_PAYMENT_LIST);
  }

  public findPaymentsByDate(date: string): any {
    return this.http.get(BusinessConstants.FIND_PAYMENTS_BY_DATE, {params: {date: date}});
  }

  public findPaymentsByDateRange(fromDate: string, toDate: string): any {
    return this.http.get(BusinessConstants.FIND_PAYMENTS_BY_DATE_RANGE, {params: {fromDate: fromDate, toDate: toDate}});
  }

}
