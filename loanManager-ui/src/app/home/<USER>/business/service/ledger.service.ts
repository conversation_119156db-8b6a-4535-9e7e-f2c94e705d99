import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Ledger } from '../component/ledger/ledger.component';
import { CoreApiConstants } from '../../../core/core-constants';

@Injectable({
  providedIn: 'root'
})
export class LedgerService {

  constructor(private http: HttpClient) { }

  findLedger(appNo: string): Observable<Ledger> {
    const params = new HttpParams().set('appNo', appNo);
    return this.http.get<Ledger>(CoreApiConstants.API_URL + 'mobileApp/getLedgerByAppNo', { params });
  }

  correctLedger(appNo: string, amount: number): Observable<boolean> {
    const params = new HttpParams()
      .set('appNo', appNo)
      .set('amount', amount.toString());

    return this.http.get<boolean>(CoreApiConstants.API_URL + 'mobileApp/correctLedger', { params });
  }

  createLedger(ledger: Ledger): Observable<Ledger> {
    // This endpoint may need to be created in the backend
    return this.http.post<Ledger>(CoreApiConstants.API_URL + 'ledger/create', ledger);
  }

  updateLedger(ledger: Ledger): Observable<Ledger> {
    // This endpoint may need to be created in the backend
    return this.http.put<Ledger>(CoreApiConstants.API_URL + 'ledger/update', ledger);
  }

  getLedgerBalance(appNo: string): Observable<number> {
    const params = new HttpParams().set('appNo', appNo);
    return this.http.get<number>(CoreApiConstants.API_URL + 'mobileApp/getLedgerByAppNo', { params });
  }
}
