import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {BusinessConstants} from "../business-constants";
import {environment} from "../../../../../environments/environment";

@Injectable({
  providedIn: 'root'
})
export class LoanRecordService {

  constructor(private http: HttpClient) {
  }

  public findTodayList(): any {
    return this.http.get(BusinessConstants.FIND_TODAY_LIST);
  }

  public findTodayLoanByName(name: string): any {
    return this.http.get(BusinessConstants.FIND_TODAY_LOAN_BY_NAME, {params: {name: name}});
  }

  public findTodayLoanByNic(nic: string): any {
    return this.http.get(BusinessConstants.FIND_TODAY_LOAN_BY_NIC, {params: {nic: nic}});
  }

  public findRecordsByLoanNo(loanNo: string): any {
    return this.http.get(BusinessConstants.FIND_ALL_RECORDS_BY_LOAN_NO, {params: {loanNo: loanNo}});
  }

  public findPendingRecordsByLoanNo(loanNo: string): any {
    return this.http.get(BusinessConstants.FIND_PENDING_LOAN_RECORDS_BY_LOAN_NO, {params: {loanNo: loanNo}});
  }

  public findPendingRecordsByNic(nic: string): any {
    return this.http.get(BusinessConstants.FIND_PENDING_LOAN_RECORDS_BY_NIC, {params: {nic: nic}});
  }

  public findPendingRecordsByTp1(tp1: string): any {
    return this.http.get(BusinessConstants.FIND_PENDING_LOAN_RECORDS_BY_TP_1, {params: {tp1: tp1}});
  }

  public findArrearsList(): any {
    return this.http.get(BusinessConstants.FIND_ARREARS_LIST);
  }

  public findArrearsLoanByName(name: string): any {
    return this.http.get(BusinessConstants.FIND_ARREARS_LOAN_BY_NAME, {params: {name: name}});
  }

  public pay(loanRecordId: string, payment: number, appNo: string = "web"): any {
    return this.http.get(BusinessConstants.PAY_LOAN_RECORD, {
      params: {
        loanRecordId: loanRecordId,
        payment: payment,
        appNo: appNo
      }
    });
  }

  public createAndPay(loanNo: string, payment: number, appNo: string = "web"): any {
    return this.http.get(BusinessConstants.CREATE_AND_PAY_LOAN_RECORD, {
      params: {
        loanNo: loanNo,
        payment: payment,
        appNo: appNo
      }
    });
  }

  public removeUnpaidRecordsForDate(date: string): any {
    return this.http.delete(BusinessConstants.REMOVE_UNPAID_RECORDS_FOR_DATE, {
      params: {
        date: date
      }
    });
  }

}
