import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { LedgerRecord } from '../component/ledger/ledger.component';
import { CoreApiConstants } from '../../../core/core-constants';

@Injectable({
  providedIn: 'root'
})
export class LedgerRecordService {

  constructor(private http: HttpClient) { }

  findAll(page: number = 0, size: number = 10): Observable<any> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    return this.http.get(CoreApiConstants.API_URL + 'ledgerRecord/findAll', { params });
  }

  findByType(appNo: string, type: string, dateFrom: string, dateTo: string): Observable<LedgerRecord[]> {
    const params = new HttpParams()
      .set('appNo', appNo)
      .set('fromDate', dateFrom)
      .set('toDate', dateTo);

    // If no type is specified, use the general date range endpoint
    if (!type || !type.trim()) {
      return this.http.get<LedgerRecord[]>(CoreApiConstants.API_URL + 'mobileApp/findLedgerRecByDateAndAppNo', { params });
    }

    // If type is specified, use the type-specific endpoint
    const paramsWithType = params.set('type', type);
    return this.http.get<LedgerRecord[]>(CoreApiConstants.API_URL + 'mobileApp/getLedgerByTypeDateAndAppNo', { params: paramsWithType });
  }

  findByDate(appNo: string, date: string): Observable<LedgerRecord[]> {
    const params = new HttpParams()
      .set('appNo', appNo)
      .set('fromDate', date)
      .set('toDate', date);

    return this.http.get<LedgerRecord[]>(CoreApiConstants.API_URL + 'mobileApp/findLedgerRecByDateAndAppNo', { params });
  }

  findByDateRange(appNo: string, startDate: string, endDate: string): Observable<LedgerRecord[]> {
    const params = new HttpParams()
      .set('appNo', appNo)
      .set('startDate', startDate)
      .set('endDate', endDate);

    return this.http.get<LedgerRecord[]>(CoreApiConstants.API_URL + 'ledgerRecord/findByDateRange', { params });
  }

  findByTransactionType(appNo: string, transactionType: string): Observable<LedgerRecord[]> {
    const params = new HttpParams()
      .set('appNo', appNo)
      .set('transactionType', transactionType);

    return this.http.get<LedgerRecord[]>(CoreApiConstants.API_URL + 'ledgerRecord/findByTransactionType', { params });
  }

  save(ledgerRecord: LedgerRecord): Observable<LedgerRecord> {
    return this.http.post<LedgerRecord>(CoreApiConstants.API_URL + 'mobileApp/saveLedgerRec', ledgerRecord);
  }

  update(ledgerRecord: LedgerRecord): Observable<LedgerRecord> {
    return this.http.put<LedgerRecord>(CoreApiConstants.API_URL + 'ledgerRecord/update', ledgerRecord);
  }

  delete(id: string): Observable<boolean> {
    return this.http.delete<boolean>(CoreApiConstants.API_URL + 'ledgerRecord/delete/' + id);
  }

  getTotalByType(appNo: string, type: string, transactionType: string): Observable<number> {
    const params = new HttpParams()
      .set('appNo', appNo)
      .set('type', type)
      .set('transactionType', transactionType);

    return this.http.get<number>(CoreApiConstants.API_URL + 'ledgerRecord/totalByType', { params });
  }

  getMonthlyTotal(appNo: string, year: number, month: number, transactionType: string): Observable<number> {
    const params = new HttpParams()
      .set('appNo', appNo)
      .set('year', year.toString())
      .set('month', month.toString())
      .set('transactionType', transactionType);

    return this.http.get<number>(CoreApiConstants.API_URL + 'ledgerRecord/monthlyTotal', { params });
  }
}
