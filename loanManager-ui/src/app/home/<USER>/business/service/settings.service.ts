import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BusinessConstants } from '../business-constants';

export interface Settings {
  id?: string;
  settingKey: string;
  settingValue: string;
  description: string;
  dataType: string;
  active: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class BusinessSettingsService {

  constructor(private http: HttpClient) { }

  public findAllActive(): Observable<Settings[]> {
    return this.http.get<Settings[]>(BusinessConstants.API_URL + 'settings/findAllActive');
  }

  public getValue(settingKey: string): Observable<string> {
    const params = new HttpParams().set('settingKey', settingKey);
    return this.http.get<string>(BusinessConstants.API_URL + 'settings/getValue', { params });
  }

  public updateSetting(settingKey: string, settingValue: string): Observable<any> {
    const params = new HttpParams()
      .set('settingKey', settingKey)
      .set('settingValue', settingValue);
    return this.http.put<any>(BusinessConstants.API_URL + 'settings/updateSetting', null, { params });
  }

  // Helper method to check if next business day setting is enabled
  public isNextBusinessDayEnabled(): Observable<boolean> {
    return new Observable(observer => {
      this.getValue('USE_NEXT_BUSINESS_DAY').subscribe({
        next: (value) => {
          observer.next(value === 'true');
          observer.complete();
        },
        error: (error) => {
          console.error('Error getting USE_NEXT_BUSINESS_DAY setting:', error);
          observer.next(false); // Default to false if error
          observer.complete();
        }
      });
    });
  }
}
