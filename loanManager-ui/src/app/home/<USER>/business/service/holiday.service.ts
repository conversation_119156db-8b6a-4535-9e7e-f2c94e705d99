import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Holiday } from '../component/holiday-management/holiday-management.component';
import { CoreApiConstants } from '../../../core/core-constants';

@Injectable({
  providedIn: 'root'
})
export class HolidayService {

  constructor(private http: HttpClient) { }

  findAll(page: number = 0, size: number = 10): Observable<any> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    return this.http.get(CoreApiConstants.API_URL + 'holiday/findAll', { params });
  }

  findByName(name: string): Observable<Holiday[]> {
    const params = new HttpParams().set('name', name);
    return this.http.get<Holiday[]>(CoreApiConstants.API_URL + 'holiday/findByName', { params });
  }

  findByYear(year: number): Observable<Holiday[]> {
    const params = new HttpParams().set('year', year.toString());
    return this.http.get<Holiday[]>(CoreApiConstants.API_URL + 'holiday/findByYear', { params });
  }

  findActiveHolidays(): Observable<Holiday[]> {
    return this.http.get<Holiday[]>(CoreApiConstants.API_URL + 'holiday/findActive');
  }

  findHolidaysByDateRange(startDate: string, endDate: string): Observable<Holiday[]> {
    const params = new HttpParams()
      .set('startDate', startDate)
      .set('endDate', endDate);

    return this.http.get<Holiday[]>(CoreApiConstants.API_URL + 'holiday/findByDateRange', { params });
  }

  save(holiday: Holiday): Observable<Holiday> {
    return this.http.post<Holiday>(CoreApiConstants.API_URL + 'holiday/save', holiday);
  }

  update(holiday: Holiday): Observable<Holiday> {
    return this.http.put<Holiday>(CoreApiConstants.API_URL + 'holiday/update', holiday);
  }

  delete(id: string): Observable<boolean> {
    return this.http.delete<boolean>(CoreApiConstants.API_URL + 'holiday/delete/' + id);
  }

  isHoliday(date: string): Observable<boolean> {
    const params = new HttpParams().set('date', date);
    return this.http.get<boolean>(CoreApiConstants.API_URL + 'holiday/isHoliday', { params });
  }

  getUpcomingHolidays(days: number = 30): Observable<Holiday[]> {
    const params = new HttpParams().set('days', days.toString());
    return this.http.get<Holiday[]>(CoreApiConstants.API_URL + 'holiday/upcoming', { params });
  }

  toggleStatus(id: string): Observable<Holiday> {
    return this.http.patch<Holiday>(CoreApiConstants.API_URL + 'holiday/toggleStatus/' + id, {});
  }
}
