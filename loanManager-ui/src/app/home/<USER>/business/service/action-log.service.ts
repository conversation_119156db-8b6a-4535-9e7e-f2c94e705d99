import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BusinessConstants } from '../business-constants';

export interface ActionLog {
  id?: string;
  action: string;
  description: string;
  performedBy: string;
  performedAt: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ActionLogService {

  constructor(private http: HttpClient) { }

  logAction(actionLog: ActionLog): Observable<any> {
    return this.http.post(BusinessConstants.LOG_ACTION, actionLog);
  }

  getActionLogs(page: number = 0, pageSize: number = 20): Observable<any> {
    return this.http.get(BusinessConstants.GET_ACTION_LOGS, {
      params: {
        page: page.toString(),
        size: pageSize.toString()
      }
    });
  }

  getActionLogsByUser(username: string, page: number = 0, pageSize: number = 20): Observable<any> {
    return this.http.get(BusinessConstants.GET_ACTION_LOGS_BY_USER, {
      params: {
        username: username,
        page: page.toString(),
        size: pageSize.toString()
      }
    });
  }

  getActionLogsByDateRange(fromDate: string, toDate: string, page: number = 0, pageSize: number = 20): Observable<any> {
    return this.http.get(BusinessConstants.GET_ACTION_LOGS_BY_DATE_RANGE, {
      params: {
        fromDate: fromDate,
        toDate: toDate,
        page: page.toString(),
        size: pageSize.toString()
      }
    });
  }

  getActionLogsByAction(action: string, page: number = 0, pageSize: number = 20): Observable<any> {
    return this.http.get(BusinessConstants.GET_ACTION_LOGS_BY_ACTION, {
      params: {
        action: action,
        page: page.toString(),
        size: pageSize.toString()
      }
    });
  }
}
