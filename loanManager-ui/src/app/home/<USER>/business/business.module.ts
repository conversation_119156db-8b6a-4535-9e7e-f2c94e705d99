import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {CoreModule} from '../../core/core.module';
import {businessRouteParams,  BusinessRoutingModule} from "./business-routing.module";
import {FormsModule} from "@angular/forms";
import {CollapseModule} from "ngx-bootstrap/collapse";
import {AccordionModule} from "ngx-bootstrap/accordion";
import {ModalModule} from "ngx-bootstrap/modal";

@NgModule({
    declarations: [businessRouteParams],
    exports: [

    ],
  imports: [
    FormsModule,
    CommonModule,
    BusinessRoutingModule,
    CoreModule,
    CollapseModule.forRoot(),
    AccordionModule.forRoot(),
    ModalModule.forRoot(),
  ]
})
export class BusinessModule {

}
