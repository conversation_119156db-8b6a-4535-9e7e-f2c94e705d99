<div class="container-fluid p-4">
  <!-- Header Section -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h4 class="mb-1 fw-bold">
        <i class="fas fa-plus-circle text-primary me-2"></i>
        Create New Loan
      </h4>
      <small class="text-muted">Create a new loan for a borrower</small>
    </div>
    <button type="button" class="btn btn-outline-primary btn-sm" (click)="ngOnInit()">
      <i class="fas fa-sync-alt me-1"></i> Reset
    </button>
  </div>

  <!-- Form Card -->
  <div class="card shadow-sm">
    <div class="card-header bg-light border-bottom">
      <h6 class="mb-0 fw-semibold">
        <i class="fas fa-edit text-primary me-2"></i> Loan Details
      </h6>
    </div>
    <div class="card-body">
      <form #loanForm="ngForm" (ngSubmit)="saveLoan(loanForm);">
        <div class="row g-3">
          <div class="col-md-4">
            <label for="loanCustomer" class="form-label fw-semibold">Borrower</label>
            <div class="input-group">
              <input
                [(ngModel)]="searchKey"
                [typeahead]="customers"
                (typeaheadLoading)="loadCustomer()"
                (typeaheadOnSelect)="setSelectedCustomer($event)"
                [typeaheadOptionsLimit]="7"
                required
                #loanCustomer="ngModel"
                typeaheadOptionField="name"
                autocomplete="off"
                name="loanCustomer"
                id="loanCustomer"
                placeholder="Select the Borrower"
                class="form-control"
                [class.is-invalid]="loanCustomer.invalid && loanCustomer.touched"
              />
              <button class="btn btn-primary" type="button" (click)="openCustomerModal()">
                <i class="fa fa-plus"></i>
              </button>
            </div>
            <div class="invalid-feedback d-block" *ngIf="loanCustomer.invalid && loanCustomer.touched">
              Borrower is required
            </div>
          </div>

          <div class="col-md-4">
            <label for="loanPlan" class="form-label fw-semibold">Loan Plan</label>
            <select
              id="loanPlan"
              name="loanPlan" required
              #loanPlan="ngModel"
              class="form-select"
              [(ngModel)]="loan.loanPlan"
              [class.is-invalid]="loanPlan.invalid && loanPlan.touched">
              <option *ngFor="let lPlan of loanPlans" [ngValue]="lPlan">{{ lPlan.name }}</option>
            </select>
            <div class="invalid-feedback d-block" *ngIf="loanPlan.invalid && loanPlan.touched">
              Loan Plan is required
            </div>
          </div>

          <div class="col-md-4">
            <label for="issueDate" class="form-label fw-semibold">Issued Date</label>
            <input
              type="text"
              id="issueDate"
              name="issueDate"
              required
              #issueDate="ngModel"
              bsDatepicker
              [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
              class="form-control"
              [(ngModel)]="loan.dateTime"
              placeholder="Enter Date"
              [disabled]="isEdit"
              [class.is-invalid]="issueDate.invalid && issueDate.touched"
            />
            <div class="invalid-feedback d-block" *ngIf="issueDate.invalid && issueDate.touched">
              Date is required
            </div>
          </div>

          <div class="col-md-4">
            <label for="min" class="form-label fw-semibold">Loan Amount</label>
            <input
              type="number"
              id="min"
              name="min"
              required
              #min="ngModel"
              [(ngModel)]="loan.loanAmount"
              (ngModelChange)="setInstallment()"
              placeholder="Enter Loan Amount"
              [disabled]="isEdit"
              class="form-control"
              [class.is-invalid]="min.invalid && min.touched"
              [min]="loan.loanPlan?.minAmount || 0"
              [max]="loan.loanPlan?.maxAmount || 999999999"
            />
            <div class="invalid-feedback d-block" *ngIf="min.invalid && min.touched">
              Loan Amount is required
            </div>
            <small class="form-text text-muted" *ngIf="loan.loanPlan">
              Range: {{loan.loanPlan.minAmount | number:'1.2-2'}} - {{loan.loanPlan.maxAmount | number:'1.2-2'}}
            </small>
          </div>

          <div class="col-md-4">
            <label for="balance" class="form-label fw-semibold">Balance</label>
            <input
              type="text"
              id="balance"
              name="balance"
              [(ngModel)]="loan.balance"
              placeholder="Will be calculated automatically"
              class="form-control"
              readonly
              disabled
            />
            <small class="form-text text-muted">Balance will be calculated automatically based on loan amount and interest</small>
          </div>

          <div class="col-md-4">
            <label for="insLeft" class="form-label fw-semibold">Installment Left</label>
            <input
              type="text"
              id="insLeft"
              name="installmentLeft"
              required
              #installmentLeft="ngModel"
              [(ngModel)]="loan.installmentLeft"
              placeholder="Enter No of Installment left"
              class="form-control"
              [class.is-invalid]="installmentLeft.invalid && installmentLeft.touched"
            />
            <div class="invalid-feedback d-block" *ngIf="installmentLeft.invalid && installmentLeft.touched">
              Installment Left is required
            </div>
          </div>

          <div class="col-md-4">
            <label for="loanStatus" class="form-label fw-semibold">Status</label>
            <select
              id="loanStatus"
              name="loanStatus"
              required
              #loanStatus="ngModel"
              [(ngModel)]="loan.status"
              (ngModelChange)="onStatusChange()"
              class="form-select"
            >
              <option *ngFor="let status of statusList" [ngValue]="status">{{ status.value }}</option>
            </select>
          </div>

          <!-- Next Payment Date - Show only for Current or Arrears status -->
          <div class="col-md-4" *ngIf="shouldShowNextPaymentDate()">
            <label for="nextPaymentDate" class="form-label fw-semibold">Next Payment Date</label>
            <input
              type="text"
              id="nextPaymentDate"
              name="nextPaymentDate"
              #nextPaymentDate="ngModel"
              bsDatepicker
              [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
              class="form-control"
              [(ngModel)]="loan.nextPaymentRecordDate"
              placeholder="Select Next Payment Date"
              [class.is-invalid]="nextPaymentDate.invalid && nextPaymentDate.touched"
            />
            <div class="invalid-feedback d-block" *ngIf="nextPaymentDate.invalid && nextPaymentDate.touched">
              Next Payment Date is required for Current/Arrears status
            </div>
          </div>
        </div>

        <div class="mt-4 d-flex justify-content-end gap-2">
          <button
            type="button"
            class="btn btn-outline-secondary"
            (click)="clear()"
            [disabled]="isEdit"
          >
            <i class="fas fa-eraser me-1"></i> Clear
          </button>
          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="!loanForm.form.valid || isSubmitting"
          >
            <i class="fas fa-save me-1"></i> {{ isEdit ? 'Update' : 'Create' }} Loan
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
