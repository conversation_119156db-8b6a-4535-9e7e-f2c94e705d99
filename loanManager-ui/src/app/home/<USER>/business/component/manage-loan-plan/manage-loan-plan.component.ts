import {Component, OnInit} from '@angular/core';
import {LoanPlan} from "../../model/loanPlan";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {LoanPlanService} from "../../service/loanPlanService";
import {CreateLoanPlanComponent} from "../create-loan-plan/create-loan-plan.component";

@Component({
  standalone: false,
  selector: 'app-manage-loan-plan',
  templateUrl: './manage-loan-plan.component.html',
  styleUrls: ['./manage-loan-plan.component.css']
})
export class ManageLoanPlanComponent implements OnInit {

  modalRef: BsModalRef;
  searchKey: string;
  loanPlans: Array<LoanPlan> = [];
  selectedPlan: LoanPlan;

  active: boolean = false;

  selectedRow: number;
  page;
  collectionSize;
  pageSize;

  constructor(private loanPlanService: LoanPlanService, private modalService: BsModalService) {
  }

  ngOnInit(): void {
    this.page = 1;
    this.pageSize = 8;
    this.searchKey = null;
    this.loadLoanPlans();
  }

  loadLoanPlans() {
    this.loanPlanService.findAllLoanPlan().subscribe((data: Array<LoanPlan>) => {
      this.loanPlans = data;
    });
  }

  searchByStatus() {
    this.loanPlanService.findPlansByStatus(this.active).subscribe((data: Array<LoanPlan>) => {
      this.loanPlans = data;
    });
  }

  loanPlansDetail(plan: LoanPlan, i: number) {
    this.selectedPlan = plan;
    this.selectedRow = i;
  }

  openModal() {
    if (this.selectedPlan !== null) {
      this.modalRef = this.modalService.show(CreateLoanPlanComponent, <ModalOptions>{class: 'modal-xl'});
      this.modalRef.content.loanPlan = this.selectedPlan;
    }
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.loadLoanPlans();
  }

}
