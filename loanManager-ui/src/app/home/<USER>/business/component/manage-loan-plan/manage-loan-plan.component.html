<div class="container-fluid p-4 bg-light min-vh-100">
  <!-- Header -->
  <div class="bg-white border-start border-5 border-info rounded shadow-sm p-4 mb-4">
    <div class="d-flex justify-content-between align-items-center mb-2">
      <h2 class="h4 text-info fw-bold mb-0">
        <i class="fas fa-tasks me-2"></i> Manage Loan Plans
      </h2>
      <div class="btn-group btn-group-sm">
        <button class="btn btn-outline-info" (click)="ngOnInit()">
          <i class="fas fa-sync-alt me-1"></i> Refresh
        </button>
        <button class="btn btn-success" routerLink="../create_loan_plan">
          <i class="fas fa-plus me-1"></i> New Plan
        </button>
      </div>
    </div>
    <p class="text-muted mb-0">View and manage all loan plans with their terms, interest rates, and configurations.</p>
  </div>

  <!-- Filters Card -->
  <div class="card shadow-sm mb-4">
    <div class="card-header bg-white">
      <h6 class="mb-0 text-info">
        <i class="fas fa-filter me-2"></i> Filter Options
      </h6>
    </div>
    <div class="card-body">
      <div class="form-check">
        <input class="form-check-input" type="checkbox" id="showInactive" name="showInactive"
               [(ngModel)]="active" (change)="searchByStatus()">
        <label class="form-check-label" for="showInactive">Show Inactive Loan Plans</label>
      </div>
    </div>
  </div>

  <!-- Loan Plans Table -->
  <div class="card shadow-sm mb-4">
    <div class="card-header d-flex justify-content-between align-items-center bg-white">
      <h6 class="mb-0 text-info">
        <i class="fas fa-table me-2"></i> Loan Plans
      </h6>
      <span class="badge bg-info">{{loanPlans?.length || 0}} plans</span>
    </div>
    <div class="card-body p-0">
      <div class="table-responsive">
        <table class="table table-hover align-middle">
          <thead class="table-light text-center">
          <tr>
            <th>Plan Name</th>
            <th>Amount Range</th>
            <th>Duration (Days)</th>
            <th>Interest Rate (%)</th>
            <th>Payment Frequency</th>
            <th>Status</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let plans of loanPlans; let i = index"
              (click)="loanPlansDetail(plans,i)"
              [class.table-active]="i === selectedRow"
              style="cursor: pointer;">
            <td>
              <strong>{{plans.name}}</strong>
              <small class="d-block text-muted" *ngIf="plans.description">{{plans.description}}</small>
            </td>
            <td class="text-center">{{plans.minAmount | number:'1.2-2'}} - {{plans.maxAmount | number:'1.2-2'}}</td>
            <td class="text-center">
              <span class="badge bg-info">{{plans.durationInDays}} days</span>
            </td>
            <td class="text-center">
              <span class="badge bg-success">{{plans.interestRate}}%</span>
            </td>
            <td class="text-center">
              <span class="badge bg-secondary">Every {{plans.paymentFrequencyInDays}} days</span>
            </td>
            <td class="text-center">
                <span class="badge"
                      [ngClass]="plans.active ? 'bg-success' : 'bg-danger'">
                  {{plans.active ? 'Active' : 'Inactive'}}
                </span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="p-3 d-flex justify-content-center">
        <pagination class="pagination-sm"
                    [totalItems]="collectionSize"
                    [(ngModel)]="page"
                    [maxSize]="15"
                    [itemsPerPage]="pageSize"
                    [boundaryLinks]="true"
                    (pageChanged)="pageChanged($event)">
        </pagination>
      </div>
    </div>
  </div>

  <!-- Actions Card -->
  <div class="card shadow-sm">
    <div class="card-header bg-white">
      <h6 class="mb-0 text-info">
        <i class="fas fa-cogs me-2"></i> Actions
      </h6>
    </div>
    <div class="card-body text-end">
      <button class="btn btn-primary" type="button" (click)="openModal()" [disabled]="selectedRow === null">
        <i class="fas fa-edit me-1"></i> Edit Plan
      </button>
    </div>
  </div>
</div>
