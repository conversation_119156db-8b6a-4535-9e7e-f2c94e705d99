.card {
  border-radius: 10px;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

.form-check-input:checked {
  background-color: #28a745;
  border-color: #28a745;
}

.form-check-input:focus {
  box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);
}

.form-switch .form-check-input {
  width: 2em;
  height: 1em;
}

.alert {
  border-radius: 8px;
  border: none;
}

.badge {
  font-size: 0.75rem;
  font-weight: 500;
}

.spinner-border {
  width: 2rem;
  height: 2rem;
}

.card-header {
  border-radius: 10px 10px 0 0 !important;
}

.text-primary {
  color: #007bff !important;
}

.bg-primary {
  background-color: #007bff !important;
}

.border-info {
  border-color: #17a2b8 !important;
}

.bg-info {
  background-color: #17a2b8 !important;
}
