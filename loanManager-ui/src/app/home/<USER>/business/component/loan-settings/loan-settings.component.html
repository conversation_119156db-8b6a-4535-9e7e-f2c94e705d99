<div class="container-fluid">
  <!-- Page Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h2 class="mb-1">
            <i class="fas fa-calendar-alt me-2 text-primary"></i>
            Loan Due Date Settings
          </h2>
          <p class="text-muted mb-0">Configure how loan due dates are calculated</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Alert Messages -->
  <div class="row mb-3" *ngIf="error || success">
    <div class="col-12">
      <div class="alert alert-danger alert-dismissible fade show" *ngIf="error">
        <i class="fas fa-exclamation-triangle me-2"></i>
        {{ error }}
        <button type="button" class="btn-close" (click)="error = ''"></button>
      </div>
      <div class="alert alert-success alert-dismissible fade show" *ngIf="success">
        <i class="fas fa-check-circle me-2"></i>
        {{ success }}
        <button type="button" class="btn-close" (click)="success = ''"></button>
      </div>
    </div>
  </div>

  <!-- Settings Card -->
  <div class="row">
    <div class="col-lg-8">
      <div class="card shadow-sm border-0">
        <div class="card-header bg-primary text-white d-flex align-items-center">
          <i class="fas fa-cogs me-2"></i>
          <h5 class="mb-0">Due Date Calculation</h5>
        </div>
        <div class="card-body">
          <!-- Loading Spinner -->
          <div class="text-center p-4" *ngIf="loading">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading settings...</p>
          </div>

          <!-- Settings Form -->
          <div *ngIf="!loading">
            <div class="row">
              <div class="col-12">
                <div class="form-check form-switch mb-4">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    id="useNextBusinessDay"
                    [(ngModel)]="useNextBusinessDay"
                    (change)="updateNextBusinessDaySetting()"
                    [disabled]="loading"
                  />
                  <label class="form-check-label fw-semibold" for="useNextBusinessDay">
                    Use Next Business Day for Due Dates
                  </label>
                </div>

                <div class="alert alert-info border-0">
                  <div class="d-flex">
                    <i class="fas fa-info-circle me-3 mt-1"></i>
                    <div>
                      <h6 class="alert-heading mb-2">How this setting works:</h6>
                      <ul class="mb-0 small">
                        <li *ngIf="!useNextBusinessDay">
                          <strong>Current behavior:</strong> Due dates are calculated by adding the payment frequency days to the current date.
                        </li>
                        <li *ngIf="useNextBusinessDay">
                          <strong>Business day behavior:</strong> Due dates will automatically move to the next business day if they fall on weekends or holidays.
                        </li>
                        <li>This setting affects all new loan records and payment schedules.</li>
                        <li>Existing loan records are not affected by this change.</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <!-- Current Status -->
                <div class="card bg-light border-0 mt-3">
                  <div class="card-body">
                    <h6 class="card-title mb-2">
                      <i class="fas fa-calendar-check me-2"></i>
                      Current Status
                    </h6>
                    <p class="card-text mb-0">
                      <span class="badge" [ngClass]="useNextBusinessDay ? 'bg-success' : 'bg-secondary'">
                        {{ useNextBusinessDay ? 'Business Day Mode Enabled' : 'Standard Mode Enabled' }}
                      </span>
                    </p>
                    <small class="text-muted">
                      {{ useNextBusinessDay ? 
                        'Due dates will skip weekends and holidays' : 
                        'Due dates are calculated using standard day counting' }}
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Help Card -->
    <div class="col-lg-4">
      <div class="card border-info">
        <div class="card-header bg-info text-white">
          <i class="fas fa-question-circle me-2"></i>
          Need Help?
        </div>
        <div class="card-body">
          <h6 class="fw-bold">Examples:</h6>
          <div class="small">
            <p class="mb-2">
              <strong>Standard Mode:</strong><br>
              If today is Friday and payment frequency is 7 days, due date will be next Friday.
            </p>
            <p class="mb-2">
              <strong>Business Day Mode:</strong><br>
              If the calculated due date falls on Saturday, it will move to Monday (assuming no holidays).
            </p>
            <p class="mb-0">
              <strong>Note:</strong> Holiday management can be configured separately in the Holiday Management section.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
