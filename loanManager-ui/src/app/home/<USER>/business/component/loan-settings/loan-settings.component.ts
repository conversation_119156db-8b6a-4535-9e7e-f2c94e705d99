import { Component, OnInit } from '@angular/core';
import { BusinessSettingsService } from '../../service/settings.service';

@Component({
  standalone: false,
  selector: 'app-loan-settings',
  templateUrl: './loan-settings.component.html',
  styleUrls: ['./loan-settings.component.css']
})
export class LoanSettingsComponent implements OnInit {

  useNextBusinessDay: boolean = false;
  loading: boolean = false;
  error: string = '';
  success: string = '';

  constructor(private settingsService: BusinessSettingsService) { }

  ngOnInit(): void {
    this.loadSettings();
  }

  loadSettings(): void {
    this.loading = true;
    this.error = '';

    this.settingsService.isNextBusinessDayEnabled().subscribe({
      next: (enabled) => {
        this.useNextBusinessDay = enabled;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading settings:', error);
        this.error = 'Failed to load settings. Please try again.';
        this.loading = false;
      }
    });
  }

  updateNextBusinessDaySetting(): void {
    this.loading = true;
    this.error = '';
    this.success = '';

    const newValue = this.useNextBusinessDay.toString();

    this.settingsService.updateSetting('USE_NEXT_BUSINESS_DAY', newValue).subscribe({
      next: (response) => {
        this.success = 'Due date setting updated successfully!';
        this.loading = false;
        setTimeout(() => this.success = '', 3000);
      },
      error: (error) => {
        console.error('Error updating setting:', error);
        this.error = 'Failed to update setting. Please try again.';
        this.loading = false;
        // Revert the toggle
        this.useNextBusinessDay = !this.useNextBusinessDay;
        setTimeout(() => this.error = '', 5000);
      }
    });
  }
}
