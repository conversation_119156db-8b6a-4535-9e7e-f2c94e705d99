import {Component, OnInit} from '@angular/core';
import {NgForm} from "@angular/forms";
import {LoanPlan} from "../../model/loanPlan";
import {LoanPlanService} from "../../service/loanPlanService";
import {NotificationService} from "../../../../core/service/notification.service";

@Component({
  standalone: false,
  selector: 'app-create-loan-plan',
  templateUrl: './create-loan-plan.component.html',
  styleUrls: ['./create-loan-plan.component.css']
})
export class CreateLoanPlanComponent implements OnInit {

  loanPlan: LoanPlan;
  isEdit: boolean;
  isSubmitting: boolean = false;

  constructor(private loanPlanService: LoanPlanService, private notificationService: NotificationService) {
  }

  ngOnInit(): void {
    this.loanPlan = new LoanPlan();
  }

  saveLoanPlan() {
    this.isSubmitting = true;
    this.loanPlanService.save(this.loanPlan).subscribe(result => {
      if (result === true) {
        this.notificationService.showSuccess("Loan Plan Create successfully.");
        this.ngOnInit();
      } else {
        this.notificationService.showWarning("Loan Plan Create Failed");
      }
      this.isSubmitting = false;
    });
  }

  clear() {
    this.loanPlan = new LoanPlan();
  }
}
