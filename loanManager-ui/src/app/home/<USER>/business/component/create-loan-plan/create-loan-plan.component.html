<div class="container-fluid py-4">
  <!-- Header Section -->
  <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-3">
    <h2 class="h4 mb-2 mb-md-0">
      <i class="fas fa-clipboard-list me-2"></i>
      {{ isEdit ? 'Edit' : 'New' }} Loan Plan
    </h2>
    <button type="button" class="btn btn-outline-primary btn-sm" (click)="ngOnInit()">
      <i class="fas fa-sync-alt"></i> Reset
    </button>
  </div>
  <p class="text-muted mb-4">
    {{ isEdit ? 'Update loan plan details and configuration.' : 'Create a new loan plan with interest rates and terms.' }}
  </p>

  <!-- Form Card -->
  <div class="card">
    <div class="card-header d-flex align-items-center">
      <i class="fas fa-edit me-2"></i>
      <h5 class="mb-0">Loan Plan Details</h5>
    </div>
    <div class="card-body">
      <form #loanPlanForm="ngForm" (ngSubmit)="saveLoanPlan();">
        <div class="row g-3">

          <div class="col-md-6">
            <label for="loanPlan" class="form-label">Loan Plan Name</label>
            <input
              type="text"
              id="loanPlan"
              name="loanPlan"
              class="form-control"
              placeholder="Loan Plan Name"
              required
              [(ngModel)]="loanPlan.name"
              #loanP="ngModel"
              [disabled]="isEdit"
              [class.is-invalid]="loanP.invalid && loanP.touched"
            />
            <div class="invalid-feedback" *ngIf="loanP.invalid && loanP.touched">
              Loan Plan Name is required.
            </div>
          </div>

          <div class="col-md-3">
            <label for="max" class="form-label">Max Amount (Rs)</label>
            <input
              type="text"
              id="max"
              name="max"
              class="form-control"
              placeholder="Enter Max Amount"
              required
              [(ngModel)]="loanPlan.maxAmount"
              #max="ngModel"
              [disabled]="isEdit"
              [class.is-invalid]="max.invalid && max.touched"
            />
            <div class="invalid-feedback" *ngIf="max.invalid && max.touched">
              Max Amount is required.
            </div>
          </div>

          <div class="col-md-3">
            <label for="min" class="form-label">Min Amount (Rs)</label>
            <input
              type="text"
              id="min"
              name="min"
              class="form-control"
              placeholder="Enter Min Amount"
              required
              [(ngModel)]="loanPlan.minAmount"
              #min="ngModel"
              [disabled]="isEdit"
              [class.is-invalid]="min.invalid && min.touched"
            />
            <div class="invalid-feedback" *ngIf="min.invalid && min.touched">
              Min Amount is required.
            </div>
          </div>

          <div class="col-md-3">
            <label for="duration" class="form-label">Duration (Days)</label>
            <input
              type="text"
              id="duration"
              name="duration"
              class="form-control"
              placeholder="Enter Duration"
              required
              [(ngModel)]="loanPlan.durationInDays"
              #duration="ngModel"
              [class.is-invalid]="duration.invalid && duration.touched"
            />
            <div class="invalid-feedback" *ngIf="duration.invalid && duration.touched">
              Duration is required.
            </div>
          </div>

          <div class="col-md-3">
            <label for="freq" class="form-label">Payment Frequency (Days)</label>
            <input
              type="text"
              id="freq"
              name="freq"
              class="form-control"
              placeholder="Enter Payment Frequency"
              required
              [(ngModel)]="loanPlan.paymentFrequencyInDays"
              #freq="ngModel"
              [class.is-invalid]="freq.invalid && freq.touched"
            />
            <div class="invalid-feedback" *ngIf="freq.invalid && freq.touched">
              Payment Frequency is required.
            </div>
          </div>

          <div class="col-md-3">
            <label for="noOfIns" class="form-label">Total No Of Installments</label>
            <input
              type="text"
              id="noOfIns"
              name="noOfIns"
              class="form-control"
              placeholder="Enter Total No Of Installments"
              required
              [(ngModel)]="loanPlan.totalNoOfInstallments"
              #noOfIns="ngModel"
              [class.is-invalid]="noOfIns.invalid && noOfIns.touched"
            />
            <div class="invalid-feedback" *ngIf="noOfIns.invalid && noOfIns.touched">
              Total No Of Installments is required.
            </div>
          </div>

          <div class="col-md-3">
            <label for="iRate" class="form-label">Interest Rate (Monthly)</label>
            <input
              type="text"
              id="iRate"
              name="iRate"
              class="form-control"
              placeholder="Enter Interest Rate"
              required
              [(ngModel)]="loanPlan.interestRate"
              #iRate="ngModel"
              [class.is-invalid]="iRate.invalid && iRate.touched"
            />
            <div class="invalid-feedback" *ngIf="iRate.invalid && iRate.touched">
              Interest Rate is required.
            </div>
          </div>

          <div class="col-md-3">
            <label for="arrearsInterestRate" class="form-label">Arrears Interest Rate</label>
            <input
              type="text"
              id="arrearsInterestRate"
              name="arrearsInterestRate"
              class="form-control"
              placeholder="Enter Arrears Interest Rate"
              required
              [(ngModel)]="loanPlan.arrearsInterestRate"
              #arrearsInterestRate="ngModel"
              [class.is-invalid]="arrearsInterestRate.invalid && arrearsInterestRate.touched"
            />
            <div class="invalid-feedback" *ngIf="arrearsInterestRate.invalid && arrearsInterestRate.touched">
              Arrears Interest Rate is required.
            </div>
          </div>

          <div class="col-md-3">
            <label for="arrearsInterestDuration" class="form-label">Arrears Interest Duration</label>
            <input
              type="text"
              id="arrearsInterestDuration"
              name="arrearsInterestDuration"
              class="form-control"
              placeholder="Enter Arrears Interest Duration"
              required
              [(ngModel)]="loanPlan.arrearsInterestDuration"
              #arrearsInterestDuration="ngModel"
              [class.is-invalid]="arrearsInterestDuration.invalid && arrearsInterestDuration.touched"
            />
            <div class="invalid-feedback" *ngIf="arrearsInterestDuration.invalid && arrearsInterestDuration.touched">
              Arrears Interest Duration is required.
            </div>
          </div>

          <div class="col-md-3 d-flex align-items-center mt-4">
            <div class="form-check">
              <input
                class="form-check-input"
                type="checkbox"
                id="check3"
                name="check3"
                [checked]="loanPlan.active"
                [disabled]="isEdit"
              />
              <label class="form-check-label" for="check3">Is Active</label>
            </div>
          </div>
        </div>

        <div class="mt-4 text-end">
          <button
            type="button"
            class="btn btn-outline-secondary me-2"
            (click)="clear();"
            [disabled]="isEdit"
          >
            <i class="fas fa-eraser me-1"></i> Clear
          </button>
          <button type="submit" class="btn btn-primary" [disabled]="!loanPlanForm.form.valid">
            <i class="fas fa-save me-1"></i> {{ isEdit ? 'Update' : 'Save' }} Plan
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
