<div class="container-fluid py-4">
  <!-- Header Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-start flex-wrap gap-3">
        <div>
          <h2 class="h4 fw-semibold mb-1">Holiday Management</h2>
          <p class="text-muted mb-0 small">Manage holidays and non-working days to exclude them from loan collection schedules.</p>
        </div>
        <div class="d-flex flex-wrap gap-2">
          <button class="btn btn-outline-primary btn-sm" (click)="loadHolidays()" [disabled]="loading">
            <i class="fas fa-sync-alt me-1" [class.fa-spin]="loading"></i> Refresh
          </button>
          <button class="btn btn-success btn-sm" (click)="addPredefinedHolidays()">
            <i class="fas fa-plus me-1"></i> Common Holidays
          </button>
          <button class="btn btn-danger btn-sm" (click)="openRemoveRecordsModal(removeRecordsModal)">
            <i class="fas fa-trash me-1"></i> Remove Records
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="row g-4">
    <!-- Holiday Form -->
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-header bg-light">
          <h5 class="mb-0 text-primary">{{isEditing ? 'Edit Holiday' : 'Add New Holiday'}}</h5>
        </div>
        <div class="card-body">
          <form (ngSubmit)="saveHoliday()" #holidayForm="ngForm">
            <div class="mb-3">
              <label class="form-label">Holiday Name <span class="text-danger">*</span></label>
              <input type="text" class="form-control" [(ngModel)]="holiday.name" name="holidayName" placeholder="Eg: Independence Day" required>
            </div>

            <div class="mb-3">
              <label class="form-label">Date <span class="text-danger">*</span></label>
              <input type="date" class="form-control" [(ngModel)]="holiday.date" name="holidayDate" required>
            </div>

            <div class="mb-3">
              <label class="form-label">Description</label>
              <textarea class="form-control" rows="3" [(ngModel)]="holiday.description" name="holidayDescription" placeholder="Optional description"></textarea>
            </div>

            <div class="form-check mb-2">
              <input class="form-check-input" type="checkbox" id="recurringCheck" [(ngModel)]="holiday.isRecurring" name="isRecurring">
              <label class="form-check-label" for="recurringCheck">Recurring Holiday (Annual)</label>
            </div>

            <div class="form-check mb-3">
              <input class="form-check-input" type="checkbox" id="activeCheck" [(ngModel)]="holiday.active" name="isActive">
              <label class="form-check-label" for="activeCheck">Active</label>
            </div>

            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-primary" [disabled]="!holidayForm.form.valid || loading">
                {{isEditing ? 'Update Holiday' : 'Add Holiday'}}
              </button>
              <button type="button" class="btn btn-outline-secondary" *ngIf="isEditing" (click)="resetForm()">Cancel</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Holidays List -->
    <div class="col-md-8">
      <div class="card shadow-sm">
        <div class="card-header bg-light">
          <div class="d-flex justify-content-between align-items-center flex-wrap gap-2">
            <h5 class="mb-0 text-primary">Holidays List</h5>
            <div class="d-flex flex-wrap gap-2 align-items-center">
              <div class="input-group input-group-sm" style="width: 200px;">
                <input type="text" class="form-control" [(ngModel)]="searchTerm" (keyup.enter)="searchHolidays()" placeholder="Search...">
                <button class="btn btn-outline-secondary" type="button" (click)="searchHolidays()">
                  <i class="fas fa-search"></i>
                </button>
              </div>
              <select class="form-select form-select-sm" style="width: 100px;" [(ngModel)]="currentYear" (change)="filterByYear(currentYear)">
                <option *ngFor="let year of getYearOptions()" [value]="year">{{year}}</option>
              </select>
            </div>
          </div>
        </div>

        <div class="card-body">
          <!-- Loading -->
          <div *ngIf="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-2 small text-muted">Loading holidays...</div>
          </div>

          <!-- Holiday Table -->
          <div *ngIf="!loading && holidays.length > 0" class="table-responsive">
            <table class="table table-sm table-hover align-middle">
              <thead class="table-light">
              <tr>
                <th>Name</th>
                <th>Date</th>
                <th>Type</th>
                <th>Status</th>
                <th class="text-end">Actions</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let holiday of holidays"
                  [class.table-success]="isHolidayToday(holiday)"
                  [class.table-warning]="isHolidayUpcoming(holiday)">
                <td>
                  <strong>{{holiday.name}}</strong>
                  <div class="small text-muted" *ngIf="holiday.description">{{holiday.description}}</div>
                </td>
                <td>
                  {{holiday.date | date:'mediumDate'}}
                  <span *ngIf="isHolidayToday(holiday)" class="badge bg-info ms-1">Today</span>
                  <span *ngIf="isHolidayUpcoming(holiday)" class="badge bg-warning text-dark ms-1">Upcoming</span>
                </td>
                <td>
                    <span class="badge" [ngClass]="holiday.isRecurring ? 'bg-primary' : 'bg-secondary'">
                      {{holiday.isRecurring ? 'Recurring' : 'One-time'}}
                    </span>
                </td>
                <td>
                  <div class="form-check form-switch m-0">
                    <input class="form-check-input" type="checkbox"
                           [id]="'switch-' + holiday.id"
                           [checked]="holiday.active"
                           (change)="toggleHolidayStatus(holiday)">
                    <label class="form-check-label small" [for]="'switch-' + holiday.id">
                      {{holiday.active ? 'Active' : 'Inactive'}}
                    </label>
                  </div>
                </td>
                <td class="text-end">
                  <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" (click)="editHoliday(holiday)" title="Edit">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger" (click)="deleteHoliday(holiday)" title="Delete">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
              </tbody>
            </table>
          </div>

          <!-- No Data -->
          <div *ngIf="!loading && holidays.length === 0" class="text-center py-5 text-muted">
            <i class="fas fa-calendar-times fa-3x mb-3"></i>
            <h5>No Holidays Found</h5>
            <p class="small">Use the form to add your first holiday.</p>
          </div>

          <!-- Pagination -->
          <div *ngIf="!loading && holidays.length > 0 && collectionSize > pageSize" class="d-flex justify-content-center mt-3">
            <pagination
              [totalItems]="collectionSize"
              [(ngModel)]="page"
              [maxSize]="pageSize"
              [itemsPerPage]="pageSize"
              (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Remove Unpaid Records Modal -->
<ng-template #removeRecordsModal>
  <div class="modal-header bg-danger text-white">
    <h4 class="modal-title">
      <i class="fas fa-exclamation-triangle me-2"></i>Remove Unpaid Loan Records
    </h4>
    <button type="button" class="btn-close btn-close-white" aria-label="Close" (click)="closeModal()"></button>
  </div>

  <div class="modal-body">
    <div class="alert alert-danger" role="alert">
      <h6 class="alert-heading">
        <i class="fas fa-shield-alt me-2"></i>Admin Only Action
      </h6>
      <strong>Warning:</strong> This action will permanently remove all unpaid loan records for the selected date.
      Only records with zero payment amount will be deleted. This action cannot be undone and will be logged for audit purposes.
    </div>

    <div class="row">
      <div class="col-md-6">
        <div class="mb-3">
          <label class="form-label fw-semibold">Select Date <span class="text-danger">*</span></label>
          <input
            type="date"
            class="form-control"
            [(ngModel)]="selectedDate"
            name="selectedDate"
            [disabled]="isRemovingRecords"
            [max]="maxDate">
          <small class="form-text text-muted">Only past and current dates are allowed</small>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card bg-light">
          <div class="card-body p-3">
            <h6 class="card-title text-primary">
              <i class="fas fa-info-circle me-1"></i>What will be removed?
            </h6>
            <ul class="small mb-0">
              <li>Loan records with <strong>paidAmount = 0</strong></li>
              <li>Records for the selected date only</li>
              <li>No paid records will be affected</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="mt-3">
      <div class="form-check">
        <input class="form-check-input" type="checkbox" id="confirmCheck" #confirmCheck>
        <label class="form-check-label fw-semibold" for="confirmCheck">
          I understand this action cannot be undone and will be logged for audit purposes
        </label>
      </div>
    </div>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="closeModal()" [disabled]="isRemovingRecords">
      Cancel
    </button>
    <button
      type="button"
      class="btn btn-danger"
      (click)="removeUnpaidRecords()"
      [disabled]="!selectedDate || isRemovingRecords || !confirmCheck.checked">
      <i class="fas fa-trash me-1" [class.fa-spin]="isRemovingRecords"></i>
      {{isRemovingRecords ? 'Removing Records...' : 'Remove Unpaid Records'}}
    </button>
  </div>
</ng-template>
