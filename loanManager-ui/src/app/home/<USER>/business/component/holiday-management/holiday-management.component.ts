import { Component, OnInit, TemplateRef } from '@angular/core';
import { HolidayService } from '../../service/holiday.service';
import { NotificationService } from '../../../../core/service/notification.service';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { LoanRecordService } from '../../service/loanRecord.service';
import { ActionLogService } from '../../service/action-log.service';

export interface Holiday {
  id?: string;
  name: string;
  date: string;
  description?: string;
  isRecurring: boolean;
  active: boolean;
  createdDate?: string;
  modifiedDate?: string;
}

@Component({
  standalone: false,
  selector: 'app-holiday-management',
  templateUrl: './holiday-management.component.html',
  styleUrls: ['./holiday-management.component.css']
})
export class HolidayManagementComponent implements OnInit {

  holidays: Holiday[] = [];
  holiday: Holiday = {
    name: '',
    date: '',
    description: '',
    isRecurring: false,
    active: true
  };

  selectedHoliday: Holiday | null = null;
  isEditing: boolean = false;
  loading: boolean = false;
  searchTerm: string = '';
  currentYear: number = new Date().getFullYear();

  // Pagination
  page: number = 1;
  pageSize: number = 10;
  collectionSize: number = 0;

  // Remove records modal properties
  modalRef?: BsModalRef;
  selectedDate: string = '';
  isRemovingRecords: boolean = false;
  currentUser: any = null;

  maxDate: string;

  constructor(
    private holidayService: HolidayService,
    private notificationService: NotificationService,
    private modalService: BsModalService,
    private loanRecordService: LoanRecordService,
    private actionLogService: ActionLogService
  ) { }

  ngOnInit(): void {
    this.loadHolidays();
    this.maxDate = new Date().toISOString().split('T')[0];
    // Get current user from session storage
    const userStr = sessionStorage.getItem('user');
    if (userStr) {
      this.currentUser = JSON.parse(userStr);
    }
  }

  loadHolidays(): void {
    this.loading = true;
    this.holidayService.findAll(this.page - 1, this.pageSize).subscribe({
      next: (data: any) => {
        if (data) {
          this.holidays = data.content || data;
          this.collectionSize = data.totalElements || this.holidays.length;
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading holidays:', error);
        this.notificationService.showError('Failed to load holidays');
        this.loading = false;
      }
    });
  }

  saveHoliday(): void {
    if (!this.holiday.name || !this.holiday.date) {
      this.notificationService.showError('Please fill in all required fields');
      return;
    }

    this.loading = true;

    const saveObservable = this.isEditing
      ? this.holidayService.update(this.holiday)
      : this.holidayService.save(this.holiday);

    saveObservable.subscribe({
      next: (response) => {
        if (response) {
          this.notificationService.showSuccess(
            this.isEditing ? 'Holiday updated successfully' : 'Holiday created successfully'
          );
          this.resetForm();
          this.loadHolidays();
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error saving holiday:', error);
        this.notificationService.showError('Failed to save holiday');
        this.loading = false;
      }
    });
  }

  editHoliday(holiday: Holiday): void {
    this.holiday = { ...holiday };
    this.selectedHoliday = holiday;
    this.isEditing = true;
  }

  deleteHoliday(holiday: Holiday): void {
    if (confirm('Are you sure you want to delete this holiday?')) {
      this.loading = true;
      this.holidayService.delete(holiday.id!).subscribe({
        next: (response) => {
          if (response) {
            this.notificationService.showSuccess('Holiday deleted successfully');
            this.loadHolidays();
          }
          this.loading = false;
        },
        error: (error) => {
          console.error('Error deleting holiday:', error);
          this.notificationService.showError('Failed to delete holiday');
          this.loading = false;
        }
      });
    }
  }

  toggleHolidayStatus(holiday: Holiday): void {
    const updatedHoliday = { ...holiday, active: !holiday.active };
    this.holidayService.update(updatedHoliday).subscribe({
      next: (response) => {
        if (response) {
          holiday.active = !holiday.active;
          this.notificationService.showSuccess(
            `Holiday ${holiday.active ? 'activated' : 'deactivated'} successfully`
          );
        }
      },
      error: (error) => {
        console.error('Error updating holiday status:', error);
        this.notificationService.showError('Failed to update holiday status');
      }
    });
  }

  resetForm(): void {
    this.holiday = {
      name: '',
      date: '',
      description: '',
      isRecurring: false,
      active: true
    };
    this.selectedHoliday = null;
    this.isEditing = false;
  }

  searchHolidays(): void {
    if (this.searchTerm.trim()) {
      this.holidayService.findByName(this.searchTerm).subscribe({
        next: (data: Holiday[]) => {
          this.holidays = data || [];
          this.collectionSize = this.holidays.length;
        },
        error: (error) => {
          console.error('Error searching holidays:', error);
          this.notificationService.showError('Failed to search holidays');
        }
      });
    } else {
      this.loadHolidays();
    }
  }

  filterByYear(year: number): void {
    this.currentYear = year;
    this.holidayService.findByYear(year).subscribe({
      next: (data: Holiday[]) => {
        this.holidays = data || [];
        this.collectionSize = this.holidays.length;
      },
      error: (error) => {
        console.error('Error filtering holidays by year:', error);
        this.notificationService.showError('Failed to filter holidays');
      }
    });
  }

  pageChanged(event: any): void {
    this.page = event.page;
    this.loadHolidays();
  }

  getYearOptions(): number[] {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear - 2; i <= currentYear + 2; i++) {
      years.push(i);
    }
    return years;
  }

  isHolidayToday(holiday: Holiday): boolean {
    const today = new Date().toISOString().split('T')[0];
    return holiday.date === today;
  }

  isHolidayUpcoming(holiday: Holiday): boolean {
    const today = new Date();
    const holidayDate = new Date(holiday.date);
    const diffTime = holidayDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 && diffDays <= 7;
  }

  addPredefinedHolidays(): void {
    const currentYear = this.currentYear;
    const predefinedHolidays: Holiday[] = [
      // International holidays
      {
        name: 'New Year\'s Day',
        date: `${currentYear}-01-01`,
        description: 'International New Year celebration',
        isRecurring: true,
        active: true
      },
      // Sri Lankan holidays
      {
        name: 'Independence Day',
        date: `${currentYear}-02-04`,
        description: 'Sri Lankan Independence Day',
        isRecurring: true,
        active: true
      },
      {
        name: 'National Heroes Day',
        date: `${currentYear}-05-22`,
        description: 'Commemorating national heroes',
        isRecurring: true,
        active: true
      },
      // Christian holidays
      {
        name: 'Good Friday',
        date: this.getGoodFridayDate(currentYear),
        description: 'Christian holiday - Friday before Easter',
        isRecurring: true,
        active: true
      },
      {
        name: 'Christmas Day',
        date: `${currentYear}-12-25`,
        description: 'Christian holiday celebrating birth of Jesus',
        isRecurring: true,
        active: true
      },
      // International Workers' Day
      {
        name: 'Labour Day',
        date: `${currentYear}-05-01`,
        description: 'International Workers\' Day',
        isRecurring: true,
        active: true
      }
    ];

    // Add Poya Days for the current year
    const poyaDays = this.getPoyaDaysForYear(currentYear);
    predefinedHolidays.push(...poyaDays);

    let addedCount = 0;
    let totalToAdd = 0;

    predefinedHolidays.forEach(holiday => {
      // Check if holiday already exists for this year
      const exists = this.holidays.some(h =>
        h.date === holiday.date && h.name === holiday.name
      );

      if (!exists) {
        totalToAdd++;
        this.holidayService.save(holiday).subscribe({
          next: (response: any) => {
            if (response.code === 200) {
              addedCount++;
              if (addedCount === totalToAdd) {
                this.notificationService.showSuccess(`Added ${addedCount} holidays including Poya days for ${currentYear}`);
                this.loadHolidays();
              }
            }
          },
          error: (error) => {
            console.error('Error adding predefined holiday:', error);
          }
        });
      }
    });

    if (totalToAdd === 0) {
      this.notificationService.showInfo('All predefined holidays including Poya days for this year already exist');
    }
  }

  private getGoodFridayDate(year: number): string {
    // Simple calculation for Good Friday (2 days before Easter Sunday)
    // This is a simplified version - you might want to use a proper Easter calculation
    const easter = this.getEasterDate(year);
    const goodFriday = new Date(easter);
    goodFriday.setDate(easter.getDate() - 2);
    return goodFriday.toISOString().split('T')[0];
  }

  private getEasterDate(year: number): Date {
    // Simplified Easter calculation
    const a = year % 19;
    const b = Math.floor(year / 100);
    const c = year % 100;
    const d = Math.floor(b / 4);
    const e = b % 4;
    const f = Math.floor((b + 8) / 25);
    const g = Math.floor((b - f + 1) / 3);
    const h = (19 * a + b - d - g + 15) % 30;
    const i = Math.floor(c / 4);
    const k = c % 4;
    const l = (32 + 2 * e + 2 * i - h - k) % 7;
    const m = Math.floor((a + 11 * h + 22 * l) / 451);
    const month = Math.floor((h + l - 7 * m + 114) / 31);
    const day = ((h + l - 7 * m + 114) % 31) + 1;
    return new Date(year, month - 1, day);
  }

  openRemoveRecordsModal(template: TemplateRef<any>) {
    // Check if user is admin
    if (!this.isAdminUser()) {
      this.notificationService.showError('Access denied. Only admin users can perform this action.');
      return;
    }

    this.selectedDate = '';
    this.modalRef = this.modalService.show(template, {
      class: 'modal-lg',
      backdrop: 'static',
      keyboard: false
    });
  }

  isAdminUser(): boolean {
    // Check if user has admin role or specific permission
    return this.currentUser &&
           (this.currentUser.role === 'ADMIN' ||
            this.currentUser.permissions?.includes('REMOVE_LOAN_RECORDS') ||
            this.currentUser.userType === 'ADMIN');
  }

  removeUnpaidRecords() {
    if (!this.selectedDate) {
      this.notificationService.showWarning('Please select a date');
      return;
    }

    if (!this.isAdminUser()) {
      this.notificationService.showError('Access denied. Only admin users can perform this action.');
      return;
    }

    this.isRemovingRecords = true;
    this.loanRecordService.removeUnpaidRecordsForDate(this.selectedDate).subscribe({
      next: (response: any) => {
        if (response.code === 200) {
          this.notificationService.showSuccess(response.message);

          // Log the action
          this.logAction(response.data || 0);

          this.closeModal();
        } else {
          this.notificationService.showError(response.message || 'Failed to remove records');
          this.isRemovingRecords = false;
        }
      },
      error: (error) => {
        console.error('Error removing unpaid records:', error);
        this.notificationService.showError('Failed to remove unpaid records');
        this.isRemovingRecords = false;
      }
    });
  }

  private logAction(deletedCount: number) {
    const actionLog = {
      action: 'REMOVE_UNPAID_LOAN_RECORDS',
      description: `Removed ${deletedCount} unpaid loan records for date: ${this.selectedDate}`,
      performedBy: this.currentUser?.username || this.currentUser?.name || 'Unknown',
      performedAt: new Date().toISOString(),
      details: {
        date: this.selectedDate,
        deletedCount: deletedCount,
        userRole: this.currentUser?.role || this.currentUser?.userType
      }
    };

    this.actionLogService.logAction(actionLog).subscribe({
      next: (response) => {
        console.log('Action logged successfully');
      },
      error: (error) => {
        console.error('Failed to log action:', error);
      }
    });
  }

  closeModal() {
    this.selectedDate = '';
    this.isRemovingRecords = false;
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  getPoyaDaysForYear(year: number): Holiday[] {
    // Poya days are based on lunar calendar
    // These are approximate dates for Poya days in Sri Lanka
    const poyaDays: Holiday[] = [];

    // Full moon dates for each month (approximate)
    const poyaMonths = [
      { month: 1, name: 'Duruthu Poya', day: this.getFullMoonDay(year, 1) },
      { month: 2, name: 'Navam Poya', day: this.getFullMoonDay(year, 2) },
      { month: 3, name: 'Madin Poya', day: this.getFullMoonDay(year, 3) },
      { month: 4, name: 'Bak Poya', day: this.getFullMoonDay(year, 4) },
      { month: 5, name: 'Vesak Poya', day: this.getFullMoonDay(year, 5) },
      { month: 6, name: 'Poson Poya', day: this.getFullMoonDay(year, 6) },
      { month: 7, name: 'Esala Poya', day: this.getFullMoonDay(year, 7) },
      { month: 8, name: 'Nikini Poya', day: this.getFullMoonDay(year, 8) },
      { month: 9, name: 'Binara Poya', day: this.getFullMoonDay(year, 9) },
      { month: 10, name: 'Vap Poya', day: this.getFullMoonDay(year, 10) },
      { month: 11, name: 'Il Poya', day: this.getFullMoonDay(year, 11) },
      { month: 12, name: 'Unduvap Poya', day: this.getFullMoonDay(year, 12) }
    ];

    poyaMonths.forEach(poya => {
      const date = `${year}-${poya.month.toString().padStart(2, '0')}-${poya.day.toString().padStart(2, '0')}`;
      poyaDays.push({
        name: poya.name,
        date: date,
        description: 'Buddhist full moon day - Public holiday in Sri Lanka',
        isRecurring: true,
        active: true
      });
    });

    return poyaDays;
  }

  private getFullMoonDay(year: number, month: number): number {
    // Simplified calculation for full moon days
    // In reality, you would use a proper lunar calendar calculation
    // This is an approximation based on average lunar cycle
    const baseFullMoon = new Date(2024, 0, 25); // Known full moon date
    const targetDate = new Date(year, month - 1, 15); // Mid month as starting point

    const daysDiff = Math.floor((targetDate.getTime() - baseFullMoon.getTime()) / (1000 * 60 * 60 * 24));
    const lunarCycle = 29.53; // Average lunar cycle in days
    const cyclesSince = Math.round(daysDiff / lunarCycle);

    const fullMoonDate = new Date(baseFullMoon.getTime() + (cyclesSince * lunarCycle * 24 * 60 * 60 * 1000));

    // Adjust to the correct month if needed
    if (fullMoonDate.getMonth() !== month - 1) {
      // Use a fixed day for each month as fallback
      const fixedDays = [25, 24, 25, 23, 23, 21, 21, 19, 17, 17, 15, 14];
      return fixedDays[month - 1];
    }

    return fullMoonDate.getDate();
  }
}
