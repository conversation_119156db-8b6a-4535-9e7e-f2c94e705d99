/* Ledger Container */
.ledger-container {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Page Header */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
}

.page-title i {
  margin-right: 10px;
  color: #ffd700;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.header-actions .btn {
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.header-actions .btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: white;
}

.page-description {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

/* Summary Cards */
.summary-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  border-left: 4px solid;
}

.summary-card:hover {
  transform: translateY(-3px);
}

.summary-balance {
  border-left-color: #17a2b8;
}

.summary-income {
  border-left-color: #28a745;
}

.summary-expense {
  border-left-color: #dc3545;
}

.summary-net {
  border-left-color: #007bff;
}

.summary-icon {
  font-size: 40px;
  margin-right: 20px;
  opacity: 0.8;
}

.summary-balance .summary-icon {
  color: #17a2b8;
}

.summary-income .summary-icon {
  color: #28a745;
}

.summary-expense .summary-icon {
  color: #dc3545;
}

.summary-net .summary-icon {
  color: #007bff;
}

.summary-content h4 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
}

.summary-content p {
  margin: 5px 0 0 0;
  color: #6c757d;
  font-weight: 500;
}

/* Form Card */
.form-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.form-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-header h5 {
  margin: 0;
  font-weight: 600;
  color: #495057;
}

.form-header i {
  margin-right: 8px;
  color: #667eea;
}

.form-body {
  padding: 25px;
}

/* Filters Card */
.filters-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.filters-header {
  background: #f8f9fa;
  padding: 15px 20px;
  border-bottom: 1px solid #dee2e6;
}

.filters-header h6 {
  margin: 0;
  font-weight: 600;
  color: #495057;
}

.filters-header i {
  margin-right: 8px;
  color: #667eea;
}

.filters-body {
  padding: 20px;
}

/* Records Card */
.records-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.records-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.records-header h5 {
  margin: 0;
  font-weight: 600;
  color: #495057;
}

.records-header i {
  margin-right: 8px;
  color: #667eea;
}

.records-body {
  padding: 20px;
}

/* Form Controls */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}

.form-control {
  border-radius: 8px;
  border: 1px solid #dee2e6;
  padding: 12px 15px;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-control-sm {
  padding: 8px 12px;
}

/* Table Styles */
.table {
  margin-bottom: 0;
}

.table th {
  background-color: #f8f9fa;
  border-top: none;
  font-weight: 600;
  color: #495057;
  padding: 15px 12px;
}

.table td {
  padding: 15px 12px;
  vertical-align: middle;
}

.table-hover tbody tr:hover {
  background-color: rgba(102, 126, 234, 0.05);
}

/* Badges */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
}

.badge-primary {
  background-color: #667eea;
}

.badge-secondary {
  background-color: #6c757d;
}

.badge-success {
  background-color: #28a745;
}

.badge-danger {
  background-color: #dc3545;
}

/* Loading Container */
.loading-container {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

/* No Data Container */
.no-data-container {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.no-data-content i {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.no-data-content h5 {
  margin-bottom: 10px;
  color: #495057;
}

/* Today's Records */
.today-records-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.today-header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 15px 20px;
}

.today-header h6 {
  margin: 0;
  font-weight: 600;
}

.today-header i {
  margin-right: 8px;
}

.today-body {
  padding: 20px;
}

.today-record-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-info {
  display: flex;
  flex-direction: column;
}

.record-type {
  font-weight: 600;
  color: #495057;
  font-size: 0.875rem;
}

.record-description {
  color: #6c757d;
  font-size: 0.8rem;
}

.record-amount {
  font-weight: 700;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ledger-container {
    padding: 15px;
  }
  
  .page-header {
    padding: 20px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .summary-card {
    padding: 20px;
    margin-bottom: 15px;
  }
  
  .summary-content h4 {
    font-size: 20px;
  }
  
  .form-body {
    padding: 20px;
  }
  
  .table-responsive {
    font-size: 0.875rem;
  }
}

@media (max-width: 576px) {
  .summary-card {
    flex-direction: column;
    text-align: center;
  }
  
  .summary-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .today-record-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
