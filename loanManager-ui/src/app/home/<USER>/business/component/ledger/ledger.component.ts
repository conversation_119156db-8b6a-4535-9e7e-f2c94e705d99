import { Component, OnInit } from '@angular/core';
import { LedgerService } from '../../service/ledger.service';
import { LedgerRecordService } from '../../service/ledger-record.service';
import { NotificationService } from '../../../../core/service/notification.service';

export interface Ledger {
  id?: string;
  appNo: string;
  currentBalance: number;
  route?: string;
}

export interface LedgerRecord {
  id?: string;
  appNo: string;
  type: string;
  amount: number;
  description: string;
  date: string;
  transactionType: string; // 'income' or 'expense'
  createdDate?: string;
}

@Component({
  standalone: false,
  selector: 'app-ledger',
  templateUrl: './ledger.component.html',
  styleUrls: ['./ledger.component.css']
})
export class LedgerComponent implements OnInit {

  ledger: Ledger = {
    appNo: '1',
    currentBalance: 0
  };

  records: LedgerRecord[] = [];
  todayRecords: LedgerRecord[] = [];

  // Form data
  newRecord: LedgerRecord = {
    appNo: '1',
    type: '',
    amount: 0,
    description: '',
    date: new Date().toISOString().split('T')[0],
    transactionType: 'expense'
  };

  // Filter data
  ledgerDateFrom: string = new Date().toISOString().split('T')[0];
  ledgerDateTo: string = new Date().toISOString().split('T')[0];
  selectedType: string = '';

  types: string[] = ['Salary', 'Fuel', 'Internet', 'Bank', 'Stationary', 'Other'];

  loading: boolean = false;
  showAddForm: boolean = false;

  // Pagination
  page: number = 1;
  pageSize: number = 10;
  collectionSize: number = 0;

  constructor(
    private ledgerService: LedgerService,
    private ledgerRecordService: LedgerRecordService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.loadLedgerData();
  }

  loadLedgerData(): void {
    this.findLedger();
    this.findTodayRecords();
    this.filterByTypeAndDate();
  }

  findLedger(): void {
    this.ledgerService.findLedger(this.ledger.appNo).subscribe({
      next: (data: Ledger) => {
        if (data) {
          this.ledger = data;
        }
      },
      error: (error) => {
        console.error('Error loading ledger:', error);
        this.notificationService.showError('Failed to load ledger data');
      }
    });
  }

  findTodayRecords(): void {
    const today = new Date().toISOString().split('T')[0];
    this.ledgerRecordService.findByDate(this.ledger.appNo, today).subscribe({
      next: (data: LedgerRecord[]) => {
        this.todayRecords = data || [];
      },
      error: (error) => {
        console.error('Error loading today records:', error);
      }
    });
  }

  filterByTypeAndDate(): void {
    this.loading = true;
    this.ledgerRecordService.findByType(
      this.ledger.appNo,
      this.selectedType,
      this.ledgerDateFrom,
      this.ledgerDateTo
    ).subscribe({
      next: (data: LedgerRecord[]) => {
        this.records = data || [];
        this.collectionSize = this.records.length;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error filtering records:', error);
        this.notificationService.showError('Failed to filter records');
        this.loading = false;
      }
    });
  }

  addLedgerRecord(): void {
    if (!this.newRecord.type || !this.newRecord.amount || !this.newRecord.description) {
      this.notificationService.showError('Please fill in all required fields');
      return;
    }

    this.loading = true;
    this.ledgerRecordService.save(this.newRecord).subscribe({
      next: (response) => {
        if (response) {
          this.notificationService.showSuccess('Ledger record added successfully');
          this.resetForm();
          this.loadLedgerData();
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error saving ledger record:', error);
        this.notificationService.showError('Failed to save ledger record');
        this.loading = false;
      }
    });
  }

  correctLedger(amount: number): void {
    if (!amount || amount < 0) {
      this.notificationService.showError('Please enter a valid amount');
      return;
    }

    this.loading = true;
    this.ledgerService.correctLedger(this.ledger.appNo, amount).subscribe({
      next: (response) => {
        if (response) {
          this.notificationService.showSuccess('Ledger balance corrected successfully');
          this.findLedger();
          this.findTodayRecords();
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error correcting ledger:', error);
        this.notificationService.showError('Failed to correct ledger balance');
        this.loading = false;
      }
    });
  }

  deleteLedgerRecord(record: LedgerRecord): void {
    if (confirm('Are you sure you want to delete this record?')) {
      this.ledgerRecordService.delete(record.id!).subscribe({
        next: (response) => {
          if (response) {
            this.notificationService.showSuccess('Record deleted successfully');
            this.loadLedgerData();
          }
        },
        error: (error) => {
          console.error('Error deleting record:', error);
          this.notificationService.showError('Failed to delete record');
        }
      });
    }
  }

  resetForm(): void {
    this.newRecord = {
      appNo: this.ledger.appNo,
      type: '',
      amount: 0,
      description: '',
      date: new Date().toISOString().split('T')[0],
      transactionType: 'expense'
    };
    this.showAddForm = false;
  }

  toggleAddForm(): void {
    this.showAddForm = !this.showAddForm;
    if (!this.showAddForm) {
      this.resetForm();
    }
  }

  getTotalIncome(): number {
    return this.records
      .filter(record => record.transactionType === 'income')
      .reduce((total, record) => total + record.amount, 0);
  }

  getTotalExpense(): number {
    return this.records
      .filter(record => record.transactionType === 'expense')
      .reduce((total, record) => total + record.amount, 0);
  }

  getNetAmount(): number {
    return this.getTotalIncome() - this.getTotalExpense();
  }

  refreshData(): void {
    this.loadLedgerData();
  }

  clearFilters(): void {
    this.selectedType = '';
    this.ledgerDateFrom = new Date().toISOString().split('T')[0];
    this.ledgerDateTo = new Date().toISOString().split('T')[0];
    this.filterByTypeAndDate();
  }

  exportRecords(): void {
    // Implementation for exporting records to CSV/Excel
    this.notificationService.showWarning('Export functionality will be implemented');
  }
}
