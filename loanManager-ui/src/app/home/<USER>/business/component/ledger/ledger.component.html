<div class="container-fluid py-4">
  <!-- Header Section -->
  <div class="d-flex justify-content-between align-items-start flex-wrap gap-3 mb-3">
    <div>
      <h2 class="h4 mb-1">
        <i class="fas fa-book me-2"></i>
        Ledger Management
      </h2>
      <p class="text-muted small mb-0">Track and manage financial transactions, expenses, and cash flow.</p>
    </div>
    <div class="d-flex flex-wrap gap-2">
      <button class="btn btn-outline-primary btn-sm" (click)="refreshData()" [disabled]="loading">
        <i class="fas fa-sync-alt me-1" [class.fa-spin]="loading"></i> Refresh
      </button>
      <button class="btn btn-success btn-sm" (click)="toggleAddForm()">
        <i class="fas fa-plus me-1"></i> Add Record
      </button>
      <button class="btn btn-info btn-sm text-white" (click)="exportRecords()">
        <i class="fas fa-download me-1"></i> Export
      </button>
    </div>
  </div>

  <!-- Summary Cards -->
  <div class="row g-3 mb-4">
    <div class="col-sm-6 col-md-3">
      <div class="card text-center shadow-sm">
        <div class="card-body">
          <i class="fas fa-wallet text-primary fs-4 mb-2"></i>
          <h5 class="mb-1">{{ledger.currentBalance | number:'1.2-2'}}</h5>
          <p class="text-muted small mb-0">Current Balance</p>
        </div>
      </div>
    </div>
    <div class="col-sm-6 col-md-3">
      <div class="card text-center shadow-sm">
        <div class="card-body">
          <i class="fas fa-arrow-up text-success fs-4 mb-2"></i>
          <h5 class="mb-1">{{getTotalIncome() | number:'1.2-2'}}</h5>
          <p class="text-muted small mb-0">Total Income</p>
        </div>
      </div>
    </div>
    <div class="col-sm-6 col-md-3">
      <div class="card text-center shadow-sm">
        <div class="card-body">
          <i class="fas fa-arrow-down text-danger fs-4 mb-2"></i>
          <h5 class="mb-1">{{getTotalExpense() | number:'1.2-2'}}</h5>
          <p class="text-muted small mb-0">Total Expenses</p>
        </div>
      </div>
    </div>
    <div class="col-sm-6 col-md-3">
      <div class="card text-center shadow-sm">
        <div class="card-body">
          <i class="fas fa-chart-line text-warning fs-4 mb-2"></i>
          <h5 class="mb-1" [class.text-success]="getNetAmount() >= 0" [class.text-danger]="getNetAmount() < 0">
            {{getNetAmount() | number:'1.2-2'}}
          </h5>
          <p class="text-muted small mb-0">Net Amount</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Add Record Form -->
  <div *ngIf="showAddForm" class="card mb-4 shadow-sm">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0"><i class="fas fa-plus me-2"></i>Add New Ledger Record</h5>
      <button class="btn btn-sm btn-outline-secondary" (click)="toggleAddForm()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="card-body">
      <form (ngSubmit)="addLedgerRecord()" #recordForm="ngForm">
        <div class="row g-3">
          <div class="col-md-3">
            <label class="form-label">Type *</label>
            <select class="form-select" [(ngModel)]="newRecord.type" name="recordType" required>
              <option value="">Select Type</option>
              <option *ngFor="let type of types" [value]="type">{{type}}</option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">Transaction Type *</label>
            <select class="form-select" [(ngModel)]="newRecord.transactionType" name="transactionType" required>
              <option value="income">Income</option>
              <option value="expense">Expense</option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">Amount *</label>
            <input type="number" class="form-control" [(ngModel)]="newRecord.amount" name="recordAmount" placeholder="0.00" min="0" step="0.01" required>
          </div>
          <div class="col-md-3">
            <label class="form-label">Date *</label>
            <input type="date" class="form-control" [(ngModel)]="newRecord.date" name="recordDate" required>
          </div>
          <div class="col-md-9">
            <label class="form-label">Description *</label>
            <input type="text" class="form-control" [(ngModel)]="newRecord.description" name="recordDescription" required>
          </div>
          <div class="col-md-3 d-flex align-items-end">
            <button type="submit" class="btn btn-primary w-100" [disabled]="!recordForm.form.valid || loading">
              <i class="fas fa-save me-1"></i> Save
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Filters -->
  <div class="card mb-4 shadow-sm">
    <div class="card-header">
      <h6 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Records</h6>
    </div>
    <div class="card-body">
      <div class="row g-3">
        <div class="col-md-3">
          <label class="form-label">Type</label>
          <select class="form-select form-select-sm" [(ngModel)]="selectedType" (change)="filterByTypeAndDate()">
            <option value="">All Types</option>
            <option *ngFor="let type of types" [value]="type">{{type}}</option>
          </select>
        </div>
        <div class="col-md-3">
          <label class="form-label">Date From</label>
          <input type="date" class="form-control form-control-sm" [(ngModel)]="ledgerDateFrom" (change)="filterByTypeAndDate()">
        </div>
        <div class="col-md-3">
          <label class="form-label">Date To</label>
          <input type="date" class="form-control form-control-sm" [(ngModel)]="ledgerDateTo" (change)="filterByTypeAndDate()">
        </div>
        <div class="col-md-3 d-flex align-items-end">
          <button class="btn btn-outline-secondary btn-sm w-100" (click)="clearFilters()">
            <i class="fas fa-times me-1"></i> Clear
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Records Table -->
  <div class="card shadow-sm">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0"><i class="fas fa-list me-2"></i>Ledger Records</h5>
      <span class="badge bg-primary">{{records.length}} records</span>
    </div>
    <div class="card-body">
      <div *ngIf="loading" class="text-center py-4">
        <div class="spinner-border text-primary" role="status"></div>
        <p class="mt-2">Loading records...</p>
      </div>

      <div *ngIf="!loading && records.length > 0" class="table-responsive">
        <table class="table table-hover align-middle">
          <thead class="table-light">
          <tr>
            <th>Date</th>
            <th>Type</th>
            <th>Description</th>
            <th>Transaction</th>
            <th>Amount</th>
            <th>Actions</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let record of records">
            <td>{{record.date | date:'mediumDate'}}</td>
            <td><span class="badge bg-secondary">{{record.type}}</span></td>
            <td>{{record.description}}</td>
            <td>
                <span class="badge"
                      [ngClass]="record.transactionType === 'income' ? 'bg-success' : 'bg-danger'">
                  <i class="fas me-1" [ngClass]="record.transactionType === 'income' ? 'fa-arrow-up' : 'fa-arrow-down'"></i>
                  {{record.transactionType | titlecase}}
                </span>
            </td>
            <td [class.text-success]="record.transactionType === 'income'"
                [class.text-danger]="record.transactionType === 'expense'">
              {{record.amount | number:'1.2-2'}}
            </td>
            <td>
              <button class="btn btn-outline-danger btn-sm" (click)="deleteLedgerRecord(record)">
                <i class="fas fa-trash"></i>
              </button>
            </td>
          </tr>
          </tbody>
        </table>
      </div>

      <div *ngIf="!loading && records.length === 0" class="text-center py-5 text-muted">
        <i class="fas fa-book-open fa-3x mb-3"></i>
        <h5>No Records Found</h5>
        <p class="small">Try adjusting filters or add a new record.</p>
      </div>
    </div>
  </div>

  <!-- Today's Records -->
  <div *ngIf="todayRecords.length > 0" class="card mt-4 shadow-sm">
    <div class="card-header">
      <h6 class="mb-0"><i class="fas fa-calendar-day me-2"></i>Today's Transactions</h6>
    </div>
    <div class="card-body">
      <div class="row g-3">
        <div class="col-md-6" *ngFor="let record of todayRecords">
          <div class="d-flex justify-content-between border rounded p-2 bg-light">
            <div>
              <strong class="d-block">{{record.type}}</strong>
              <small class="text-muted">{{record.description}}</small>
            </div>
            <div [class.text-success]="record.transactionType === 'income'"
                 [class.text-danger]="record.transactionType === 'expense'">
              {{record.amount | number:'1.2-2'}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
