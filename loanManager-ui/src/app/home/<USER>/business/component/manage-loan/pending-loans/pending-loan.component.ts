import {Component, OnInit} from '@angular/core';
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {Loan} from "../../../model/loan";
import {LoanService} from "../../../service/loanService";
import {NotificationService} from "../../../../../core/service/notification.service";

@Component({
  standalone: false,
  selector: 'app-manage-loan',
  templateUrl: './pending-loan.component.html',
  styleUrls: ['./pending-loan.component.css']
})
export class PendingLoanComponent implements OnInit {

  modalRef: BsModalRef;
  searchKey: string;
  loans: Array<Loan> = [];
  selectedLoan: Loan;
  rejectionReason: string = '';
  showRejectModal: boolean = false;

  selectedRow: number;

  page;
  collectionSize;
  pageSize;

  constructor(private loanService: LoanService, private notificationService: NotificationService,
              private modalService: BsModalService) {
  }

  ngOnInit(): void {
    this.page = 0;
    this.pageSize = 10;
    this.searchKey = "";
    this.loadLoans();
  }

  loadLoans() {
    this.loanService.getAllPendingLoans(this.page, this.pageSize).subscribe({
      next: (data: any) => {
        this.loans = data?.content || [];
        this.collectionSize = data?.totalElements || 0;
      },
      error: (error) => {
        console.error('Error loading pending loans:', error);
        this.notificationService.showError('Failed to load pending loans');
        this.loans = [];
      }
    });
  }

  selectRow(loan: Loan, i: number) {
    this.selectedLoan = loan;
    this.selectedRow = i;
  }

  setSelectedLoans(event) {
    this.loans = [event.item];
  }

  approveLoan() {
    if (this.selectedLoan !== null) {
      this.loanService.approveLoan(this.selectedLoan.loanNo).subscribe({
        next: (data: any) => {
          if (data == true || data === "Loan approved successfully") {
            this.notificationService.showSuccess("Loan approved successfully");
            this.selectedLoan = null;
            this.selectedRow = null;
            this.loadLoans();
          } else {
            this.notificationService.showError("Failed to approve loan");
          }
        },
        error: (error) => {
          console.error('Error approving loan:', error);
          this.notificationService.showError("Error approving loan");
        }
      });
    }
  }

  showRejectDialog() {
    if (this.selectedLoan !== null) {
      this.rejectionReason = '';
      this.showRejectModal = true;
    }
  }

  confirmRejectLoan() {
    if (this.selectedLoan !== null && this.rejectionReason.trim() !== '') {
      this.loanService.rejectLoan(this.selectedLoan.loanNo, this.rejectionReason.trim()).subscribe({
        next: (data: any) => {
          if (data == true || data === "Loan rejected successfully") {
            this.notificationService.showSuccess("Loan rejected successfully");
            this.selectedLoan = null;
            this.selectedRow = null;
            this.showRejectModal = false;
            this.rejectionReason = '';
            this.loadLoans();
          } else {
            this.notificationService.showError("Failed to reject loan");
          }
        },
        error: (error) => {
          console.error('Error rejecting loan:', error);
          this.notificationService.showError("Error rejecting loan");
        }
      });
    } else {
      this.notificationService.showError("Please provide a rejection reason");
    }
  }

  cancelReject() {
    this.showRejectModal = false;
    this.rejectionReason = '';
  }

  pageChanged(event: any) {
    this.page = event.page - 1; // Convert to 0-based index
    this.loadLoans();
  }

}
