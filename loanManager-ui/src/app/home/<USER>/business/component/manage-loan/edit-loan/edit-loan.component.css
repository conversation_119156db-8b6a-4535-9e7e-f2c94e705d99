.form-label {
  font-weight: 600;
  color: #495057;
}

.form-control:focus,
.form-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.list-group-item:hover {
  background-color: #f8f9fa;
}

.border-bottom {
  border-bottom: 2px solid #dee2e6 !important;
}

.bg-light {
  background-color: #f8f9fa !important;
}

.text-primary {
  color: #0d6efd !important;
}

.text-warning {
  color: #ffc107 !important;
}

.btn:disabled {
  opacity: 0.6;
}

.alert {
  border: none;
  border-radius: 0.5rem;
}

.card {
  border-radius: 0.75rem;
}

.card-header {
  border-radius: 0.75rem 0.75rem 0 0 !important;
}

.form-control[readonly] {
  background-color: #f8f9fa;
  opacity: 1;
}

.input-group-text {
  background-color: #e9ecef;
  border-color: #ced4da;
  font-weight: 500;
}

.badge {
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
}

.section-divider {
  border-top: 1px solid #dee2e6;
  margin: 2rem 0;
}

.clickable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.form-section {
  background: #ffffff;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e9ecef;
}

.required-field::after {
  content: " *";
  color: #dc3545;
}

.field-help {
  font-size: 0.875rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 0.5rem;
}

.status-current {
  background-color: #28a745;
}

.status-pending {
  background-color: #ffc107;
}

.status-issued {
  background-color: #17a2b8;
}

.status-arrears {
  background-color: #dc3545;
}

.status-settled {
  background-color: #6c757d;
}
