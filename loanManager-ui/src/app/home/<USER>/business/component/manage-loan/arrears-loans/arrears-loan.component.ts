import {Component, OnInit} from '@angular/core';
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {Loan} from "../../../model/loan";
import {LoanService} from "../../../service/loanService";
import {LoanDetailsComponent} from "../../loan-details/loan-details.component";

@Component({
  standalone: false,
  selector: 'app-manage-loan',
  templateUrl: './arrears-loan.component.html',
  styleUrls: ['./arrears-loan.component.css']
})
export class ArrearsLoanComponent implements OnInit {

  modalRef: BsModalRef;
  searchKey: string;
  loans: Array<Loan> = [];
  selectedLoan: Loan;

  selectedRow: number;

  page;
  collectionSize;
  pageSize;

  constructor(private loanService: LoanService, private modalService: BsModalService) {
  }

  ngOnInit(): void {
    this.page = 0;
    this.pageSize = 10;
    this.searchKey = "";
    this.loadLoans();
  }

  loadLoans() {
    this.loanService.getAllArrearsLoans(this.page, this.pageSize).subscribe({
      next: (data: any) => {
        this.loans = data?.content || [];
        this.collectionSize = data?.totalElements || 0;
      },
      error: (error) => {
        console.error('Error loading arrears loans:', error);
        this.loans = [];
        this.collectionSize = 0;
      }
    });
  }

  selectRow(loan: Loan, i: number) {
    this.selectedLoan = loan;
    this.selectedRow = i;
  }

  setSelectedLoans(event){
    this.loans = [event.item];
  }

  loansDetail() {
    if (this.selectedLoan !== null) {
      this.modalRef = this.modalService.show(LoanDetailsComponent, <ModalOptions>{class: 'modal-lg'});
      this.modalRef.content.loan = this.selectedLoan;
    }
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.loadLoans();
  }

}
