# Edit Loan Component

## Overview
The Edit Loan Component is a comprehensive administrative tool that allows authorized users to modify all important properties of loan records. This component provides a powerful interface for loan management with proper validation and error handling.

## Features

### 1. **Loan Search**
- Search loans by loan number
- Real-time validation and feedback
- Loading indicators during search

### 2. **Borrower Management**
- Search and replace borrower by NIC
- Search borrowers by name with multiple results
- Display current borrower information
- Easy borrower selection from search results

### 3. **Loan Properties Editing**
- **Basic Information**: Loan number, issue date, status
- **Financial Details**: Loan amount, installment amount, total amount with interest
- **Payment Information**: Current balance, paid amount, installments left
- **Status Management**: Change loan status with automatic status code setting
- **Advanced Fields**: Arrears amount, over-paid amount, next payment date, settlement date

### 4. **Status Code Management**
Automatically sets correct status codes when status is changed:
- Pending → `1`
- Issued → `2`
- Current → `3` (also sets next payment date)
- Arrears → `4`
- Settled → `5` (also sets active to false)

### 5. **Validation & Safety**
- Form validation for required fields
- Unsaved changes detection and warning
- Confirmation dialogs for destructive actions
- Reset functionality to discard changes
- Real-time calculation of loan amounts

### 6. **User Experience**
- Clean, organized interface with collapsible sections
- Loading indicators and progress feedback
- Error handling with user-friendly messages
- Advanced fields toggle for power users
- Responsive design

## Usage

### Accessing the Component
Navigate to: `/business/edit_loan`

### Basic Workflow
1. **Search for Loan**: Enter loan number and click search
2. **Review Current Data**: Examine all loan properties
3. **Make Changes**: Modify any editable fields
4. **Save Changes**: Click "Save Changes" to update the loan
5. **Reset if Needed**: Use "Reset Changes" to discard modifications

### Advanced Features
- **Toggle Advanced Fields**: Click "Show Advanced" to access additional properties
- **Borrower Search**: Use NIC or name search to change the borrower
- **Status Changes**: Changing status automatically updates related fields
- **Amount Calculations**: Loan plan changes automatically recalculate amounts

## Security Considerations
- This component provides powerful editing capabilities
- Should only be accessible to authorized administrators
- All changes are logged through the backend audit system
- Validation prevents invalid data entry

## Technical Details
- Built with Angular reactive forms
- Uses existing loan service endpoints
- Implements proper error handling and user feedback
- Follows established UI/UX patterns from the application

## Related Components
- **Create Loan**: For creating new loans
- **All Loans**: For viewing and basic loan management
- **Pending Loans**: For loan approval workflow
- **Rejected Loans**: For viewing rejected loan applications

## API Endpoints Used
- `GET /loan/findByLoanNo` - Search loans
- `PUT /loan/update` - Update loan data
- `GET /customer/findByNic` - Search borrowers by NIC
- `GET /customer/findByNameLike` - Search borrowers by name
- `GET /loanPlan/findAll` - Load loan plans
- `GET /metaData/findByCategory` - Load status options
