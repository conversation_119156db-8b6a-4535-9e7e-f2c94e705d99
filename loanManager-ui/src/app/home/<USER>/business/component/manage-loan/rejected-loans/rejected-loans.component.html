<div class="container-fluid p-4 bg-light min-vh-100">
  <!-- Header Section -->
  <div class="bg-white border-start border-5 border-danger rounded shadow-sm p-4 mb-4">
    <div class="d-flex justify-content-between align-items-center mb-2">
      <h2 class="h4 text-danger fw-bold mb-0">
        <i class="fas fa-ban me-2"></i> Rejected Loans
      </h2>
      <button class="btn btn-outline-danger btn-sm" (click)="loadRejectedLoans()">
        <i class="fas fa-sync-alt me-1"></i> Refresh
      </button>
    </div>
    <p class="text-muted mb-0">View and manage rejected loan applications.</p>
  </div>

  <!-- Search Section -->
  <div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-white border-bottom">
      <h6 class="fw-semibold text-secondary mb-0">
        <i class="fas fa-search text-primary me-2"></i> Search Rejected Loans
      </h6>
    </div>
    <div class="card-body">
      <div class="row g-3">
        <!-- Search by Customer Name -->
        <div class="col-md-4">
          <div class="input-group">
            <span class="input-group-text bg-white border-end-0"><i class="fa fa-user text-muted"></i></span>
            <input
              [(ngModel)]="searchCustomerName"
              (keyup.enter)="searchByCustomerName()"
              autocomplete="off"
              class="form-control border-start-0"
              placeholder="Search by Customer Name"
              name="searchCustomerName">
            <button class="btn btn-outline-primary" type="button" (click)="searchByCustomerName()">
              <i class="fa fa-search"></i>
            </button>
          </div>
        </div>

        <!-- Search by Customer NIC -->
        <div class="col-md-4">
          <div class="input-group">
            <span class="input-group-text bg-white border-end-0"><i class="fa fa-id-card text-muted"></i></span>
            <input
              [(ngModel)]="searchCustomerNic"
              (keyup.enter)="searchByCustomerNic()"
              autocomplete="off"
              class="form-control border-start-0"
              placeholder="Search by Customer NIC"
              name="searchCustomerNic">
            <button class="btn btn-outline-primary" type="button" (click)="searchByCustomerNic()">
              <i class="fa fa-search"></i>
            </button>
          </div>
        </div>

        <!-- Search by Telephone -->
        <div class="col-md-4">
          <div class="input-group">
            <span class="input-group-text bg-white border-end-0"><i class="fa fa-phone text-muted"></i></span>
            <input
              [(ngModel)]="searchTelephone"
              (keyup.enter)="searchByTelephone()"
              autocomplete="off"
              class="form-control border-start-0"
              placeholder="Search by Telephone"
              name="searchTelephone">
            <button class="btn btn-outline-primary" type="button" (click)="searchByTelephone()">
              <i class="fa fa-search"></i>
            </button>
          </div>
        </div>
      </div>
      
      <div class="row mt-3">
        <div class="col-md-4">
          <button class="btn btn-outline-secondary w-100" type="button" (click)="clearSearch()">
            <i class="fa fa-times me-2"></i>Clear Search
          </button>
        </div>
        
        <div class="col-md-8" *ngIf="isSearching">
          <div class="alert alert-info mb-0">
            <i class="fa fa-spinner fa-spin me-2"></i>Searching...
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Results Section -->
  <div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
      <h6 class="fw-semibold text-secondary mb-0">
        <i class="fas fa-list text-primary me-2"></i> 
        Rejected Loans ({{collectionSize}} total)
      </h6>
    </div>
    <div class="card-body">
      <!-- Table -->
      <div class="table-responsive">
        <table class="table table-hover align-middle text-center">
          <thead class="table-light">
          <tr>
            <th>Loan No</th>
            <th>Customer Name</th>
            <th>Customer NIC</th>
            <th>Telephone</th>
            <th>Amount</th>
            <th>Loan Plan</th>
            <th>Rejected Date</th>
            <th>Rejected By</th>
            <th>Reason</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let loan of rejectedLoans; let i = index"
              (click)="selectRow(loan, i)"
              [class.table-active]="i === selectedRow"
              class="clickable-row">
            <td><strong>{{loan.loanNo}}</strong></td>
            <td>{{loan.customerName}}</td>
            <td>{{loan.customerNic}}</td>
            <td>{{loan.telephone}}</td>
            <td>{{loan.amount | currency:'LKR':'symbol':'1.2-2'}}</td>
            <td>{{loan.loanPlanName}}</td>
            <td>{{loan.rejectedDate | date:'mediumDate'}}</td>
            <td>{{loan.rejectedBy}}</td>
            <td>
              <span class="text-truncate d-inline-block" style="max-width: 200px;" 
                    [title]="loan.rejectionReason">
                {{loan.rejectionReason}}
              </span>
            </td>
          </tr>
          <tr *ngIf="rejectedLoans.length === 0">
            <td colspan="9" class="text-center text-muted py-4">
              <i class="fas fa-inbox fa-2x mb-2"></i><br>
              No rejected loans found
            </td>
          </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="d-flex justify-content-center my-3" *ngIf="collectionSize > pageSize">
        <pagination class="pagination-sm"
                    [totalItems]="collectionSize"
                    [(ngModel)]="page"
                    [maxSize]="15"
                    [itemsPerPage]="pageSize"
                    [boundaryLinks]="true"
                    (pageChanged)="pageChanged($event)">
        </pagination>
      </div>

      <!-- Selected Loan Details -->
      <div class="card mt-4" *ngIf="selectedLoan">
        <div class="card-header bg-light">
          <h6 class="fw-semibold mb-0">
            <i class="fas fa-info-circle me-2"></i>Rejection Details
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <p><strong>Loan Number:</strong> {{selectedLoan.loanNo}}</p>
              <p><strong>Customer:</strong> {{selectedLoan.customerName}}</p>
              <p><strong>NIC:</strong> {{selectedLoan.customerNic}}</p>
              <p><strong>Telephone:</strong> {{selectedLoan.telephone}}</p>
            </div>
            <div class="col-md-6">
              <p><strong>Amount:</strong> {{selectedLoan.amount | currency:'LKR':'symbol':'1.2-2'}}</p>
              <p><strong>Loan Plan:</strong> {{selectedLoan.loanPlanName}}</p>
              <p><strong>Rejected Date:</strong> {{selectedLoan.rejectedDate | date:'medium'}}</p>
              <p><strong>Rejected By:</strong> {{selectedLoan.rejectedBy}}</p>
            </div>
          </div>
          <div class="row">
            <div class="col-12">
              <p><strong>Rejection Reason:</strong></p>
              <div class="alert alert-danger">
                {{selectedLoan.rejectionReason}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
