<div class="container-fluid p-4 bg-light min-vh-100">
  <!-- Header Section -->
  <div class="bg-white border-start border-5 border-warning rounded shadow-sm p-4 mb-4">
    <div class="d-flex justify-content-between align-items-center mb-2">
      <h2 class="h4 text-warning fw-bold mb-0">
        <i class="fas fa-clock me-2"></i> Pending Loans
      </h2>
      <button class="btn btn-outline-warning btn-sm" (click)="loadLoans()">
        <i class="fas fa-sync-alt me-1"></i> Refresh
      </button>
    </div>
    <p class="text-muted mb-0">Review and manage loan applications awaiting approval.</p>
  </div>

  <!-- Card -->
  <div class="card shadow-sm">
    <div class="card-body">
      <!-- Search -->
      <div class="mb-4">
        <div class="input-group">
          <span class="input-group-text bg-white border-end-0">
            <i class="fa fa-filter text-muted"></i>
          </span>
          <input [(ngModel)]="searchKey"
                 [typeahead]="loans"
                 (typeaheadLoading)="loadLoans()"
                 (typeaheadOnSelect)="setSelectedLoans($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadOptionField="name"
                 autocomplete="off"
                 placeholder="Search By Telephone"
                 class="form-control border-start-0"
                 name="loans">
        </div>
      </div>

      <!-- Table -->
      <div class="table-responsive">
        <table class="table table-hover align-middle text-center">
          <thead class="table-light">
          <tr>
            <th>Borrower Name</th>
            <th>Loan Plan</th>
            <th>Loan Amount</th>
            <th>Loan Balance</th>
            <th>Status</th>
            <th>Loan Date</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let loan of loans; let i = index"
              (click)="selectRow(loan, i)"
              [class.table-active]="i === selectedRow"
              class="clickable-row">
            <td><strong>{{loan.borrower?.name || 'N/A'}}</strong></td>
            <td>{{loan.loanPlan?.name || 'N/A'}}</td>
            <td>{{loan.loanAmount | number:'1.2-2'}}</td>
            <td>{{loan.balance | number:'1.2-2'}}</td>
            <td>
              <span class="badge bg-warning text-dark">{{loan.status?.value || 'N/A'}}</span>
            </td>
            <td>{{loan.dateTime | date:'mediumDate'}}</td>
          </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="d-flex justify-content-center my-3">
        <pagination class="pagination-sm"
                    [totalItems]="collectionSize"
                    [(ngModel)]="page"
                    [maxSize]="15"
                    [itemsPerPage]="pageSize"
                    [boundaryLinks]="true"
                    (pageChanged)="pageChanged($event)">
        </pagination>
      </div>

      <!-- Action Buttons -->
      <div class="d-flex justify-content-end gap-2 mt-4">
        <button class="btn btn-danger"
                type="button"
                (click)="showRejectDialog()"
                [disabled]="selectedRow === null">
          <i class="fas fa-times me-1"></i> Reject
        </button>
        <button class="btn btn-success"
                type="button"
                (confirm)="approveLoan()"
                mwlConfirmationPopover
                [disabled]="selectedRow === null">
          <i class="fas fa-check me-1"></i> Approve
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Rejection Reason Modal -->
<div class="modal fade" [class.show]="showRejectModal" [style.display]="showRejectModal ? 'block' : 'none'"
     tabindex="-1" role="dialog" aria-labelledby="rejectModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header bg-danger text-white">
        <h5 class="modal-title" id="rejectModalLabel">
          <i class="fas fa-times-circle me-2"></i>Reject Loan Application
        </h5>
        <button type="button" class="btn-close btn-close-white" (click)="cancelReject()" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="alert alert-warning">
          <i class="fas fa-exclamation-triangle me-2"></i>
          <strong>Warning:</strong> This action will permanently reject the loan application and move it to the rejected loans collection.
        </div>

        <div *ngIf="selectedLoan" class="mb-3">
          <h6 class="fw-bold">Loan Details:</h6>
          <ul class="list-unstyled">
            <li><strong>Loan No:</strong> {{selectedLoan.loanNo}}</li>
            <li><strong>Borrower:</strong> {{selectedLoan.borrower?.name}}</li>
            <li><strong>Amount:</strong> {{selectedLoan.loanAmount | currency:'LKR':'symbol':'1.2-2'}}</li>
          </ul>
        </div>

        <div class="mb-3">
          <label for="rejectionReason" class="form-label">
            <strong>Rejection Reason <span class="text-danger">*</span></strong>
          </label>
          <textarea
            id="rejectionReason"
            class="form-control"
            rows="4"
            [(ngModel)]="rejectionReason"
            placeholder="Please provide a detailed reason for rejecting this loan application..."
            maxlength="500">
          </textarea>
          <div class="form-text">{{rejectionReason.length}}/500 characters</div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="cancelReject()">
          <i class="fas fa-times me-1"></i>Cancel
        </button>
        <button type="button"
                class="btn btn-danger"
                (click)="confirmRejectLoan()"
                [disabled]="!rejectionReason || rejectionReason.trim().length < 10">
          <i class="fas fa-ban me-1"></i>Reject Loan
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal backdrop -->
<div class="modal-backdrop fade" [class.show]="showRejectModal" *ngIf="showRejectModal"></div>
