<div class="container-fluid p-4 bg-light min-vh-100">
  <!-- Header -->
  <div class="bg-white border-start border-5 border-danger rounded shadow-sm p-4 mb-4">
    <div class="d-flex justify-content-between align-items-center mb-2">
      <h2 class="h4 text-danger fw-bold mb-0">
        <i class="fas fa-exclamation-triangle me-2"></i> Arrears Loans
      </h2>
      <button class="btn btn-outline-danger btn-sm" (click)="loadLoans()">
        <i class="fas fa-sync-alt me-1"></i> Refresh
      </button>
    </div>
    <p class="text-muted mb-0">Manage loans that are overdue and require immediate attention.</p>
  </div>

  <!-- Card Container -->
  <div class="card shadow-sm">
    <div class="card-body">
      <!-- Search -->
      <div class="mb-4">
        <div class="input-group">
          <span class="input-group-text bg-white border-end-0">
            <i class="fa fa-filter text-muted"></i>
          </span>
          <input [(ngModel)]="searchKey"
                 [typeahead]="loans"
                 (typeaheadLoading)="loadLoans()"
                 (typeaheadOnSelect)="setSelectedLoans($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadOptionField="name"
                 autocomplete="off"
                 placeholder="Search By NIC"
                 class="form-control border-start-0"
                 name="loans">
        </div>
      </div>

      <!-- Table -->
      <div class="table-responsive">
        <table class="table table-hover align-middle text-center">
          <thead class="table-light">
          <tr>
            <th>Borrower Name</th>
            <th>Loan Plan</th>
            <th>Loan Amount</th>
            <th>Loan Balance</th>
            <th>Status</th>
            <th>Loan Date</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let loan of loans; let i = index"
              (click)="selectRow(loan, i)"
              [class.table-active]="i === selectedRow"
              class="clickable-row">
            <td><strong>{{loan.borrower?.name || 'N/A'}}</strong></td>
            <td>{{loan.loanPlan?.name || 'N/A'}}</td>
            <td>{{loan.loanAmount | number:'1.2-2'}}</td>
            <td>{{loan.balance | number:'1.2-2'}}</td>
            <td>
              <span class="badge bg-danger">{{loan.status?.value || 'N/A'}}</span>
            </td>
            <td>{{loan.dateTime | date:'mediumDate'}}</td>
          </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="d-flex justify-content-center my-3">
        <pagination class="pagination-sm"
                    [totalItems]="collectionSize"
                    [(ngModel)]="page"
                    [maxSize]="15"
                    [itemsPerPage]="pageSize"
                    [boundaryLinks]="true"
                    (pageChanged)="pageChanged($event)">
        </pagination>
      </div>

      <!-- Action Button -->
      <div class="text-end mt-4">
        <button class="btn btn-primary" type="button" (click)="loansDetail()"
                [disabled]="selectedRow === null">
          <i class="fas fa-info-circle me-1"></i> Loan Details
        </button>
      </div>
    </div>
  </div>
</div>
