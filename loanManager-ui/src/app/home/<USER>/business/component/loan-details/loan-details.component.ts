import {Component, OnInit} from '@angular/core';
import {Loan} from "../../model/loan";
import {BsModalRef, BsModalService} from "ngx-bootstrap/modal";
import {<PERSON><PERSON><PERSON>} from "../../../borrower/model/borrower";
import {LoanPlan} from "../../model/loanPlan";
import {MetaData} from "../../../../core/model/metaData";
import {LoanRecord} from "../../model/loanRecord";
import {LoanRecordService} from "../../service/loanRecord.service";

@Component({
  standalone: false,
  selector: 'app-create-loan',
  templateUrl: './loan-details.component.html',
  styleUrls: ['./loan-details.component.css']
})
export class LoanDetailsComponent implements OnInit {

  loan: Loan;
  modalRef: BsModalRef;
  records: Array<LoanRecord> = [];

  paidAmount: number;
  installmentTotal: number;
  recCount: number;
  overPaidAmount: number;
  overPaidSettledAmount: number;

  constructor(private modalService: BsModalService, private loanRecordService: LoanRecordService) {

  }

  ngOnInit(): void {
    this.loan = new Loan();
    this.loan.borrower = new Borrower();
    this.loan.loanPlan = new LoanPlan();
    this.loan.status = new MetaData();
  }

  findLoanRecord() {
    this.loanRecordService.findRecordsByLoanNo(this.loan.loanNo).subscribe((data: any) => {
      this.records = data;
      this.calcPaidAmount();
    });
  }

  calcPaidAmount() {
    this.paidAmount = 0;
    this.installmentTotal = 0;
    this.recCount = 0;
    this.overPaidAmount = 0;
    this.overPaidSettledAmount = 0;
    for (let rec of this.records) {
      this.recCount++;
      this.paidAmount = this.paidAmount + rec.paidAmount;
      this.installmentTotal = this.installmentTotal + rec.installmentAmount;
      this.overPaidSettledAmount = this.overPaidSettledAmount + rec.paidAmountByOverPaid;
      this.overPaidAmount = this.overPaidAmount + (rec.paidAmount > 0 ? rec.balance * -1 : 0);
    }
  }

  close() {
    this.modalRef.hide();
  }

}
