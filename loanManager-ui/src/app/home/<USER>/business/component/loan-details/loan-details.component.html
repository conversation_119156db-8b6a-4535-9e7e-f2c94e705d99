<div class="card shadow-sm border-0">
  <div class="card-header bg-primary text-white fw-semibold">
    <PERSON><PERSON>ail
  </div>
  <div class="card-body">
    <form #loanForm="ngForm">
      <div class="row g-3">
        <div class="col-md-3">
          <label for="loanCustomerName" class="form-label">Borrower</label>
          <input [ngModel]="loan.borrower.name" autocomplete="off" name="loanCustomerName" id="loanCustomerName" class="form-control">
        </div>
        <div class="col-md-3">
          <label for="loanCustomerTp" class="form-label">Borrower T.P</label>
          <input [ngModel]="loan.borrower.telephone1" autocomplete="off" name="loanCustomerTp" id="loanCustomerTp" class="form-control">
        </div>
        <div class="col-md-3">
          <label for="loanPlan" class="form-label">Loan Plan</label>
          <input [ngModel]="loan.loanPlan.name" autocomplete="off" name="loanPlan" id="loanPlan" class="form-control">
        </div>
        <div class="col-md-3">
          <label for="loanStatus" class="form-label">Loan Status</label>
          <input [ngModel]="loan.status.value" autocomplete="off" name="loanStatus" id="loanStatus" class="form-control">
        </div>
        <div class="col-md-3">
          <label for="issueDate" class="form-label">Issued Date</label>
          <input type="date" class="form-control" id="issueDate" [ngModel]="loan.dateTime" name="issueDate">
        </div>
        <div class="col-md-3">
          <label for="loanAmount" class="form-label">Loan Amount</label>
          <input type="text" class="form-control" id="loanAmount" [ngModel]="loan.loanAmount | number: '1.2-2'" name="loanAmount">
        </div>
        <div class="col-md-3">
          <label for="installment" class="form-label">Installment Amount (Rs)</label>
          <input type="text" class="form-control" id="installment" [ngModel]="loan.installmentAmount | number: '1.2-2'" name="installment">
        </div>
        <div class="col-md-3">
          <label for="loanAmountWithInterest" class="form-label">Loan Amount With Interest</label>
          <input type="text" class="form-control" id="loanAmountWithInterest" [ngModel]="loan.loanAmountWithInterest | number: '1.2-2'" name="loanAmountWithInterest">
        </div>
        <div class="col-md-2">
          <label for="paidAmount" class="form-label">Paid Amount</label>
          <input type="text" class="form-control" id="paidAmount" [ngModel]="loan.paidAmount | number: '1.2-2'" name="paidAmount">
        </div>
        <div class="col-md-2">
          <label for="arrearsAmount" class="form-label">Arrears Amount</label>
          <input type="text" class="form-control" id="arrearsAmount" [ngModel]="loan.arrearsAmount | number: '1.2-2'" name="arrearsAmount">
        </div>
        <div class="col-md-2">
          <label for="overPaidAmount" class="form-label">Over paid Amount</label>
          <input type="text" class="form-control" id="overPaidAmount" [ngModel]="loan.overPaidAmount | number: '1.2-2'" name="overPaidAmount">
        </div>
        <div class="col-md-2">
          <label for="settlementDate" class="form-label">Settlement Date</label>
          <input type="text" class="form-control" id="settlementDate" [ngModel]="loan.settlementDate" name="settlementDate">
        </div>
        <div class="col-md-2">
          <label for="installmentLeft" class="form-label">No of Installment Left</label>
          <input type="text" class="form-control" id="installmentLeft" [ngModel]="loan.installmentLeft" name="installmentLeft">
        </div>
        <div class="col-md-2">
          <label for="balance" class="form-label">Balance</label>
          <input type="text" class="form-control" id="balance" name="balance" [ngModel]="loan.balance">
        </div>
      </div>

      <div class="mt-4">
        <div class="table-responsive">
          <table class="table table-bordered table-sm text-center align-middle">
            <thead class="table-light">
            <tr>
              <th scope="col">Date</th>
              <th scope="col">Installment</th>
              <th scope="col">Payment</th>
              <th scope="col">Balance</th>
              <th scope="col">Over paid Share</th>
              <th scope="col">Date Due</th>
              <th scope="col">Status</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let rec of records, let i = index"
                [ngClass]="{
                    'table-warning': rec.status.name === '1',
                    'table-success': rec.status.name === '3',
                    'table-danger': rec.status.name === '2'
                  }">
              <td>{{ rec.installmentDate }}</td>
              <td>{{ rec.installmentAmount | number: '1.2-2' }}</td>
              <td>{{ rec.paidAmount | number: '1.2-2' }}</td>
              <td>{{ rec.balance | number: '1.2-2' }}</td>
              <td>{{ rec.paidAmountByOverPaid | number: '1.2-2' }}</td>
              <td>{{ rec.daysDue }}</td>
              <td>{{ rec.status.value }}</td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="mt-3">
        <div class="d-flex flex-wrap gap-3">
          <span class="badge bg-secondary">Record Count: {{ recCount }}</span>
          <span class="badge bg-success">Paid Amount: {{ paidAmount }}</span>
          <span class="badge bg-info text-dark">Installment Total: {{ installmentTotal }}</span>
          <span class="badge bg-warning text-dark">Over Paid Total: {{ overPaidAmount }}</span>
          <span class="badge bg-dark">Total Settled by O.P: {{ overPaidSettledAmount }}</span>
        </div>
      </div>
    </form>
  </div>
</div>
