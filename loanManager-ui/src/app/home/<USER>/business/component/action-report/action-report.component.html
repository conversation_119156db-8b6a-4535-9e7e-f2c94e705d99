<div class="container-fluid py-4">
  <!-- Header Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-start flex-wrap gap-3">
        <div>
          <h2 class="h4 fw-semibold mb-1">Action Report</h2>
          <p class="text-muted mb-0 small">View and audit all system actions performed by users.</p>
        </div>
        <div class="d-flex flex-wrap gap-2">
          <button class="btn btn-outline-primary btn-sm" (click)="loadActionLogs()" [disabled]="loading">
            <i class="fas fa-sync-alt me-1" [class.fa-spin]="loading"></i> Refresh
          </button>
          <button class="btn btn-success btn-sm" (click)="exportToCSV()" [disabled]="actionLogs.length === 0">
            <i class="fas fa-download me-1"></i> Export CSV
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-light">
          <h6 class="mb-0 text-primary">
            <i class="fas fa-filter me-2"></i>Filters
          </h6>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-3">
              <label class="form-label">User</label>
              <input
                type="text"
                class="form-control"
                [(ngModel)]="selectedUser"
                placeholder="Enter username">
            </div>
            <div class="col-md-3">
              <label class="form-label">Action Type</label>
              <select class="form-select" [(ngModel)]="selectedAction">
                <option value="">All Actions</option>
                <option *ngFor="let action of availableActions" [value]="action">
                  {{action.replace('/_/g', ' ')}}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">From Date</label>
              <input
                type="date"
                class="form-control"
                [(ngModel)]="fromDate">
            </div>
            <div class="col-md-2">
              <label class="form-label">To Date</label>
              <input
                type="date"
                class="form-control"
                [(ngModel)]="toDate">
            </div>
            <div class="col-md-2 d-flex align-items-end">
              <div class="d-flex gap-2 w-100">
                <button class="btn btn-primary btn-sm" (click)="applyFilters()">
                  <i class="fas fa-search me-1"></i> Apply
                </button>
                <button class="btn btn-outline-secondary btn-sm" (click)="clearFilters()">
                  <i class="fas fa-times me-1"></i> Clear
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Action Logs Table -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
          <h6 class="mb-0 text-primary">
            <i class="fas fa-list me-2"></i>Action Logs
          </h6>
          <span class="badge bg-info">{{totalElements}} total records</span>
        </div>
        <div class="card-body p-0">
          <!-- Loading State -->
          <div *ngIf="loading" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading action logs...</p>
          </div>

          <!-- Action Logs Table -->
          <div *ngIf="!loading && actionLogs.length > 0" class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>Date/Time</th>
                  <th>Action</th>
                  <th>Description</th>
                  <th>Performed By</th>
                  <th>Details</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let log of actionLogs">
                  <td class="text-nowrap">
                    <small>{{formatDate(log.performedAt)}}</small>
                  </td>
                  <td>
                    <span [class]="getActionBadgeClass(log.action)">
                      {{log.action.replace('/_/g', ' ')}}
                    </span>
                  </td>
                  <td>
                    <span class="text-truncate d-inline-block" style="max-width: 300px;"
                          [title]="log.description">
                      {{log.description}}
                    </span>
                  </td>
                  <td>
                    <strong>{{log.performedBy}}</strong>
                  </td>
                  <td>
                    <button
                      *ngIf="log.details"
                      class="btn btn-outline-info btn-sm"
                      (click)="viewDetails(log)"
                      title="View Details">
                      <i class="fas fa-eye"></i>
                    </button>
                    <span *ngIf="!log.details" class="text-muted">-</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- No Data -->
          <div *ngIf="!loading && actionLogs.length === 0" class="text-center py-5 text-muted">
            <i class="fas fa-clipboard-list fa-3x mb-3"></i>
            <h5>No Action Logs Found</h5>
            <p class="small">No actions match your current filters.</p>
          </div>

          <!-- Pagination -->
          <div *ngIf="!loading && actionLogs.length > 0 && totalPages > 1" class="d-flex justify-content-center p-3">
            <pagination
              [totalItems]="totalElements"
              [(ngModel)]="page"
              [maxSize]="5"
              [itemsPerPage]="pageSize"
              [rotate]="false"
              [boundaryLinks]="true"
              (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
