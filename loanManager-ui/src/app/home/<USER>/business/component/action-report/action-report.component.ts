import { Component, OnInit } from '@angular/core';
import { ActionLogService, ActionLog } from '../../service/action-log.service';
import { NotificationService } from '../../../../core/service/notification.service';

@Component({
  standalone: false,
  selector: 'app-action-report',
  templateUrl: './action-report.component.html',
  styleUrls: ['./action-report.component.css']
})
export class ActionReportComponent implements OnInit {

  actionLogs: ActionLog[] = [];
  loading: boolean = false;

  // Pagination
  page: number = 0;
  pageSize: number = 20;
  totalElements: number = 0;
  totalPages: number = 0;

  // Filters
  selectedUser: string = '';
  selectedAction: string = '';
  fromDate: string = '';
  toDate: string = '';

  // Available actions for filter
  availableActions: string[] = [
    'REMOVE_UNPAID_LOAN_RECORDS',
    'LOAN_PLAN_CHANGE',
    'LOAN_EDIT',
    'USER_LOGIN',
    'USER_LOGOUT',
    'LOAN_APPROVAL',
    'LOAN_REJECTION',
    'PAYMENT_AMENDMENT'
  ];

  constructor(
    private actionLogService: ActionLogService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.loadActionLogs();
  }

  loadActionLogs() {
    this.loading = true;

    if (this.fromDate && this.toDate) {
      this.loadByDateRange();
    } else if (this.selectedUser) {
      this.loadByUser();
    } else if (this.selectedAction) {
      this.loadByAction();
    } else {
      this.loadAll();
    }
  }

  private loadAll() {
    this.actionLogService.getActionLogs(this.page, this.pageSize).subscribe({
      next: (response: any) => {
        this.actionLogs = response.content || [];
        this.totalElements = response.totalElements || 0;
        this.totalPages = response.totalPages || 0;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading action logs:', error);
        this.notificationService.showError('Failed to load action logs');
        this.loading = false;
      }
    });
  }

  private loadByUser() {
    this.actionLogService.getActionLogsByUser(this.selectedUser, this.page, this.pageSize).subscribe({
      next: (response: any) => {
        this.actionLogs = response.content || [];
        this.totalElements = response.totalElements || 0;
        this.totalPages = response.totalPages || 0;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading action logs by user:', error);
        this.notificationService.showError('Failed to load action logs');
        this.loading = false;
      }
    });
  }

  private loadByAction() {
    this.actionLogService.getActionLogsByAction(this.selectedAction, this.page, this.pageSize).subscribe({
      next: (response: any) => {
        this.actionLogs = response.content || [];
        this.totalElements = response.totalElements || 0;
        this.totalPages = response.totalPages || 0;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading action logs by action:', error);
        this.notificationService.showError('Failed to load action logs');
        this.loading = false;
      }
    });
  }

  private loadByDateRange() {
    this.actionLogService.getActionLogsByDateRange(this.fromDate, this.toDate, this.page, this.pageSize).subscribe({
      next: (response: any) => {
        this.actionLogs = response.content || [];
        this.totalElements = response.totalElements || 0;
        this.totalPages = response.totalPages || 0;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading action logs by date range:', error);
        this.notificationService.showError('Failed to load action logs');
        this.loading = false;
      }
    });
  }

  applyFilters() {
    this.page = 0; // Reset to first page
    this.loadActionLogs();
  }

  clearFilters() {
    this.selectedUser = '';
    this.selectedAction = '';
    this.fromDate = '';
    this.toDate = '';
    this.page = 0;
    this.loadActionLogs();
  }

  pageChanged(event: any) {
    this.page = event.page - 1; // Convert to 0-based index
    this.loadActionLogs();
  }

  getActionBadgeClass(action: string): string {
    switch (action) {
      case 'REMOVE_UNPAID_LOAN_RECORDS':
        return 'badge bg-danger';
      case 'LOAN_PLAN_CHANGE':
        return 'badge bg-warning';
      case 'LOAN_EDIT':
        return 'badge bg-info';
      case 'USER_LOGIN':
        return 'badge bg-success';
      case 'USER_LOGOUT':
        return 'badge bg-secondary';
      case 'LOAN_APPROVAL':
        return 'badge bg-success';
      case 'LOAN_REJECTION':
        return 'badge bg-danger';
      case 'PAYMENT_AMENDMENT':
        return 'badge bg-warning';
      default:
        return 'badge bg-primary';
    }
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleString();
  }

  exportToCSV() {
    // Simple CSV export functionality
    const headers = ['Date/Time', 'Action', 'Description', 'Performed By'];
    const csvContent = [
      headers.join(','),
      ...this.actionLogs.map(log => [
        this.formatDate(log.performedAt),
        log.action,
        `"${log.description}"`,
        log.performedBy
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `action-report-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  }

  viewDetails(log: ActionLog) {
    if (log.details) {
      const detailsStr = JSON.stringify(log.details, null, 2);
      alert(`Action Details:\n\n${detailsStr}`);
    } else {
      this.notificationService.showInfo('No additional details available for this action');
    }
  }
}
