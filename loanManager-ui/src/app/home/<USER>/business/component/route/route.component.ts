import {Component, OnInit} from '@angular/core';
import {Route} from "../../model/route";
import {RouteService} from "../../service/route.service";
import {NotificationService} from "../../../../core/service/notification.service";

@Component({
  standalone: false,
  selector: 'app-route',
  templateUrl: './route.component.html',
  styleUrls: ['./route.component.css']
})

export class RouteComponent implements OnInit {

  route = new Route();
  routes: Array<Route> = [];
  selectedRow: number;
  keyRoute: string;
  collectionSize;
  page;
  pageSize;

  constructor(private routeService: RouteService, private notificationService: NotificationService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 8;
    this.route = new Route();
    this.route.active = true;
    this.findAll();
  }

  saveRoute() {
    this.routeService.save(this.route).subscribe(result => {
      if(result == true){
        this.notificationService.showSuccess("Saved Successfully");
        this.ngOnInit();
      }else{
        this.notificationService.showError("Saving Failed");
      }
    }, error => {
      console.log(error);
    });
  }

  loadRoutes() {
    this.routeService.findByName(this.keyRoute).subscribe((data: Array<Route>) => {
      return this.routes = data;
    });
  }

  findAll() {
    this.routeService.findAll(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.routes = data.content;
      this.collectionSize = data.totalPages * 8;
      console.log(this.routes);
    });
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  routeDetail(route, index) {
    this.route = route;
    this.selectedRow = index;
  }

  setSelectedRoute(event) {
    this.route = event.item;
  }

  updateRoute() {
    this.saveRoute();
  }

  clear() {
    this.route = new Route();
  }
}
