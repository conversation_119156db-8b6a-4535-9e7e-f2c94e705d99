<div class="card shadow-sm">
  <div class="card-header bg-primary fw-bold">
    MANAGE ROUTES
  </div>
  <div class="card-body">
    <div class="row">
      <!-- Left Side: Search and Routes Table -->
      <div class="col-md-6 mb-4 mb-md-0">
        <input [(ngModel)]="keyRoute"
               [typeahead]="routes"
               (typeaheadLoading)="loadRoutes()"
               (typeaheadOnSelect)="setSelectedRoute($event)"
               [typeaheadOptionsLimit]="7"
               typeaheadOptionField="name"
               placeholder="Search Route"
               autocomplete="off"
               class="form-control mb-3"
               name="rout"
               size="16"
               required>

        <div class="table-responsive">
          <table class="table table-striped table-hover align-middle">
            <thead>
            <tr class="text-center">
              <th>Name</th>
              <th>Start</th>
              <th>End</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let route of routes; let i = index"
                (click)="routeDetail(route, i)"
                [class.table-active]="i === selectedRow"
                style="cursor:pointer;">
              <td>{{ route.name }}</td>
              <td>{{ route.start }}</td>
              <td>{{ route.end }}</td>
            </tr>
            </tbody>
          </table>
        </div>

        <div class="d-flex justify-content-center mt-3">
          <pagination
            [totalItems]="collectionSize"
            [(ngModel)]="page"
            (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>

      <!-- Right Side: Manage Route Form -->
      <div class="col-md-6">
        <form #manageRouteForm="ngForm" (ngSubmit)="saveRoute(); manageRouteForm.reset()">
          <div class="mb-3">
            <label for="bName" class="form-label">Route Name</label>
            <input type="text" required #bName="ngModel"
                   [class.is-invalid]="bName.invalid && bName.touched"
                   class="form-control" id="bName" [(ngModel)]="route.name" name="bName" placeholder="Route Name">
            <div *ngIf="bName.errors && (bName.invalid || bName.touched)" class="invalid-feedback d-block">
              *{{ 'ROUTE_NAME' | translate }} {{ 'REQUIRED' | translate }}
            </div>
          </div>

          <div class="mb-3">
            <label for="start" class="form-label">Start</label>
            <input required #start="ngModel"
                   [class.is-invalid]="start.invalid && start.touched"
                   class="form-control" id="start" [(ngModel)]="route.start" name="start" placeholder="Start">
            <div *ngIf="start.errors && (start.invalid || start.touched)" class="invalid-feedback d-block">
              *{{ 'START' | translate }} {{ 'REQUIRED' | translate }}
            </div>
          </div>

          <div class="mb-3">
            <label for="end" class="form-label">End</label>
            <input required #end="ngModel"
                   [class.is-invalid]="end.invalid && end.touched"
                   class="form-control" id="end" [(ngModel)]="route.end" name="end" placeholder="End">
            <div *ngIf="end.errors && (end.invalid || end.touched)" class="invalid-feedback d-block">
              *{{ 'END' | translate }} {{ 'REQUIRED' | translate }}
            </div>
          </div>

          <div class="form-check mb-4">
            <input class="form-check-input" type="checkbox" id="check3" name="check3" [(ngModel)]="route.active">
            <label class="form-check-label" for="check3">Active</label>
          </div>

          <div class="d-flex justify-content-end gap-2">
            <button type="button" class="btn btn-warning" (click)="clear()">
              Clear
            </button>
            <button type="button" class="btn btn-primary"
                    [disabled]="!manageRouteForm.form.valid"
                    (click)="updateRoute()">
              Update
            </button>
            <button type="submit" class="btn btn-success" [disabled]="!manageRouteForm.form.valid">
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
