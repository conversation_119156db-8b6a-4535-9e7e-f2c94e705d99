import {environment} from '../../../../environments/environment';

export class BusinessConstants {

  public static API_URL = environment.apiUrl;

  static FIND_ALL_LOAN_PLAN = BusinessConstants.API_URL + "loanPlan/findAll";
  static FIND_LOAN_PLAN_BY_STATUS = BusinessConstants.API_URL + "loanPlan/findByStatus";
  static SAVE_LOAN_PLAN = BusinessConstants.API_URL + "loanPlan/save";

  static SAVE_LOAN = BusinessConstants.API_URL + "loan/save";
  static UPDATE_LOAN = BusinessConstants.API_URL + "loan/update";
  static GET_LOAN_BY_BORROWER_NIC = BusinessConstants.API_URL + "loan/findByBorrowerNic";
  static GET_LOAN_BY_LOAN_NO = BusinessConstants.API_URL + "loan/findByLoanNo";
  static APPROVE_LOAN = BusinessConstants.API_URL + "mobileApp/approveLoan";
  static REJECT_LOAN = BusinessConstants.API_URL + "mobileApp/rejectLoan";
  static GET_ALL_LOANS = BusinessConstants.API_URL + "loan/findAll";
  static GET_ALL_PENDING_LOANS = BusinessConstants.API_URL + "mobileApp/pendingLoans";
  static GET_ALL_ARREARS_LOANS = BusinessConstants.API_URL + "loan/findAllArrears";
  static GET_REJECTED_LOANS = BusinessConstants.API_URL + "mobileApp/rejectedLoans";
  static SEARCH_REJECTED_LOANS = BusinessConstants.API_URL + "mobileApp/rejectedLoans/search";
  static FIND_LOAN_BY_STATUS = BusinessConstants.API_URL + "loan/findLoanByStatus";

  public static FIND_TODAY_PAYMENT_LIST = BusinessConstants.API_URL + 'payment/findTodayPaymentList';
  public static FIND_PAYMENTS_BY_DATE = BusinessConstants.API_URL + 'payment/findPaymentsByDate';
  public static FIND_PAYMENTS_BY_DATE_RANGE = BusinessConstants.API_URL + 'payment/findPaymentsByDateRange';

  public static SAVE_ROUTE = BusinessConstants.API_URL + 'route/save';
  public static GET_ROUTES = BusinessConstants.API_URL + 'route/findAll';
  public static FIND_ALL_ROUTES_FOR_SELECT = BusinessConstants.API_URL + 'route/findAllActiveForSelect';
  public static SEARCH_ROUTE = BusinessConstants.API_URL + 'route/search';

  public static FIND_TODAY_LIST = BusinessConstants.API_URL + 'loanRecord/findTodayList';
  public static FIND_TODAY_LOAN_BY_NAME = BusinessConstants.API_URL + 'loanRecord/findTodayLoanByName';
  public static FIND_TODAY_LOAN_BY_NIC = BusinessConstants.API_URL + 'loanRecord/findTodayLoanByNic';
  public static FIND_ALL_RECORDS_BY_LOAN_NO = BusinessConstants.API_URL + 'loanRecord/findRecordsByLoanNo';
  public static FIND_PENDING_LOAN_RECORDS_BY_LOAN_NO = BusinessConstants.API_URL + 'loanRecord/findPendingRecordsByLoanNo';
  public static FIND_PENDING_LOAN_RECORDS_BY_NIC = BusinessConstants.API_URL + 'loanRecord/findPendingRecordsByNic';
  public static FIND_PENDING_LOAN_RECORDS_BY_TP_1 = BusinessConstants.API_URL + 'loanRecord/findPendingRecordsByTp1';
  public static FIND_ARREARS_LIST = BusinessConstants.API_URL + 'loanRecord/findArrearsList';
  public static FIND_ARREARS_LOAN_BY_NAME = BusinessConstants.API_URL + 'loanRecord/findArrearsLoanByName';
  public static PAY_LOAN_RECORD = BusinessConstants.API_URL + 'loanRecord/payLoanRecord';
  public static CREATE_AND_PAY_LOAN_RECORD = BusinessConstants.API_URL + 'loanRecord/createAndPayLoanRecord';
  public static REMOVE_UNPAID_RECORDS_FOR_DATE = BusinessConstants.API_URL + 'loanRecord/removeUnpaidRecordsForDate';

  // Action Log endpoints
  public static LOG_ACTION = BusinessConstants.API_URL + 'actionLog/save';
  public static GET_ACTION_LOGS = BusinessConstants.API_URL + 'actionLog/findAll';
  public static GET_ACTION_LOGS_BY_USER = BusinessConstants.API_URL + 'actionLog/findByUser';
  public static GET_ACTION_LOGS_BY_DATE_RANGE = BusinessConstants.API_URL + 'actionLog/findByDateRange';
  public static GET_ACTION_LOGS_BY_ACTION = BusinessConstants.API_URL + 'actionLog/findByAction';

}


