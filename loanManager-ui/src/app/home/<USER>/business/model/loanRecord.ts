import {MetaData} from "../../../core/model/metaData";
import {Loan} from "./loan";

export class LoanRecord {
  public id: string;
  public loanNo: string;
  public borrowerName: string;
  public borrowerAddress: string;
  public borrowerNic: string;
  public borrowerTp1: string;
  public borrowerTp2: string;
  public loanPlan: string;
  public routeName: string;
  public installmentDate: Date;
  public dueDate: Date;
  public paidDate: Date;
  public installmentAmount: number;
  public arrearsAmount: number;
  public paidAmountByOverPaid: number;
  public paidAmount: number;
  public balance: number;
  public daysDue: number;
  public status: MetaData;
}
