import {APP_INITIALIZER, NgModule} from '@angular/core';
import {CommonModule, DatePipe} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {HTTP_INTERCEPTORS, HttpClientModule} from '@angular/common/http';
import {JwtInterceptor} from '../../helper/jwt.interceptor';
import {ErrorInterceptor} from '../../helper/error.interceptor';
import {RouterModule} from '@angular/router';
import {routeParams} from './core-routing.module';
import {TranslateService} from '../../translate.service';
import {TranslatePipe} from '../../translate.pipe';

// NgBootstrap modules - with .forRoot() for services
import {PaginationModule} from 'ngx-bootstrap/pagination';
import {TypeaheadModule} from 'ngx-bootstrap/typeahead';
import {ModalModule} from 'ngx-bootstrap/modal';
import {BsDatepickerModule} from 'ngx-bootstrap/datepicker';
import {BsDropdownModule} from 'ngx-bootstrap/dropdown';
import {TimepickerModule} from 'ngx-bootstrap/timepicker';
import {CollapseModule} from 'ngx-bootstrap/collapse';

// Other third-party modules (temporarily commented out incompatible ones)
import {TagInputModule} from 'ngx-chips';
import {ToastrModule} from 'ngx-toastr';
import {ConfirmationPopoverModule} from 'angular-confirmation-popover';
import {NgxPrintModule} from 'ngx-print';
import {NgxDropzoneModule} from 'ngx-dropzone';
// NgxLoadingModule removed - using Bootstrap spinners instead

export function setupTranslateFactory(
  service: TranslateService) {
  return () => {
    // Use the language from localStorage or default to English
    const lang = localStorage.getItem('lang') || 'en';

    // Load the translations
    return service.use(lang).then(() => {
      // Disable translation warnings by default (false parameter)
      service.debugTranslations(false);
    }).catch(error => {
      console.error('Error loading translations:', error);
    });
  };
}

@NgModule({
  declarations: [TranslatePipe, routeParams],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    RouterModule,
    PaginationModule.forRoot(),
    TypeaheadModule.forRoot(),
    ModalModule.forRoot(),
    BsDatepickerModule.forRoot(),
    BsDropdownModule.forRoot(),
    TimepickerModule.forRoot(),
    CollapseModule.forRoot(),
    TagInputModule,
    ToastrModule.forRoot(),
    ConfirmationPopoverModule.forRoot({
      confirmButtonType: 'danger',
      popoverTitle: 'Confirmation',
      popoverMessage: 'Are you sure?'
    }),
    NgxPrintModule,
    NgxDropzoneModule
    // NgxLoadingModule.forRoot({}) - removed in favor of Bootstrap spinners
  ],
  exports: [
    RouterModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    PaginationModule,
    TypeaheadModule,
    ModalModule,
    BsDatepickerModule,
    BsDropdownModule,
    TimepickerModule,
    CollapseModule,
    TagInputModule,
    ToastrModule,
    ConfirmationPopoverModule,
    NgxPrintModule,
    NgxDropzoneModule,
    // NgxLoadingModule removed - using Bootstrap spinners instead
    TranslatePipe
  ],
  providers: [{provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true},
    {provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true},
    TranslateService, DatePipe,
    {
      provide: APP_INITIALIZER,
      useFactory: setupTranslateFactory,
      deps: [TranslateService],
      multi: true
    }
  ]
})

export class CoreModule {
}
