import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {BorrowerConstants} from '../borrower-constants';


@Injectable({
  providedIn: 'root'
})
export class CheckLoanStatusService {

  constructor(private http: HttpClient) {
  }

  public findActiveLoanByNic(nic: string): any {
    return this.http.get(BorrowerConstants.FIND_ACTIVE_LOAN_BY_NIC, {params: {nic: nic}});
  }

  public findByLoanNo(loanNo: string): any {
    return this.http.get(BorrowerConstants.FIND_LOAN_BY_LOAN_NO, {params: {loanNo: loanNo}});
  }

  public findPendingRecordsByLoanNo(loanNo: string): any {
    return this.http.get(BorrowerConstants.FIND_PENDING_LOAN_RECORDS_BY_LOAN_NO, {params: {loanNo: loanNo}});
  }

}
