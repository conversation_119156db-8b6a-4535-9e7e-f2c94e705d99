
import { Injectable } from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Deposit} from '../model/deposit';
import {BorrowerConstants} from '../borrower-constants';

@Injectable({
  providedIn: 'root'
})
export class DepositService {

public  deposit: Deposit;
  constructor(private http:HttpClient) {

  }

  save(deposit) {
    return this.http.post<any>(BorrowerConstants.SAVE_DEPOSIT, deposit);
  }

 public saveAsAngularObject(deposit){
   this.deposit = deposit;

   return  this.deposit;
  }

  public findByCheque(cheque) {
    return this.http.post<any>(BorrowerConstants.FIND_BY_CHEQUE, cheque);
  }
}
