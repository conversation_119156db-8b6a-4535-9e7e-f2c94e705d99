import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {BorrowerConstants} from "../borrower-constants";

@Injectable({
  providedIn: 'root'
})
export class BorrowerService {

  constructor(private http: HttpClient) {
  }

  save(customer) {
    return this.http.post<any>(BorrowerConstants.SAVE_CUSTOMER, customer);
  }

  public findAllPagination(page, pageSize) {
    return this.http.get(BorrowerConstants.FIND_ALL_CUSTOMERS, {params: {page: page, pageSize: pageSize}});
  }

  findById(id: string) {
    return this.http.get(BorrowerConstants.FIND_CUSTOMER_BY_ID, {params: {id: id}});
  }

  findByNameLike(name: string) {
    return this.http.get(BorrowerConstants.FIND_BY_CUSTOMER_NAME, {params: {name: name}});
  }

  findByNic(nic: string) {
    return this.http.get(BorrowerConstants.FIND_BY_CUSTOMER_NIC, {params: {nic: nic}});
  }

  findByTpLike(tp: string) {
    return this.http.get(BorrowerConstants.FIND_BY_CUSTOMER_TELEPHONE, {params: {tp: tp}});
  }

  public checkNic(nic) {
    return this.http.get(BorrowerConstants.CUSTOMER_NIC_CHECK, {params: {nic: nic}});
  }

}
