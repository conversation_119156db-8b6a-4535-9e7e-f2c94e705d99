import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {BorrowerConstants} from '../borrower-constants';


@Injectable({
  providedIn: 'root'
})
export class BankAccountService {

  constructor(private http: HttpClient) {
  }


  save(bankAccount) {
    return this.http.post<any>(BorrowerConstants.SAVE_BANK_ACCOUNT, bankAccount);

  }


  findAllBankAccounts() {
    return this.http.get(BorrowerConstants.GET_ALL_BANK_ACCOUNTS);
  }

  public findAll(page , pageSize){
    return this.http.get(BorrowerConstants.GET_PAGES, {params:{page:page,pageSize:pageSize}});
  }


  findById(bankAccountId: string) {
    return this.http.get(BorrowerConstants.SEARCH_BANK_ACCOUNT, {params: {any: bankAccountId}});
  }

  public findByBankAccountNo (bankAccountNo) {
    return this.http.get(BorrowerConstants.SEARCH_BANK_ACCOUNT_NO, {params: {any: bankAccountNo}});
  }

}
