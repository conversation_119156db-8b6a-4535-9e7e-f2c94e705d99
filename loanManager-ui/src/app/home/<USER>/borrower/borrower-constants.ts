import {environment} from '../../../../environments/environment';

export class BorrowerConstants {

  public static API_URL = environment.apiUrl;

  public static SAVE_BANK_ACCOUNT = BorrowerConstants.API_URL + 'bankAccount/save';
  public static GET_ALL_BANK_ACCOUNTS = BorrowerConstants.API_URL + 'bankAccount/findAllBankAccounts';
  public static SEARCH_BANK_ACCOUNT = BorrowerConstants.API_URL + 'bankAccount/findById';
  public static GET_PAGES = BorrowerConstants.API_URL + 'bankAccount/findAllPage';
  public static SEARCH_BANK_ACCOUNT_NO = BorrowerConstants.API_URL + 'bankAccount/search';

  public static SAVE_DEPOSIT = BorrowerConstants.API_URL + 'deposit/save';

  public static FIND_BY_CHEQUE = BorrowerConstants.API_URL + 'deposit/findByCheque';

  public static SAVE_CUSTOMER = BorrowerConstants.API_URL + 'customer/save';
  public static FIND_CUSTOMER_BY_ID = BorrowerConstants.API_URL + 'customer/findById';
  public static FIND_ALL_CUSTOMERS = BorrowerConstants.API_URL + 'customer/findAll';
  public static FIND_BY_CUSTOMER_NAME = BorrowerConstants.API_URL + 'customer/searchByName';
  public static FIND_BY_CUSTOMER_TELEPHONE = BorrowerConstants.API_URL + 'customer/searchByTp';
  public static FIND_BY_CUSTOMER_NIC = BorrowerConstants.API_URL + 'customer/searchByNic';
  public static CUSTOMER_NIC_CHECK = BorrowerConstants.API_URL + 'customer/checkNic';

  public static FIND_ACTIVE_LOAN_BY_NIC = BorrowerConstants.API_URL + 'mobileApp/findActiveLoanByNic';
  public static FIND_LOAN_BY_LOAN_NO = BorrowerConstants.API_URL + 'mobileApp/findLoanByLoanNo';
  public static FIND_PENDING_LOAN_RECORDS_BY_LOAN_NO = BorrowerConstants.API_URL + 'mobileApp/findPendingRecordsByLoanNo';

}


