import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {NewBorrowerComponent} from './component/new-borrower/new-borrower.component';
import {ManageBorrowerComponent} from './component/manage-borrower/manage-borrower.component';

const routes: Routes = [
  {
    path: 'create_borrower',
    component: NewBorrowerComponent
  },
  {
    path: 'manage_borrower',
    component: ManageBorrowerComponent
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class BorrowerRoutingModule {
}

export const tradeRouteParams = [NewBorrowerComponent, ManageBorrowerComponent];

