<div class="container-fluid py-4">

  <!-- Header Section -->
  <div class="mb-4">
    <div class="d-flex justify-content-between align-items-center mb-2">
      <h2 class="h4 d-flex align-items-center gap-2 mb-0">
        <i class="fas fa-users"></i>
        Manage Borrowers
      </h2>
      <div class="btn-group">
        <button class="btn btn-outline-primary btn-sm" (click)="ngOnInit()" type="button">
          <i class="fas fa-sync-alt"></i> Refresh
        </button>
        <button class="btn btn-success btn-sm" routerLink="../new_borrower" type="button">
          <i class="fas fa-user-plus"></i> New Borrower
        </button>
      </div>
    </div>
    <p class="text-muted mb-0">Search, view, and manage all borrowers in the system.</p>
  </div>

  <!-- Search Card -->
  <div class="card mb-4">
    <div class="card-header bg-primary d-flex align-items-center gap-2">
      <i class="fas fa-search"></i>
      <h6 class="mb-0">Search Borrowers</h6>
    </div>
    <div class="card-body">
      <form class="row g-3">
        <div class="col-md-4">
          <label for="searchByName" class="form-label">Search by Name</label>
          <input
            id="searchByName"
            [(ngModel)]="searchKeyName"
            [typeahead]="customers"
            (typeaheadLoading)="loadCustomerByName()"
            (typeaheadOnSelect)="setSelectedCustomer($event)"
            [typeaheadOptionsLimit]="7"
            typeaheadOptionField="name"
            autocomplete="off"
            placeholder="Enter borrower name"
            name="searchName"
            class="form-control"
          />
        </div>
        <div class="col-md-4">
          <label for="searchByPhone" class="form-label">Search by Phone</label>
          <input
            id="searchByPhone"
            [(ngModel)]="searchKeyTp"
            [typeahead]="customers"
            (typeaheadLoading)="loadCustomerByTp()"
            (typeaheadOnSelect)="setSelectedCustomer($event)"
            [typeaheadOptionsLimit]="7"
            typeaheadOptionField="telephone1"
            autocomplete="off"
            placeholder="Enter phone number"
            name="searchPhone"
            class="form-control"
          />
        </div>
        <div class="col-md-4 d-flex align-items-center">
          <div class="form-check mt-4">
            <input
              class="form-check-input"
              type="checkbox"
              id="showInactive"
              name="showInactive"
              (change)="searchActiveResult($event)"
              [(ngModel)]="active"
            />
            <label class="form-check-label" for="showInactive">Show Inactive Borrowers</label>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Borrowers Table -->
  <div class="card mb-4">
    <div class="card-header bg-primary d-flex justify-content-between align-items-center">
      <h6 class="mb-0 d-flex align-items-center gap-2">
        <i class="fas fa-table"></i>
        Borrowers List
      </h6>
      <span class="badge bg-primary">{{ customers?.length || 0 }} borrowers</span>
    </div>
    <div class="card-body p-0">
      <div class="table-responsive">
        <table class="table table-hover mb-0">
          <thead class="table-light">
          <tr>
            <th>Borrower Name</th>
            <th>NIC</th>
            <th>Contact Numbers</th>
            <th>Address</th>
            <th>Status</th>
          </tr>
          </thead>
          <tbody>
          <tr
            *ngFor="let customer of customers; let i = index"
            (click)="customerDetail(customer,i)"
            [class.table-active]="i === selectedRow"
            style="cursor: pointer"
          >
            <td>
              <strong>{{ customer.name }}</strong>
              <small class="d-block text-muted" *ngIf="customer.email">{{ customer.email }}</small>
            </td>
            <td>{{ customer.nic }}</td>
            <td>
              <div>{{ customer.telephone1 }}</div>
              <small class="text-muted" *ngIf="customer.telephone2">{{ customer.telephone2 }}</small>
            </td>
            <td>{{ customer.address }}</td>
            <td>
                <span
                  class="badge"
                  [ngClass]="customer.active ? 'bg-success' : 'bg-danger'"
                >{{ customer.active ? 'Active' : 'Inactive' }}</span
                >
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Pagination -->
    <div class="card-footer d-flex justify-content-center">
      <pagination
        [totalItems]="collectionSize"
        [(ngModel)]="page"
        (pageChanged)="pageChanged($event)"
      ></pagination>
    </div>
  </div>

  <!-- Actions Card -->
  <div class="card">
    <div class="card-header bg-primary d-flex align-items-center gap-2">
      <i class="fas fa-cogs"></i>
      <h6 class="mb-0">Actions</h6>
    </div>
    <div class="card-body text-end">
      <button
        class="btn btn-primary"
        type="button"
        (click)="openModal()"
        [disabled]="selectedRow === null"
      >
        <i class="fas fa-edit"></i> Edit Borrower
      </button>
    </div>
  </div>
</div>
