import {Component, OnInit, TemplateRef} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';
import {ModalOptions} from 'ngx-bootstrap/modal';
import {<PERSON>rrow<PERSON>} from '../../model/borrower';
import {NotificationService} from '../../../../core/service/notification.service';
import {BorrowerService} from '../../service/borrower.service';
import {MetaDataService} from '../../../../core/service/metaData.service';
import {NewBorrowerComponent} from '../new-borrower/new-borrower.component';

@Component({
  standalone: false,
  selector: 'app-customer-detail',
  templateUrl: './manage-borrower.component.html',
  styleUrls: ['./manage-borrower.component.css']
})
export class ManageBorrowerComponent implements OnInit {

  modalRef: BsModalRef;
  customers: Array<Borrower> = [];

  selectedRow: number;
  setClickedRow: Function;

  searchKeyName: string;
  searchKeyTp: string;

  page;
  collectionSize;
  pageSize;

  borrower: Borrower;
  active: boolean = false;

  constructor(private notificationService: NotificationService, private modalService: BsModalService,
              private customerService: BorrowerService, public metaDataService: MetaDataService) {

  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 8;
    this.findAll();
    this.searchKeyName = null;
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  findAll() {
    this.customerService.findAllPagination(this.page - 1, this.pageSize).subscribe({
      next: (data: any) => {
        this.customers = data?.content || [];
        this.collectionSize = (data?.totalPages || 0) * 10;
      },
      error: (error) => {
        console.error('Error loading customers:', error);
        this.customers = [];
        this.collectionSize = 0;
      }
    });
  }

  setSelectedCustomer(event) {
    this.borrower = event.item;
    this.customers = [];
    this.customers.push(this.borrower);
  }

  customerDetail(selectedItem: any , index) {
    this.selectedRow = index;
    this.borrower = selectedItem;
  }

  loadCustomerByName() {
    if (this.searchKeyName !== '') {
      this.customerService.findByNameLike(this.searchKeyName).subscribe({
        next: (data: Array<Borrower>) => {
          this.customers = data || [];
        },
        error: (error) => {
          console.error('Error loading customers by name:', error);
          this.customers = [];
        }
      });
    } else {
      this.ngOnInit();
    }
  }

  loadCustomerByTp() {
    if (this.searchKeyTp !== '') {
      this.customerService.findByTpLike(this.searchKeyTp).subscribe({
        next: (data: Array<Borrower>) => {
          this.customers = data || [];
        },
        error: (error) => {
          console.error('Error loading customers by telephone:', error);
          this.customers = [];
        }
      });
    } else {
      this.ngOnInit();
    }
  }

  openModal() {
    this.modalRef = this.modalService.show(NewBorrowerComponent, <ModalOptions>{class: 'modal-lg'});
    this.modalRef.content.borrower = this.borrower;
    this.modalRef.content.isEdit = true;

    this.modalRef.content.modalRef = this.modalRef;
    this.modalService.onHide.subscribe(result => {
      this.findAll();
    });
  }

  searchActiveResult(e) {
    if (e.target.checked) {
      this.active = true;
    } else {
      this.active = false;
      this.findAll();
    }
  }

}
