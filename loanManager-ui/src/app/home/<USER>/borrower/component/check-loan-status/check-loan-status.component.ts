import {Component, OnInit} from '@angular/core';
import {NotificationService} from '../../../../core/service/notification.service';
import {BorrowerService} from '../../service/borrower.service';
import {Loan} from "../../../business/model/loan";
import {CheckLoanStatusService} from "../../service/check-loan-status.service";
import {LoanRecord} from "../../../business/model/loanRecord";

@Component({
  standalone: false,
  selector: 'app-check-loan-status',
  templateUrl: './check-loan-status.component.html',
  styleUrls: ['./check-loan-status.component.css']
})

export class CheckLoanStatusComponent implements OnInit {

  nicNumber: string = "";
  loans: Array<Loan> = [];

  loanRecords: Array<LoanRecord> = [];

  loan: Loan = new Loan();

  constructor(public checkLoanService: CheckLoanStatusService,
              public notificationService: NotificationService) {
  }

  ngOnInit() {
    this.clear();
  }

  findActiveLoanByNic(nic: string) {
    this.clear();
    this.checkLoanService.findActiveLoanByNic(nic.toUpperCase()).subscribe((data: any) => {
      if (data) {
        this.loans = data;
      } else {
        this.notificationService.showError("ක්‍රියාත්මක වන ණය දීම් සොයා ගත නොහැකිය");
      }
    });
  }

  findByLoanNo(loanNo: string) {
    this.loan = new Loan();
    this.checkLoanService.findByLoanNo(loanNo).subscribe((data: any) => {
      if (data) {
        this.loan = data;
        this.findPendingLoanRecords();
      } else {
        this.notificationService.showError("ණය දීම සොයා ගත නොහැකිය");
      }
    });
  }

  findPendingLoanRecords() {
    this.loanRecords = [];
    this.checkLoanService.findPendingRecordsByLoanNo(this.loan.loanNo).subscribe((data: Array<LoanRecord>) => {
      if (data) {
        this.loanRecords = data;
      }
    });
  }

  clear() {
    this.loan = new Loan();
    this.loans = [];
    this.loanRecords = [];
    this.nicNumber = "";
  }

}

