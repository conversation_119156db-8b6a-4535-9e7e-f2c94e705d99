<div class="card">
  <div class="card-header bg-primary fw-bold">
    ණය පිලිබඳ විස්තර
  </div>
  <div class="card-body">
    <form class="row justify-content-center g-2 align-items-center mb-4" (ngSubmit)="findActiveLoanByNic(nicNumber)">
      <div class="col-12 col-md-5">
        <input
          type="text"
          required
          [(ngModel)]="nicNumber"
          name="loans"
          autocomplete="off"
          placeholder="හැඳුනුම් පත් අංකය යොදන්න"
          class="form-control"
        />
      </div>
      <div class="col-12 col-md-2">
        <button type="submit" class="btn btn-primary w-100">සොයන්න</button>
      </div>
    </form>

    <div *ngIf="loans.length > 0" class="text-center mb-4">
      <hr />
      <div *ngFor="let ln of loans; let i = index" class="mb-3" style="cursor: pointer;" (click)="findByLoanNo(ln.loanNo)">
        <p class="mb-1">{{ (i + 1) + '. ණය සැලැස්ම - ' + ln.loanPlan?.name }}</p>
        <p class="mb-0"><strong>මුදල - </strong>{{ ln.loanAmount }}</p>
        <hr />
      </div>
    </div>

    <div class="row mt-3" *ngIf="loan?.loanNo">
      <div class="col-12 text-center mb-3">
        <h3 *ngIf="loan.status.name == 4" class="text-danger fw-bold">
          ඔබේ ණය මුදල හිඟ ණය කාණ්ඩයට වැටී ඇත
        </h3>
        <p class="text-danger small">
          වාරික {{loan.loanPlan.arrearsInterestDuration}}ක් ගෙවීම පැහැර හැර ඇත්නම් {{loan.loanPlan.arrearsInterestDuration+1}}වන වාරිකයේ සිට සියලුම හිඟ වාරික වලට
          අමතර {{loan.loanPlan.arrearsInterestRate}}%ක පොලීයක් එකතු වේ
        </p>
      </div>

      <div class="col-12">
        <table class="table table-striped table-bordered">
          <tbody>
          <tr>
            <th scope="row">ණය අංකය</th>
            <td>{{ loan.loanNo }}</td>
          </tr>
          <tr>
            <th scope="row" class="text-primary">ණය ගැනුම් කරු</th>
            <td>{{ loan.borrower.name }}</td>
          </tr>
          <tr>
            <th scope="row" class="text-primary">ණය සැලැස්ම</th>
            <td>{{ loan.loanPlan?.name }}</td>
          </tr>
          <tr>
            <th scope="row" class="text-primary">ණය මුදල</th>
            <td>{{ loan.loanAmount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <th scope="row" class="text-primary">පොලිය සමග ණය මුදල</th>
            <td>{{ loan.loanAmountWithInterest | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <th scope="row" class="text-primary">ලබා ගත් දිනය</th>
            <td>{{ loan.dateTime | date }}</td>
          </tr>
          <tr>
            <th scope="row" class="text-danger">ගෙවා අවසන් වන දිනය</th>
            <td>{{ loan.settlementDate | date }}</td>
          </tr>
          <tr>
            <th scope="row" class="text-danger">ශේෂය</th>
            <td>{{ loan.balance | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <th scope="row" class="text-danger">ඉතිරි වාරික ගණන</th>
            <td>{{ loan.installmentLeft }}</td>
          </tr>
          <tr>
            <th scope="row" class="text-primary">ගෙවූ මුදල</th>
            <td>{{ loan.paidAmount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <th scope="row text-danger" style="color: #dc3545;">දඩ පොලී මුදල</th>
            <td>{{ loan.arrearsAmount | number: '1.2-2' }}</td>
          </tr>
          </tbody>
        </table>

        <table class="table table-striped mt-3">
          <thead>
          <tr>
            <th>වාරික දිනය</th>
            <th>වාරිකය</th>
            <th>ගෙවූ දිනය</th>
            <th>ගෙවීම්</th>
            <th>ශේෂය</th>
          </tr>
          </thead>
          <tbody>
          <tr
            *ngFor="let record of loanRecords"
            [ngClass]="{
                'table-warning': record.status.name === '1',
                'table-success': record.status.name === '3',
                'table-danger': record.status.name === '2'
              }"
          >
            <td>{{ record.installmentDate }}</td>
            <td>{{ record.installmentAmount | number: '1.2-2' }}</td>
            <td>{{ record.paidDate ? record.paidDate : 'ගෙවා නැත' }}</td>
            <td>{{ record.paidAmount | number: '1.2-2' }}</td>
            <td>{{ record.balance | number: '1.2-2' }}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
