import {Component, OnInit, ViewChild} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {NgForm} from '@angular/forms';
import {NotificationService} from '../../../../core/service/notification.service';
import {BorrowerService} from '../../service/borrower.service';
import {<PERSON>rrower} from "../../model/borrower";
import {Route} from "../../../business/model/route";
import {FileService} from "../../../../core/service/file.service";
import {RouteService} from "../../../business/service/route.service";

@Component({
  standalone: false,
  selector: 'app-new-customer',
  templateUrl: './new-borrower.component.html',
  styleUrls: ['./new-borrower.component.css']
})

export class NewBorrowerComponent implements OnInit {
  @ViewChild('photo', {static: true}) photo;

  borrower: Borrower;
  nicAvailability = false;
  modalRef: BsModalRef;
  isEdit: boolean;
  isModal = false;
  type: string;
  loan: string;

  selectedRoute: Route;
  routeList: Array<Route> = [];

  files: File[] = [];
  isSubmitting = false;

  constructor(public customerService: BorrowerService,
              public notificationService: NotificationService,
              private fileService: FileService,
              private routeService: RouteService) {
  }

  ngOnInit() {
    this.borrower = new Borrower();
    this.borrower.active = true;
    this.loadRoute();
    this.loadFiles();
  }

  checkNic() {
    this.customerService.checkNic(this.borrower.nic).subscribe((res: boolean) => {
      this.nicAvailability = res;
    });
  }

  setSelectedRout() {
    this.borrower.route = new Route();
    this.borrower.route.id = this.selectedRoute.id;
  }

  loadRoute() {
    this.routeService.findAllForSelect().subscribe((data: Array<Route>) => {
      return this.routeList = data;
    });
  }

  savePerson(form: NgForm) {
    this.isSubmitting = true;
    this.customerService.save(this.borrower).subscribe((result: any) => {
      if (result.code === 200) {
        this.notificationService.showSuccess('Borrower Saved Successfully');
        if (this.isModal) {
          this.modalRef.hide();
        }
        this.ngOnInit();
        this.isSubmitting = false;
        if (this.modalRef) {
          this.modalRef.hide();
        }
      } else if (result === null) {
        this.notificationService.showError('Borrower Saving Failed');
        this.borrower.active = true;
        this.isSubmitting = false;
      }
    })
  }

  clear() {

  }

  onSelect(event) {
    this.files.push(...event.addedFiles);
  }

  onRemove(event) {
    this.files.splice(this.files.indexOf(event), 1);
  }

  loadFiles() {
    if (this.borrower.imageUrls) {
      for (let url of this.borrower.imageUrls) {
        this.fileService.getFile(url).subscribe((result: File) => {
          this.files.push(result);
        });
      }
    }
  }

}

