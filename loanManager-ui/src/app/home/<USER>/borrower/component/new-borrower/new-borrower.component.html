<div class="container-fluid py-4">

  <!-- Header Section -->
  <div class="mb-4">
    <div class="d-flex justify-content-between align-items-center mb-2">
      <h2 class="h4 d-flex align-items-center gap-2 mb-0">
        <i class="fas fa-user-plus"></i>
        {{ isEdit ? 'Edit' : 'New' }} Borrower
      </h2>
      <button
        type="button"
        class="btn btn-outline-primary btn-sm"
        (click)="ngOnInit()"
      >
        <i class="fas fa-sync-alt"></i> Reset
      </button>
    </div>
    <p class="text-muted mb-0">
      {{ isEdit
      ? 'Update borrower information and details.'
      : 'Add a new borrower to the system with personal and contact information.' }}
    </p>
  </div>

  <!-- Form Card -->
  <div class="card">
    <div class="card-header bg-primary d-flex align-items-center gap-2">
      <i class="fas fa-edit"></i>
      <h5 class="mb-0">Borrower Information</h5>
    </div>
    <div class="card-body">
      <form #newPersonForm="ngForm" (ngSubmit)="savePerson(newPersonForm)">

        <div class="row g-3">
          <div class="col-md-6">
            <label for="nic" class="form-label">NIC</label>
            <input
              id="nic"
              name="nic"
              type="text"
              class="form-control"
              placeholder="Enter NIC No"
              required
              pattern="([0-9]{9}[xXvV]|[0-9]{12})$"
              [(ngModel)]="borrower.nic"
              #nic="ngModel"
              (keyup)="checkNic()"
              [class.is-invalid]="nic.invalid && nic.touched"
              [disabled]="isEdit"
            />
            <div *ngIf="nic.invalid && nic.touched" class="invalid-feedback">
              NIC No is required and must be valid.
            </div>
            <div *ngIf="nicAvailability" class="text-danger mt-1">
              * NIC No already used
            </div>
          </div>

          <div class="col-md-6">
            <label for="CuName" class="form-label">Name</label>
            <input
              id="CuName"
              name="CuName"
              type="text"
              class="form-control"
              placeholder="Enter Name"
              required
              [(ngModel)]="borrower.name"
              #CuName="ngModel"
              [class.is-invalid]="CuName.invalid && CuName.touched"
              [disabled]="isEdit"
            />
            <div *ngIf="CuName.invalid && CuName.touched" class="invalid-feedback">
              Name is required.
            </div>
          </div>

          <div class="col-md-6">
            <label for="address1" class="form-label">Address</label>
            <input
              id="address1"
              name="address1"
              type="text"
              class="form-control"
              placeholder="Enter Address"
              required
              [(ngModel)]="borrower.address"
              #address1="ngModel"
              [class.is-invalid]="address1.invalid && address1.touched"
            />
            <div *ngIf="address1.invalid && address1.touched" class="invalid-feedback">
              Address is required.
            </div>
          </div>

          <div class="col-md-4">
            <label for="telephone1" class="form-label">Contact Number 1</label>
            <input
              id="telephone1"
              name="telephone1"
              type="text"
              class="form-control"
              placeholder="Enter Contact Number 1"
              pattern="^\d{10}$"
              required
              [(ngModel)]="borrower.telephone1"
              #telephone1="ngModel"
              [class.is-invalid]="telephone1.invalid && telephone1.touched"
            />
            <div *ngIf="telephone1.invalid && telephone1.touched" class="invalid-feedback">
              Contact Number 1 is required and must be 10 digits.
            </div>
          </div>

          <div class="col-md-4">
            <label for="telephone2" class="form-label">Contact Number 2</label>
            <input
              id="telephone2"
              name="telephone2"
              type="text"
              class="form-control"
              placeholder="Enter Contact Number 2"
              pattern="^\d{10}$"
              [(ngModel)]="borrower.telephone2"
            />
          </div>

          <div class="col-md-4">
            <label for="routeSelect" class="form-label">Route</label>
            <select
              id="routeSelect"
              name="routeSelect"
              class="form-select"
              required
              [(ngModel)]="selectedRoute"
              #routeSelect="ngModel"
              (ngModelChange)="setSelectedRout()"
            >
              <option value="" disabled selected>Select Route No</option>
              <option *ngFor="let type of routeList" [ngValue]="type">{{ type.name }}</option>
            </select>
            <div *ngIf="routeSelect.invalid && routeSelect.touched" class="invalid-feedback">
              Please select a route.
            </div>
          </div>
        </div>

        <div class="mt-4">
          <label class="form-label">Upload Photos</label>
          <ngx-dropzone (change)="onSelect($event)" [maxFileSize]="2e+6">
            <ngx-dropzone-label>
              <div class="text-center p-4">
                <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                <p class="mb-1">Drop images here or click to browse</p>
                <small class="text-muted">Maximum file size: 2MB</small>
              </div>
            </ngx-dropzone-label>

            <ngx-dropzone-image-preview
              ngProjectAs="ngx-dropzone-preview"
              *ngFor="let f of files"
              [file]="f"
              [removable]="true"
              (removed)="onRemove($event)"
            >
              <ngx-dropzone-label>{{ f.name }} ({{ f.type }})</ngx-dropzone-label>
            </ngx-dropzone-image-preview>
          </ngx-dropzone>
        </div>

        <div class="mt-4 text-end">
          <button
            type="button"
            class="btn btn-outline-secondary me-2"
            (click)="clear()"
            [disabled]="isEdit"
          >
            <i class="fas fa-eraser"></i> Clear
          </button>
          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="isSubmitting || !newPersonForm.form.valid || nicAvailability"
          >
            <i class="fas fa-save"></i> {{ isEdit ? 'Update' : 'Save' }} Borrower
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
