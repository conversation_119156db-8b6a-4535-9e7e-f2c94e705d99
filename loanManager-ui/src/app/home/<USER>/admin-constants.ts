import {environment} from '../../../environments/environment';

export class ApiConstants {

  public static API_URL = environment.apiUrl;


  public static SAVE_USER = ApiConstants.API_URL + 'user/save';
  public static GET_USERS = ApiConstants.API_URL + 'user/findAll';
  public static GET_USER = ApiConstants.API_URL + 'user/findUser';
  public static DISABLE_USER = ApiConstants.API_URL + 'user/delete';
  public static SEARCH_USER = ApiConstants.API_URL + 'user/search';
  public static USER_CHECK = ApiConstants.API_URL + 'user/checkForUserName';
  public static UPDATE_USER = ApiConstants.API_URL + 'user/updateDesktopPerm';
  public static SEARCH_BY_NAME = ApiConstants.API_URL + 'user/searchByName';

  // Settings API endpoints
  public static SAVE_SETTINGS = ApiConstants.API_URL + 'settings/save';
  public static GET_ALL_SETTINGS = ApiConstants.API_URL + 'settings/findAll';
  public static GET_ALL_ACTIVE_SETTINGS = ApiConstants.API_URL + 'settings/findAllActive';
  public static GET_SETTING_BY_ID = ApiConstants.API_URL + 'settings/findById';
  public static GET_SETTING_BY_KEY = ApiConstants.API_URL + 'settings/findByKey';
  public static GET_SETTING_VALUE = ApiConstants.API_URL + 'settings/getValue';
  public static UPDATE_SETTING = ApiConstants.API_URL + 'settings/updateSetting';
  public static CREATE_DEFAULT_SETTINGS = ApiConstants.API_URL + 'settings/createDefaults';


  public static GET_ROLES = ApiConstants.API_URL + 'role/findAll';
  public static SAVE_ROLE = ApiConstants.API_URL + 'role/save';
  public static DELETE_ROLE = ApiConstants.API_URL + 'role/delete';
  public static SEARCH_ROLE = ApiConstants.API_URL + 'role/search';

  public static FIND_ENABLED_PERMISSIONS = ApiConstants.API_URL + 'user/findEnabledPermission';
  public static FIND_DESKTOP_PERMISSIONS = ApiConstants.API_URL + 'user/findDesktopPermissions';
  public static FIND_PERMISSION_BY_MODULE = ApiConstants.API_URL + 'user/findPermissionByModule';
  public static SAVE_DESKTOP_PERMS = ApiConstants.API_URL + 'user/saveDesktopPerms';
  public static GET_ENABLED_MODULES = ApiConstants.API_URL + 'user/getEnabledModules';
  public static GET_PERMISSION = ApiConstants.API_URL + 'user/getPermission';
  public static SEARCH_USER_BY_USERNAME = ApiConstants.API_URL + ' user/findByUsername';
}
