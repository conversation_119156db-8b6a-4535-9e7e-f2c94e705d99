import {Injectable} from '@angular/core';
import {ApiConstants} from '../admin-constants';
import {HttpClient} from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})

export class PermissionService {

  constructor(private http: HttpClient) {
  }

  findEnabledPermissions(username) {
    return this.http.get(ApiConstants.FIND_ENABLED_PERMISSIONS, {params: {'username': username}});
  }

  getEnabledModules(username) {
    return this.http.get(ApiConstants.GET_ENABLED_MODULES,{params: {'username': username}});
  }

  findDesktopPermissions(username) {
    return this.http.get(ApiConstants.FIND_DESKTOP_PERMISSIONS, {params: {'username': username}});
  }

  findPermsByModule(module) {
    return this.http.get(ApiConstants.FIND_PERMISSION_BY_MODULE, {params: {'moduleId': module}});
  }

  saveDesktopPerms(username, permissions) {
    const desktopPerm = {'username': username, 'permissions': permissions};
    return this.http.post(ApiConstants.SAVE_DESKTOP_PERMS, desktopPerm);
  }

  getSelectedPermission(permissionId) {
    return this.http.get(ApiConstants.GET_PERMISSION, {params: {'permissionId': permissionId}});
  }

}
