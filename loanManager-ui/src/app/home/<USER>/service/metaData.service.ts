import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {CoreApiConstants} from '../core-constants';

@Injectable({
  providedIn: 'root'
})
export class MetaDataService {

  constructor(private http: HttpClient) {
  }

  save(metadata) {
    return this.http.post<any>(CoreApiConstants.SAVE_METADATA, metadata);
  }

  findByCategory(category) {
    return this.http.get(CoreApiConstants.SEARCH_METADATA, {params: {any: category}});
  }

  findById(id) {
    return this.http.get(CoreApiConstants.SEARCH_METADATA_BY_ID, {params: {id: id}});
  }

  public findByValueAndCategory(value, category) {
    return this.http.get(CoreApiConstants.SEARCH_METADATA_BY_CAT_VAL, {params: {value: value, category: category}});
  }

  public findByValueAndCategoryBoolean(value, category) {
    return this.http.get(CoreApiConstants.SEARCH_METADATA_BY_CAT_VAL_BOOL, {params: {value: value, category: category}});
  }

}
