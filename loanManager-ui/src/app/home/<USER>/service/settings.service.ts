import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ApiConstants } from '../admin-constants';
import { Settings } from '../model/settings';

@Injectable({
  providedIn: 'root'
})
export class SettingsService {

  constructor(private http: HttpClient) { }

  public save(settings: Settings): Observable<any> {
    return this.http.post<any>(ApiConstants.SAVE_SETTINGS, settings);
  }

  public findAll(): Observable<Settings[]> {
    return this.http.get<Settings[]>(ApiConstants.GET_ALL_SETTINGS);
  }

  public findAllActive(): Observable<Settings[]> {
    return this.http.get<Settings[]>(ApiConstants.GET_ALL_ACTIVE_SETTINGS);
  }

  public findById(id: string): Observable<Settings> {
    const params = new HttpParams().set('id', id);
    return this.http.get<Settings>(ApiConstants.GET_SETTING_BY_ID, { params });
  }

  public findByKey(settingKey: string): Observable<Settings> {
    const params = new HttpParams().set('settingKey', settingKey);
    return this.http.get<Settings>(ApiConstants.GET_SETTING_BY_KEY, { params });
  }

  public getValue(settingKey: string): Observable<string> {
    const params = new HttpParams().set('settingKey', settingKey);
    return this.http.get<string>(ApiConstants.GET_SETTING_VALUE, { params });
  }

  public updateSetting(settingKey: string, settingValue: string): Observable<any> {
    const params = new HttpParams()
      .set('settingKey', settingKey)
      .set('settingValue', settingValue);
    return this.http.put<any>(ApiConstants.UPDATE_SETTING, null, { params });
  }

  public createDefaultSettings(): Observable<any> {
    return this.http.post<any>(ApiConstants.CREATE_DEFAULT_SETTINGS, null);
  }
}
