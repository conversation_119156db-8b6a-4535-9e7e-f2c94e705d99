import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { CoreApiConstants } from '../core-constants';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {

  constructor(private http: HttpClient) { }

  getOngoingLoanStats(): Observable<any> {
    return this.http.get(CoreApiConstants.API_URL + 'mobileApp/ongoingLoanStat');
  }

  getLoanCountStats(): Observable<any> {
    return this.http.get(CoreApiConstants.API_URL + 'mobileApp/loanCountStat');
  }

  getMonthlyStats(): Observable<any> {
    return this.http.get(CoreApiConstants.API_URL + 'mobileApp/monthlyStat');
  }

  getSettledStats(): Observable<any> {
    return this.http.get(CoreApiConstants.API_URL + 'mobileApp/settledLoanStats');
  }

  getAllLoanStat(): Observable<any> {
    return this.http.get(CoreApiConstants.API_URL + 'mobileApp/allLoanStat');
  }

  getCashInHand(): Observable<any> {
    // Using the existing endpoint for getting ledger by app number
    const appNo = '1'; // This should come from configuration or user context
    return this.http.get(CoreApiConstants.API_URL + 'mobileApp/getLedgerByAppNo', {
      params: { appNo: appNo }
    });
  }

  getRecentActivity(): Observable<any> {
    return this.http.get(CoreApiConstants.API_URL + 'mobileApp/recentActivity');
  }

  getLoanPerformanceMetrics(): Observable<any> {
    return this.http.get(CoreApiConstants.API_URL + 'mobileApp/loanPerformanceMetrics');
  }

  getPaymentTrends(): Observable<any> {
    return this.http.get(CoreApiConstants.API_URL + 'mobileApp/paymentTrends');
  }
}
