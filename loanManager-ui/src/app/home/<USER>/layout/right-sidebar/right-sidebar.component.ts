import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { RightSidebarService } from './right-sidebar.service';
import { PermissionService } from '../../service/permission.service';

interface Permission {
  id: string;
  name: string;
  route: string;
  iconCss: string;
  module: {
    id: string;
    name: string;
  };
}

interface ModuleGroup {
  name: string;
  permissions: Permission[];
}

@Component({
  standalone: false,
  selector: 'app-right-sidebar',
  templateUrl: './right-sidebar.component.html',
  styleUrls: ['./right-sidebar.component.css']
})
export class RightSidebarComponent implements OnInit {

  isOpen = false;
  moduleGroups: ModuleGroup[] = [];
  user: any;
  loading = true;

  constructor(
    private rightSidebarService: RightSidebarService,
    private router: Router,
    private permissionService: PermissionService
  ) { }

  ngOnInit(): void {
    this.rightSidebarService.isOpen$.subscribe(isOpen => {
      this.isOpen = isOpen;
    });

    this.loadUserInfo();
    this.loadUserPermissions();
  }

  loadUserInfo() {
    if (localStorage.getItem('currentUser')) {
      this.user = JSON.parse(localStorage.getItem('currentUser')).user;
    } else {
      this.router.navigateByUrl('/login');
    }
  }

  loadUserPermissions() {
    if (this.user && this.user.username) {
      this.loading = true;
      this.permissionService.findAvailablePermissions(this.user.username).subscribe(
        (permissions: Permission[]) => {
          this.groupPermissionsByModule(permissions);
          this.loading = false;
        },
        (error) => {
          console.error('Error loading permissions:', error);
          this.loading = false;
        }
      );
    }
  }

  groupPermissionsByModule(permissions: Permission[]) {
    const moduleMap = new Map<string, ModuleGroup>();

    permissions.forEach(permission => {
      if (permission.module && permission.module.name) {
        const moduleName = permission.module.name;

        if (!moduleMap.has(moduleName)) {
          moduleMap.set(moduleName, {
            name: moduleName,
            permissions: []
          });
        }

        moduleMap.get(moduleName)!.permissions.push(permission);
      }
    });

    this.moduleGroups = Array.from(moduleMap.values());
  }

  navigateTo(route: string) {
    // Clean the route and navigate
    const cleanRoute = route.startsWith('/') ? route.substring(1) : route;
    this.router.navigate(['/home/' + cleanRoute]);
    this.rightSidebarService.close();
  }

  closeSidebar() {
    this.rightSidebarService.close();
  }

  getModuleIcon(moduleName: string): string {
    // Return appropriate icons based on module name
    const moduleIcons: { [key: string]: string } = {
      'Report': 'fas fa-chart-bar',
      'Business': 'fas fa-briefcase',
      'Admin': 'fas fa-user-cog',
      'Borrower': 'fas fa-users',
      'HR': 'fas fa-user-tie'
    };

    return moduleIcons[moduleName] || 'fas fa-folder';
  }

  getPermissionDescription(permissionName: string): string {
    // Return appropriate descriptions based on permission name
    const descriptions: { [key: string]: string } = {
      'Payment Report': 'View payment transactions',
      'Borrower Report': 'Borrower analytics',
      'Income Report': 'Income analysis',
      'User Management': 'Manage system users',
      'Pending Loans': 'Review pending applications',
      'Arrears Loans': 'Overdue loan management',
      'Ledger': 'Financial records',
      'Create Loan': 'Create new loan applications',
      'Loan Payment': 'Process loan payments',
      'All Loans': 'View all loan records'
    };

    return descriptions[permissionName] || 'Access ' + permissionName.toLowerCase();
  }
}
