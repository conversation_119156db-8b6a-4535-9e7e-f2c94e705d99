<!-- Overlay -->
<div class="position-fixed top-0 start-0 w-100 h-100 bg-dark bg-opacity-50"
     [class.d-block]="isOpen"
     [class.d-none]="!isOpen"
     (click)="closeSidebar()"
     style="z-index:1040;"></div>

<!-- Sidebar -->
<div class="position-fixed top-0 end-0 bg-white shadow-lg h-100 overflow-auto"
     [class.d-block]="isOpen"
     [class.d-none]="!isOpen"
     style="width: 360px; z-index: 1045; transition: transform 0.3s ease-in-out;">

  <!-- Header -->
  <div class="d-flex justify-content-between align-items-center border-bottom p-3 bg-light">
    <h5 class="mb-0 text-primary">
      <i class="fas fa-tools me-2"></i>Reports & Tools
    </h5>
    <button class="btn btn-sm btn-outline-secondary" (click)="closeSidebar()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <!-- Content -->
  <div class="p-3">

    <!-- Loading State -->
    <div *ngIf="loading" class="text-center py-4 text-muted">
      <i class="fas fa-spinner fa-spin fa-2x"></i>
      <p class="mt-2">Loading modules...</p>
    </div>

    <!-- No Permissions State -->
    <div *ngIf="!loading && moduleGroups.length === 0" class="text-center py-4 text-muted">
      <i class="fas fa-lock fa-2x"></i>
      <p class="mt-2">No modules available</p>
      <small>Contact your administrator for access</small>
    </div>

    <!-- Module Groups -->
    <div *ngFor="let moduleGroup of moduleGroups" class="mb-4">
      <div class="d-flex align-items-center mb-2 text-primary fw-semibold">
        <i [class]="getModuleIcon(moduleGroup.name)" class="me-2"></i>
        <span>{{ moduleGroup.name }}</span>
      </div>

      <div class="list-group">
        <button type="button"
                class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                *ngFor="let permission of moduleGroup.permissions"
                (click)="navigateTo(permission.route)">
          <div class="d-flex align-items-start gap-2">
            <i [class]="permission.iconCss || 'fas fa-cog'"></i>
            <div>
              <div class="fw-semibold">{{ permission.name }}</div>
              <div class="small text-muted">{{ getPermissionDescription(permission.name) }}</div>
            </div>
          </div>
          <i class="fas fa-chevron-right text-muted"></i>
        </button>
      </div>
    </div>

  </div>
</div>
