import { Component, OnInit } from '@angular/core';
import { DashboardService } from '../service/dashboard.service';

export interface LoanCountStat {
  settledLoanCount: number;
  activeLoanCount: number;
  arrearsLoanCount: number;
  totalLoanCount: number;
}

export interface MonthlyPaymentStats {
  monthlyPaymentAmount: number;
  monthlyExpenseAmount: number;
  monthlyProfit: number;
}

export interface LoanStat {
  amountLent: number;
  amountCollected: number;
  collectibleAmount: number;
  profit: number;
}

export interface RecentActivity {
  recentLoans: RecentLoan[];
  recentPayments: RecentPayment[];
  recentArrears: RecentArrears[];
}

export interface RecentLoan {
  loanNo: string;
  borrowerName: string;
  loanAmount: number;
  loanPlanName: string;
  approvedDate: string;
  status: string;
}

export interface RecentPayment {
  loanNo: string;
  borrowerName: string;
  paymentAmount: number;
  paymentDate: string;
  paymentType: string;
}

export interface RecentArrears {
  loanNo: string;
  borrowerName: string;
  daysDue: number;
  overdueAmount: number;
  dueDate: string;
}

export interface LoanPerformanceMetrics {
  collectionRate: number;
  defaultRate: number;
  averagePaymentTime: number;
  totalOverdueLoans: number;
  totalOverdueAmount: number;
  loansIssuedThisMonth: number;
  loansSettledThisMonth: number;
  averageLoanAmount: number;
  recoveryRate: number;
  activeBorrowers: number;
}

export interface PaymentTrends {
  last7Days: DailyPayment[];
  last4Weeks: WeeklyPayment[];
  todayCollection: number;
  weeklyAverage: number;
  monthlyTarget: number;
  monthlyProgress: number;
}

export interface DailyPayment {
  date: string;
  amount: number;
  paymentCount: number;
}

export interface WeeklyPayment {
  weekLabel: string;
  amount: number;
  paymentCount: number;
  weekStart: string;
  weekEnd: string;
}

@Component({
  standalone: false,
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit {

  loanCountStat: LoanCountStat = {
    settledLoanCount: 0,
    activeLoanCount: 0,
    arrearsLoanCount: 0,
    totalLoanCount: 0
  };

  monthlyStats: MonthlyPaymentStats = {
    monthlyPaymentAmount: 0,
    monthlyExpenseAmount: 0,
    monthlyProfit: 0
  };

  ongoingLoanStat: LoanStat = {
    amountLent: 0,
    amountCollected: 0,
    collectibleAmount: 0,
    profit: 0
  };

  settledLoanStat: LoanStat = {
    amountLent: 0,
    amountCollected: 0,
    collectibleAmount: 0,
    profit: 0
  };

  allLoanStat: LoanStat = {
    amountLent: 0,
    amountCollected: 0,
    collectibleAmount: 0,
    profit: 0
  };

  cashInHand: number = 0;
  loading: boolean = true;
  user: any;

  recentActivity: RecentActivity = {
    recentLoans: [],
    recentPayments: [],
    recentArrears: []
  };

  performanceMetrics: LoanPerformanceMetrics = {
    collectionRate: 0,
    defaultRate: 0,
    averagePaymentTime: 0,
    totalOverdueLoans: 0,
    totalOverdueAmount: 0,
    loansIssuedThisMonth: 0,
    loansSettledThisMonth: 0,
    averageLoanAmount: 0,
    recoveryRate: 0,
    activeBorrowers: 0
  };

  paymentTrends: PaymentTrends = {
    last7Days: [],
    last4Weeks: [],
    todayCollection: 0,
    weeklyAverage: 0,
    monthlyTarget: 0,
    monthlyProgress: 0
  };

  constructor(private dashboardService: DashboardService) {
    this.user = JSON.parse(localStorage.getItem('currentUser') || '{}').user || {};
  }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    this.loading = true;

    // Load all dashboard data
    Promise.all([
      this.getMonthlyStat(),
      this.getOngoingLoanStats(),
      this.getLoanCountStat(),
      this.getSettledLoanStat(),
      this.getAllLoanStat(),
      this.getCashInHand(),
      this.getRecentActivity(),
      this.getPerformanceMetrics(),
      this.getPaymentTrends()
    ]).finally(() => {
      this.loading = false;
    });
  }

  getOngoingLoanStats(): Promise<void> {
    return new Promise((resolve) => {
      this.dashboardService.getOngoingLoanStats().subscribe({
        next: (data: LoanStat) => {
          if (data) {
            this.ongoingLoanStat = data;
          }
          resolve();
        },
        error: (error) => {
          console.error('Error loading ongoing loan stats:', error);
          resolve();
        }
      });
    });
  }

  getLoanCountStat(): Promise<void> {
    return new Promise((resolve) => {
      this.dashboardService.getLoanCountStats().subscribe({
        next: (data: LoanCountStat) => {
          if (data) {
            this.loanCountStat = data;
          }
          resolve();
        },
        error: (error) => {
          console.error('Error loading loan count stats:', error);
          resolve();
        }
      });
    });
  }

  getMonthlyStat(): Promise<void> {
    return new Promise((resolve) => {
      this.dashboardService.getMonthlyStats().subscribe({
        next: (data: MonthlyPaymentStats) => {
          if (data) {
            this.monthlyStats = data;
          }
          resolve();
        },
        error: (error) => {
          console.error('Error loading monthly stats:', error);
          resolve();
        }
      });
    });
  }

  getSettledLoanStat(): Promise<void> {
    return new Promise((resolve) => {
      this.dashboardService.getSettledStats().subscribe({
        next: (data: LoanStat) => {
          if (data) {
            this.settledLoanStat = data;
          }
          resolve();
        },
        error: (error) => {
          console.error('Error loading settled loan stats:', error);
          resolve();
        }
      });
    });
  }

  getAllLoanStat(): Promise<void> {
    return new Promise((resolve) => {
      this.dashboardService.getAllLoanStat().subscribe({
        next: (data: LoanStat) => {
          if (data) {
            this.allLoanStat = data;
          }
          resolve();
        },
        error: (error) => {
          console.error('Error loading all loan stats:', error);
          resolve();
        }
      });
    });
  }

  getCashInHand(): Promise<void> {
    return new Promise((resolve) => {
      this.dashboardService.getCashInHand().subscribe({
        next: (data: any) => {
          if (data) {
            this.cashInHand = data.currentBalance || 0;
          }
          resolve();
        },
        error: (error) => {
          console.error('Error loading cash in hand:', error);
          resolve();
        }
      });
    });
  }

  getRecentActivity(): Promise<void> {
    return new Promise((resolve) => {
      this.dashboardService.getRecentActivity().subscribe({
        next: (data: RecentActivity) => {
          if (data) {
            this.recentActivity = data;
          }
          resolve();
        },
        error: (error) => {
          console.error('Error loading recent activity:', error);
          resolve();
        }
      });
    });
  }

  getPerformanceMetrics(): Promise<void> {
    return new Promise((resolve) => {
      this.dashboardService.getLoanPerformanceMetrics().subscribe({
        next: (data: LoanPerformanceMetrics) => {
          if (data) {
            this.performanceMetrics = data;
          }
          resolve();
        },
        error: (error) => {
          console.error('Error loading performance metrics:', error);
          resolve();
        }
      });
    });
  }

  getPaymentTrends(): Promise<void> {
    return new Promise((resolve) => {
      this.dashboardService.getPaymentTrends().subscribe({
        next: (data: PaymentTrends) => {
          if (data) {
            this.paymentTrends = data;
          }
          resolve();
        },
        error: (error) => {
          console.error('Error loading payment trends:', error);
          resolve();
        }
      });
    });
  }

  refreshData(): void {
    this.loadDashboardData();
  }
}
