<div class="container-fluid p-3 bg-light min-vh-100">
  <!-- Header Section -->
  <div class="bg-primary text-white p-4 rounded-3 mb-4 shadow">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <h4 class="mb-0">
        <i class="fas fa-tachometer-alt text-warning me-2"></i>
        Dashboard Overview
      </h4>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-light btn-sm" (click)="refreshData()" [disabled]="loading">
          <i class="fas fa-sync-alt me-1" [class.fa-spin]="loading"></i>
          Refresh
        </button>
        <button class="btn btn-outline-light btn-sm" routerLink="../starter">
          <i class="fas fa-times me-1"></i>
          Close
        </button>
      </div>
    </div>
    <div>
      <h5 class="mb-1">Welcome back, {{user.firstName}}!</h5>
      <p class="mb-0 opacity-75">Here's what's happening with your loan business today.</p>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="loading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2 text-muted">Loading dashboard data...</p>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!loading">

    <!-- Loan Count Statistics -->
    <div class="mb-4">
      <h5 class="h6 text-secondary mb-3 border-bottom pb-2">
        <i class="fas fa-chart-bar text-primary me-2"></i>
        Loan Statistics
      </h5>
      <div class="row g-3">
        <div class="col-md-3 col-sm-6">
          <div class="card border-start border-success border-4 h-100">
            <div class="card-body d-flex align-items-center">
              <div class="text-success me-3">
                <i class="fas fa-check-circle fa-2x"></i>
              </div>
              <div>
                <h4 class="mb-0 fw-bold">{{loanCountStat.settledLoanCount}}</h4>
                <p class="mb-0 text-muted small">Settled Loans</p>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3 col-sm-6">
          <div class="card border-start border-primary border-4 h-100">
            <div class="card-body d-flex align-items-center">
              <div class="text-primary me-3">
                <i class="fas fa-play-circle fa-2x"></i>
              </div>
              <div>
                <h4 class="mb-0 fw-bold">{{loanCountStat.activeLoanCount}}</h4>
                <p class="mb-0 text-muted small">Active Loans</p>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3 col-sm-6">
          <div class="card border-start border-warning border-4 h-100">
            <div class="card-body d-flex align-items-center">
              <div class="text-warning me-3">
                <i class="fas fa-exclamation-triangle fa-2x"></i>
              </div>
              <div>
                <h4 class="mb-0 fw-bold">{{loanCountStat.arrearsLoanCount}}</h4>
                <p class="mb-0 text-muted small">Arrears Loans</p>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3 col-sm-6">
          <div class="card border-start border-info border-4 h-100">
            <div class="card-body d-flex align-items-center">
              <div class="text-info me-3">
                <i class="fas fa-list fa-2x"></i>
              </div>
              <div>
                <h4 class="mb-0 fw-bold">{{loanCountStat.totalLoanCount}}</h4>
                <p class="mb-0 text-muted small">Total Loans</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Monthly Summary -->
    <div class="mb-4">
      <h5 class="h6 text-secondary mb-3 border-bottom pb-2">
        <i class="fas fa-calendar-alt text-primary me-2"></i>
        Monthly Summary
      </h5>
      <div class="row g-3">
        <div class="col-md-4">
          <div class="card border-start border-success border-4">
            <div class="card-body">
              <div class="d-flex align-items-center mb-2">
                <i class="fas fa-arrow-up text-success me-2"></i>
                <span class="fw-semibold">Monthly Income</span>
              </div>
              <h4 class="mb-0 text-success fw-bold">
                {{monthlyStats.monthlyPaymentAmount | number:'1.2-2'}}
              </h4>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card border-start border-danger border-4">
            <div class="card-body">
              <div class="d-flex align-items-center mb-2">
                <i class="fas fa-arrow-down text-danger me-2"></i>
                <span class="fw-semibold">Monthly Expenses</span>
              </div>
              <h4 class="mb-0 text-danger fw-bold">
                {{monthlyStats.monthlyExpenseAmount | number:'1.2-2'}}
              </h4>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card border-start border-primary border-4">
            <div class="card-body">
              <div class="d-flex align-items-center mb-2">
                <i class="fas fa-chart-line text-primary me-2"></i>
                <span class="fw-semibold">Monthly Profit</span>
              </div>
              <h4 class="mb-0 text-primary fw-bold">
                {{monthlyStats.monthlyProfit | number:'1.2-2'}}
              </h4>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Financial Overview -->
    <div class="row g-3 mb-4">
      <!-- Ongoing Loans -->
      <div class="col-md-6">
        <div class="card">
          <div class="card-header bg-light">
            <h6 class="mb-0"><i class="fas fa-clock text-primary me-2"></i> Ongoing Loans</h6>
          </div>
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
              <span class="text-muted">Amount Lent:</span>
              <span class="fw-bold text-danger">{{ongoingLoanStat.amountLent | number:'1.2-2'}}</span>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
              <span class="text-muted">Amount Collected:</span>
              <span class="fw-bold text-success">{{ongoingLoanStat.amountCollected | number:'1.2-2'}}</span>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2">
              <span class="text-muted">Amount to Collect:</span>
              <span class="fw-bold text-warning">{{(ongoingLoanStat.profit * -1) | number:'1.2-2'}}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- All Loans Summary -->
      <div class="col-md-6">
        <div class="card">
          <div class="card-header bg-light">
            <h6 class="mb-0"><i class="fas fa-chart-pie text-primary me-2"></i> All Loans Summary</h6>
          </div>
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
              <span class="text-muted">Total Amount Lent:</span>
              <span class="fw-bold text-danger">{{allLoanStat.amountLent | number:'1.2-2'}}</span>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
              <span class="text-muted">Total with Interest:</span>
              <span class="fw-bold text-info">{{allLoanStat.collectibleAmount | number:'1.2-2'}}</span>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
              <span class="text-muted">Total Collected:</span>
              <span class="fw-bold text-success">{{allLoanStat.amountCollected | number:'1.2-2'}}</span>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2">
              <span class="text-muted">Expected Profit:</span>
              <span class="fw-bold text-primary">{{allLoanStat.profit | number:'1.2-2'}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Cash in Hand -->
    <div class="card bg-success text-white">
      <div class="card-body d-flex align-items-center">
        <div class="me-3">
          <i class="fas fa-wallet fa-3x opacity-75"></i>
        </div>
        <div>
          <h6 class="mb-1">Cash in Hand</h6>
          <h3 class="mb-0 fw-bold">{{cashInHand | number:'1.2-2'}}</h3>
        </div>
      </div>
    </div>

  </div>
</div>
