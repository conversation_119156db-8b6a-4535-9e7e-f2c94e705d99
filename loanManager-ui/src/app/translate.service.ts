import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {BehaviorSubject, Observable} from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TranslateService {
  // Store translations in a BehaviorSubject for reactivity
  private translationsSubject = new BehaviorSubject<any>({});
  public translations$: Observable<any> = this.translationsSubject.asObservable();

  // Keep a reference to the current language
  private currentLang: string = localStorage.getItem('lang') || 'en';

  // For backward compatibility
  data: any = {};

  constructor(private http: HttpClient) {
    // Load initial translations synchronously
    this.loadTranslationsSync(this.currentLang);
  }

  /**
   * Load translations synchronously (for initial load)
   * @param lang Language code ('en' or 'sn')
   * @private
   */
  private loadTranslationsSync(lang: string): void {
    // First set some default translations to avoid errors
    this.data = {
      "COMMON": {
        "ENABLED": "Enabled",
        "DISABLED": "Disabled"
      },
      "SETTINGS": {
        "USER_SETTINGS": "User Settings",
        "OVERRIDE_INFO": "These settings will override the default application settings.",
        "DEFAULT_DISCOUNT_MODE": "Default Discount Mode",
        "DISCOUNT_MODE_HELP": "Choose how discounts are calculated by default",
        "PRINT_PAGE_FORMAT": "Print Page Format",
        "PRINT_PAGE_HELP": "Select the default page format for printing",
        "SILENT_PRINTING": "Silent Printing",
        "SILENT_PRINT_HELP": "Enable or disable silent printing",
        "ENABLED_OPTION": "Enabled (No Print Dialog)",
        "DISABLED_OPTION": "Disabled (Show Print Dialog)",
        "APPLICATION_LANGUAGE": "Application Language",
        "SELECT_LANGUAGE": "Select your preferred language:",
        "LANGUAGE_HELP": "Changes to language will apply immediately.",
        "HOW_SETTINGS_WORK": "How settings work:",
        "SETTINGS_HELP_1": "Each setting can be enabled or disabled",
        "SETTINGS_HELP_2": "Disabled settings use system defaults",
        "SETTINGS_HELP_3": "Enabled settings use custom values",
        "SETTINGS_HELP_4": "Settings are saved per user",
        "SERVER_URL_NOTE": "Note: Server URL cannot be changed",
        "RESET_TO_DEFAULTS": "Reset to Defaults",
        "SAVE_SETTINGS": "Save Settings"
      }
    };

    // Then load the actual translations
    this.loadTranslations(lang);
  }

  /**
   * Load translations for a specific language
   * @param lang Language code ('en' or 'sn')
   * @private
   */
  private loadTranslations(lang: string): void {
    // Normalize the language code
    const normalizedLang = (lang || 'en').toLowerCase();
    const langPath = `assets/i18n/${normalizedLang}.json`;

    // Make a direct HTTP request to get the translation file
    this.http.get<{}>(`assets/i18n/${normalizedLang}.json`).subscribe(
      translation => {
        if (translation && Object.keys(translation).length > 0) {

          // Update both the subject and the legacy data property
          this.translationsSubject.next(translation);
          this.data = Object.assign({}, translation);

          // Update the current language
          this.currentLang = normalizedLang;
          localStorage.setItem('lang', normalizedLang);

        } else {
          console.error(`Language file ${langPath} is empty or invalid`);
          this.fallbackToEnglish(normalizedLang);
        }
      },
      error => {
        console.error(`Error loading language file: ${langPath}`, error);
        this.fallbackToEnglish(normalizedLang);
      }
    );
  }

  /**
   * Fall back to English if there's an error loading the requested language
   * @param requestedLang The language that failed to load
   * @private
   */
  private fallbackToEnglish(requestedLang: string): void {
    if (requestedLang !== 'en') {
      this.loadTranslations('en');
    } else {
      // If even English fails, use default translations
      console.error('Failed to load English translations, using defaults');
      this.translationsSubject.next(this.getDefaultTranslations());
      this.data = this.getDefaultTranslations();
    }
  }

  /**
   * Get default translations to use if all else fails
   * @private
   */
  private getDefaultTranslations(): any {
    return {
      "COMMON": {
        "ENABLED": "Enabled",
        "DISABLED": "Disabled",
        "SAVE": "Save",
        "CANCEL": "Cancel"
      },
      "SETTINGS": {
        "USER_SETTINGS": "User Settings",
        "APPLICATION_LANGUAGE": "Application Language",
        "SELECT_LANGUAGE": "Select your preferred language:"
      }
    };
  }

  /**
   * Change the current language
   * @param lang Language code ('en' or 'sn')
   * @returns Promise that resolves when translations are loaded
   */
  use(lang: string): Promise<{}> {

    return new Promise<{}>((resolve) => {
      // Force the language code to be lowercase
      const normalizedLang = (lang || 'en').toLowerCase();

      // Save the language preference immediately
      localStorage.setItem('lang', normalizedLang);
      this.currentLang = normalizedLang;

      // Load the translations directly from the file
      const langPath = `assets/i18n/${normalizedLang}.json`;

      this.http.get<{}>(langPath).subscribe(
        (translations) => {
          this.data = Object.assign({}, translations);
          this.translationsSubject.next(this.data);
          resolve(this.data);
        },
        (error) => {
          console.error(`Error loading translations for ${normalizedLang}:`, error);
          if (normalizedLang !== 'en') {
            console.log('Falling back to English');
            this.use('en').then(resolve);
          } else {
            this.data = this.getDefaultTranslations();
            this.translationsSubject.next(this.data);
            resolve(this.data);
          }
        }
      );
    });
  }

  /**
   * Get a translation by key
   * @param key Translation key (can be nested like 'SETTINGS.USER_SETTINGS')
   * @returns The translated string or the key if not found
   */
  getTranslation(key: string): string {
    if (!key) return key;

    // Get the current translations
    const translations = this.data;

    if (key.includes('.')) {
      const keys = key.split('.');
      let value = translations;

      for (const k of keys) {
        if (value && value[k] !== undefined) {
          value = value[k];
        } else {
          console.warn(`Translation key not found: ${key}`);
          return key;
        }
      }

      return value;
    }

    return translations[key] || key;
  }

  /**
   * Debug method to check if translations are loaded and control warning messages
   * @param showMissingKeys Whether to show warnings for missing translation keys (default: false)
   */
  debugTranslations(showMissingKeys: boolean = false) {

    // Set the static property in TranslatePipe to control warnings
    // We need to use the window object as an intermediary
    (window as any).TranslatePipeShowWarnings = showMissingKeys;

    if (showMissingKeys) {
      console.log('Missing translation key warnings are ENABLED');
    } else {
      console.log('Missing translation key warnings are DISABLED');
    }
  }
}
