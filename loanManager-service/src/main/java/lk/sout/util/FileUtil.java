package lk.sout.util;

import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

@Component
public class FileUtil {

    @Value("${uploadPath}")
    private String uploadPath;

    @Value("${baseDir}")
    private String baseDir;

    private static final Logger LOGGER = LoggerFactory.getLogger(FileUtil.class);

    public boolean fileUploadService(MultipartFile multipartFile, String subDir, String fileName) {
        String fileExtension = FilenameUtils.getExtension(multipartFile.getOriginalFilename());
        try {
            String basePath = baseDir + uploadPath;
            Path baseDir = Paths.get(basePath);
            Path path = Paths.get(basePath + subDir);

            if (!Files.exists(baseDir)) {
                Files.createDirectories(baseDir);
            }
            if (!Files.exists(path)) {
                Files.createDirectories(path);
            }
            try (InputStream inputStream = multipartFile.getInputStream()) {
                Path filePath = path.resolve(fileName + "." + fileExtension);
                Files.copy(inputStream, filePath, StandardCopyOption.REPLACE_EXISTING);
            } catch (IOException ioe) {
                throw new IOException("Could not save image file: " + fileName, ioe);
            }
            return true;
        } catch (Exception e) {
            LOGGER.error("Exception in fileUploadService " + e.getMessage());
        }
        return false;
    }


}
