package lk.sout.loanManager.mobileApp.service;

import lk.sout.loanManager.business.entity.*;
import lk.sout.loanManager.mobileApp.model.LoanCountStats;
import lk.sout.loanManager.mobileApp.model.LoanStat;
import lk.sout.loanManager.mobileApp.model.RecentActivity;
import lk.sout.loanManager.mobileApp.model.LoanPerformanceMetrics;
import lk.sout.loanManager.mobileApp.model.PaymentTrends;

import java.util.List;

public interface StatService {

    LoanCountStats loanCountStat();

    MonthlyStats monthlyStat();

    LoanStat ongoingLoanStat();

    LoanStat settledLoanStats();

    LoanStat allLoanStat();

    RecentActivity getRecentActivity();

    LoanPerformanceMetrics getLoanPerformanceMetrics();

    PaymentTrends getPaymentTrends();

}
