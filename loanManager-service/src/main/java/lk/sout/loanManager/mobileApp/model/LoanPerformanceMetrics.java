package lk.sout.loanManager.mobileApp.model;

import org.springframework.stereotype.Component;

@Component
public class LoanPerformanceMetrics {

    private double collectionRate;
    private double defaultRate;
    private double averagePaymentTime;
    private int totalOverdueLoans;
    private double totalOverdueAmount;
    private int loansIssuedThisMonth;
    private int loansSettledThisMonth;
    private double averageLoanAmount;
    private double recoveryRate;
    private int activeBorrowers;

    // Constructors
    public LoanPerformanceMetrics() {}

    public LoanPerformanceMetrics(double collectionRate, double defaultRate, double averagePaymentTime,
                                 int totalOverdueLoans, double totalOverdueAmount, int loansIssuedThisMonth,
                                 int loansSettledThisMonth, double averageLoanAmount, double recoveryRate,
                                 int activeBorrowers) {
        this.collectionRate = collectionRate;
        this.defaultRate = defaultRate;
        this.averagePaymentTime = averagePaymentTime;
        this.totalOverdueLoans = totalOverdueLoans;
        this.totalOverdueAmount = totalOverdueAmount;
        this.loansIssuedThisMonth = loansIssuedThisMonth;
        this.loansSettledThisMonth = loansSettledThisMonth;
        this.averageLoanAmount = averageLoanAmount;
        this.recoveryRate = recoveryRate;
        this.activeBorrowers = activeBorrowers;
    }

    // Getters and Setters
    public double getCollectionRate() {
        return collectionRate;
    }

    public void setCollectionRate(double collectionRate) {
        this.collectionRate = collectionRate;
    }

    public double getDefaultRate() {
        return defaultRate;
    }

    public void setDefaultRate(double defaultRate) {
        this.defaultRate = defaultRate;
    }

    public double getAveragePaymentTime() {
        return averagePaymentTime;
    }

    public void setAveragePaymentTime(double averagePaymentTime) {
        this.averagePaymentTime = averagePaymentTime;
    }

    public int getTotalOverdueLoans() {
        return totalOverdueLoans;
    }

    public void setTotalOverdueLoans(int totalOverdueLoans) {
        this.totalOverdueLoans = totalOverdueLoans;
    }

    public double getTotalOverdueAmount() {
        return totalOverdueAmount;
    }

    public void setTotalOverdueAmount(double totalOverdueAmount) {
        this.totalOverdueAmount = totalOverdueAmount;
    }

    public int getLoansIssuedThisMonth() {
        return loansIssuedThisMonth;
    }

    public void setLoansIssuedThisMonth(int loansIssuedThisMonth) {
        this.loansIssuedThisMonth = loansIssuedThisMonth;
    }

    public int getLoansSettledThisMonth() {
        return loansSettledThisMonth;
    }

    public void setLoansSettledThisMonth(int loansSettledThisMonth) {
        this.loansSettledThisMonth = loansSettledThisMonth;
    }

    public double getAverageLoanAmount() {
        return averageLoanAmount;
    }

    public void setAverageLoanAmount(double averageLoanAmount) {
        this.averageLoanAmount = averageLoanAmount;
    }

    public double getRecoveryRate() {
        return recoveryRate;
    }

    public void setRecoveryRate(double recoveryRate) {
        this.recoveryRate = recoveryRate;
    }

    public int getActiveBorrowers() {
        return activeBorrowers;
    }

    public void setActiveBorrowers(int activeBorrowers) {
        this.activeBorrowers = activeBorrowers;
    }
}
