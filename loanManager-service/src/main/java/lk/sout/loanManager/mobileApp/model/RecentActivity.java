package lk.sout.loanManager.mobileApp.model;

import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Component
public class RecentActivity {

    private List<RecentLoan> recentLoans;
    private List<RecentPayment> recentPayments;
    private List<RecentArrears> recentArrears;

    public static class RecentLoan {
        private String loanNo;
        private String borrowerName;
        private double loanAmount;
        private String loanPlanName;
        private LocalDate approvedDate;
        private String status;

        // Constructors
        public RecentLoan() {}

        public RecentLoan(String loanNo, String borrowerName, double loanAmount, 
                         String loanPlanName, LocalDate approvedDate, String status) {
            this.loanNo = loanNo;
            this.borrowerName = borrowerName;
            this.loanAmount = loanAmount;
            this.loanPlanName = loanPlanName;
            this.approvedDate = approvedDate;
            this.status = status;
        }

        // Getters and Setters
        public String getLoanNo() { return loanNo; }
        public void setLoanNo(String loanNo) { this.loanNo = loanNo; }

        public String getBorrowerName() { return borrowerName; }
        public void setBorrowerName(String borrowerName) { this.borrowerName = borrowerName; }

        public double getLoanAmount() { return loanAmount; }
        public void setLoanAmount(double loanAmount) { this.loanAmount = loanAmount; }

        public String getLoanPlanName() { return loanPlanName; }
        public void setLoanPlanName(String loanPlanName) { this.loanPlanName = loanPlanName; }

        public LocalDate getApprovedDate() { return approvedDate; }
        public void setApprovedDate(LocalDate approvedDate) { this.approvedDate = approvedDate; }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }

    public static class RecentPayment {
        private String loanNo;
        private String borrowerName;
        private double paymentAmount;
        private LocalDateTime paymentDate;
        private String paymentType;

        // Constructors
        public RecentPayment() {}

        public RecentPayment(String loanNo, String borrowerName, double paymentAmount, 
                           LocalDateTime paymentDate, String paymentType) {
            this.loanNo = loanNo;
            this.borrowerName = borrowerName;
            this.paymentAmount = paymentAmount;
            this.paymentDate = paymentDate;
            this.paymentType = paymentType;
        }

        // Getters and Setters
        public String getLoanNo() { return loanNo; }
        public void setLoanNo(String loanNo) { this.loanNo = loanNo; }

        public String getBorrowerName() { return borrowerName; }
        public void setBorrowerName(String borrowerName) { this.borrowerName = borrowerName; }

        public double getPaymentAmount() { return paymentAmount; }
        public void setPaymentAmount(double paymentAmount) { this.paymentAmount = paymentAmount; }

        public LocalDateTime getPaymentDate() { return paymentDate; }
        public void setPaymentDate(LocalDateTime paymentDate) { this.paymentDate = paymentDate; }

        public String getPaymentType() { return paymentType; }
        public void setPaymentType(String paymentType) { this.paymentType = paymentType; }
    }

    public static class RecentArrears {
        private String loanNo;
        private String borrowerName;
        private int daysDue;
        private double overdueAmount;
        private LocalDate dueDate;

        // Constructors
        public RecentArrears() {}

        public RecentArrears(String loanNo, String borrowerName, int daysDue, 
                           double overdueAmount, LocalDate dueDate) {
            this.loanNo = loanNo;
            this.borrowerName = borrowerName;
            this.daysDue = daysDue;
            this.overdueAmount = overdueAmount;
            this.dueDate = dueDate;
        }

        // Getters and Setters
        public String getLoanNo() { return loanNo; }
        public void setLoanNo(String loanNo) { this.loanNo = loanNo; }

        public String getBorrowerName() { return borrowerName; }
        public void setBorrowerName(String borrowerName) { this.borrowerName = borrowerName; }

        public int getDaysDue() { return daysDue; }
        public void setDaysDue(int daysDue) { this.daysDue = daysDue; }

        public double getOverdueAmount() { return overdueAmount; }
        public void setOverdueAmount(double overdueAmount) { this.overdueAmount = overdueAmount; }

        public LocalDate getDueDate() { return dueDate; }
        public void setDueDate(LocalDate dueDate) { this.dueDate = dueDate; }
    }

    // Main class getters and setters
    public List<RecentLoan> getRecentLoans() { return recentLoans; }
    public void setRecentLoans(List<RecentLoan> recentLoans) { this.recentLoans = recentLoans; }

    public List<RecentPayment> getRecentPayments() { return recentPayments; }
    public void setRecentPayments(List<RecentPayment> recentPayments) { this.recentPayments = recentPayments; }

    public List<RecentArrears> getRecentArrears() { return recentArrears; }
    public void setRecentArrears(List<RecentArrears> recentArrears) { this.recentArrears = recentArrears; }
}
