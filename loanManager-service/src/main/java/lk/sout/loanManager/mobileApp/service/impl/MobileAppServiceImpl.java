package lk.sout.loanManager.mobileApp.service.impl;

import lk.sout.core.entity.MetaData;
import lk.sout.core.service.MetaDataService;
import lk.sout.loanManager.business.entity.LoanRecord;
import lk.sout.loanManager.mobileApp.model.GroupedLoanRecord;
import lk.sout.loanManager.mobileApp.repository.MobileAppRepository;
import lk.sout.loanManager.mobileApp.service.MobileAppService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MobileAppServiceImpl implements MobileAppService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MobileAppServiceImpl.class);

    @Autowired
    MobileAppRepository mobileAppRepository;

    @Autowired
    MetaDataService metaDataService;

    @Override
    public List<GroupedLoanRecord> findTodayList() {
        try {
            MetaData paymentPending = metaDataService.searchMetaData("Payment Pending", "Loan Record Status");
            return mobileAppRepository.findByStatusGroupByBorrowerNic(paymentPending);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<GroupedLoanRecord> findTodayListByName(String name) {
        try {
            MetaData paymentPending = metaDataService.searchMetaData("Payment Pending", "Loan Record Status");
            return mobileAppRepository.findByStatusGroupByBorrowerNameLike(paymentPending, name);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<GroupedLoanRecord> findArrearsList() {
        try {
            MetaData arrears = metaDataService.searchMetaData("Arrears", "Loan Record Status");
            return mobileAppRepository.findByStatusGroupByBorrowerNic(arrears);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<GroupedLoanRecord> findArrearsListByName(String name) {
        try {
            MetaData arrears = metaDataService.searchMetaData("Arrears", "Loan Record Status");
            return mobileAppRepository.findByStatusGroupByBorrowerNameLike(arrears, name);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LoanRecord> findArrearsRecordsByNic(String nic) {
        try {
            MetaData arrears = metaDataService.searchMetaData("Arrears", "Loan Record Status");
            return mobileAppRepository.findLoanRecordByStatusAndNic(nic, arrears);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LoanRecord> findTodayRecordsByNic(String nic) {
        try {
            MetaData paymentPending = metaDataService.searchMetaData("Payment Pending", "Loan Record Status");
            return mobileAppRepository.findLoanRecordByStatusAndNic(nic, paymentPending);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

}
