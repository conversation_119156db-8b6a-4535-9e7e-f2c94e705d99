package lk.sout.loanManager.mobileApp.model;

import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON><PERSON> on 2/8/2024
 */

@Component
public class LoanCountStats {

    private double totalLoanCount;

    private double activeLoanCount;

    private double settledLoanCount;

    private double arrearsLoanCount;

    public double getTotalLoanCount() {
        return totalLoanCount;
    }

    public void setTotalLoanCount(double totalLoanCount) {
        this.totalLoanCount = totalLoanCount;
    }

    public double getActiveLoanCount() {
        return activeLoanCount;
    }

    public void setActiveLoanCount(double activeLoanCount) {
        this.activeLoanCount = activeLoanCount;
    }

    public double getSettledLoanCount() {
        return settledLoanCount;
    }

    public void setSettledLoanCount(double settledLoanCount) {
        this.settledLoanCount = settledLoanCount;
    }

    public double getArrearsLoanCount() {
        return arrearsLoanCount;
    }

    public void setArrearsLoanCount(double arrearsLoanCount) {
        this.arrearsLoanCount = arrearsLoanCount;
    }
}
