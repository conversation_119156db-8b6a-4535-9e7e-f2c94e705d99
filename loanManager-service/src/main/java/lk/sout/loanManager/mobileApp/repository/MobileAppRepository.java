package lk.sout.loanManager.mobileApp.repository;

import lk.sout.core.entity.MetaData;
import lk.sout.loanManager.business.entity.LoanRecord;
import lk.sout.loanManager.mobileApp.model.GroupedLoanRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;

/**
 * Created by <PERSON><PERSON><PERSON> on 5/27/2023
 */

@Repository
public class MobileAppRepository {

    private final MongoTemplate mongoTemplate;

    public MobileAppRepository(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public List<GroupedLoanRecord> findByStatusGroupByBorrowerNic(MetaData status) {
        try {
            TypedAggregation<LoanRecord> agg = newAggregation(LoanRecord.class,
                    project("borrowerNic", "borrowerName", "statusCode"),
                    match(Criteria.where("statusCode").regex(status.getName())),
                    group("borrowerNic", "borrowerName"),
                    project("borrowerNic", "borrowerName")
            );
            AggregationResults<GroupedLoanRecord> groupResults
                    = mongoTemplate.aggregate(agg, GroupedLoanRecord.class);
            List<GroupedLoanRecord> result = groupResults.getMappedResults();
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public List<GroupedLoanRecord> findByStatusGroupByBorrowerNameLike(MetaData status, String name) {
        try {
            TypedAggregation<LoanRecord> agg = newAggregation(LoanRecord.class,
                    project("borrowerNic", "borrowerName", "statusCode"),
                    match(Criteria.where("borrowerName").regex(".*" + name + ".*")),
                    match(Criteria.where("statusCode").regex(status.getName())),
                    group("borrowerNic", "borrowerName"),
                    project("borrowerNic", "borrowerName")
            );
            AggregationResults<GroupedLoanRecord> groupResults
                    = mongoTemplate.aggregate(agg, GroupedLoanRecord.class);
            List<GroupedLoanRecord> result = groupResults.getMappedResults();
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public List<LoanRecord> findLoanRecordByStatusAndNic(String borrowerNic, MetaData status) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("statusCode").regex(status.getName()));
            query.addCriteria(Criteria.where("borrowerNic").regex(borrowerNic));
            query.fields().include("loanPlan").include("installmentDate").include("statusCode").
                    include("borrowerTp1").include("borrowerTp2").include("installmentAmount").
                    include("balance").include("daysDue").include("borrowerAddress").
                    include("loanNo").include("borrowerName");
            return mongoTemplate.find(query, LoanRecord.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public List<LoanRecord> findAllRecordsByLoanNo(String loanNo) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("loanNo").regex(loanNo));
            return mongoTemplate.find(query, LoanRecord.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

}
