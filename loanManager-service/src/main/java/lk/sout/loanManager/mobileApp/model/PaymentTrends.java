package lk.sout.loanManager.mobileApp.model;

import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

@Component
public class PaymentTrends {

    private List<DailyPayment> last7Days;
    private List<WeeklyPayment> last4Weeks;
    private double todayCollection;
    private double weeklyAverage;
    private double monthlyTarget;
    private double monthlyProgress;

    public static class DailyPayment {
        private LocalDate date;
        private double amount;
        private int paymentCount;

        public DailyPayment() {}

        public DailyPayment(LocalDate date, double amount, int paymentCount) {
            this.date = date;
            this.amount = amount;
            this.paymentCount = paymentCount;
        }

        // Getters and Setters
        public LocalDate getDate() { return date; }
        public void setDate(LocalDate date) { this.date = date; }

        public double getAmount() { return amount; }
        public void setAmount(double amount) { this.amount = amount; }

        public int getPaymentCount() { return paymentCount; }
        public void setPaymentCount(int paymentCount) { this.paymentCount = paymentCount; }
    }

    public static class WeeklyPayment {
        private String weekLabel;
        private double amount;
        private int paymentCount;
        private LocalDate weekStart;
        private LocalDate weekEnd;

        public WeeklyPayment() {}

        public WeeklyPayment(String weekLabel, double amount, int paymentCount, 
                           LocalDate weekStart, LocalDate weekEnd) {
            this.weekLabel = weekLabel;
            this.amount = amount;
            this.paymentCount = paymentCount;
            this.weekStart = weekStart;
            this.weekEnd = weekEnd;
        }

        // Getters and Setters
        public String getWeekLabel() { return weekLabel; }
        public void setWeekLabel(String weekLabel) { this.weekLabel = weekLabel; }

        public double getAmount() { return amount; }
        public void setAmount(double amount) { this.amount = amount; }

        public int getPaymentCount() { return paymentCount; }
        public void setPaymentCount(int paymentCount) { this.paymentCount = paymentCount; }

        public LocalDate getWeekStart() { return weekStart; }
        public void setWeekStart(LocalDate weekStart) { this.weekStart = weekStart; }

        public LocalDate getWeekEnd() { return weekEnd; }
        public void setWeekEnd(LocalDate weekEnd) { this.weekEnd = weekEnd; }
    }

    // Main class getters and setters
    public List<DailyPayment> getLast7Days() { return last7Days; }
    public void setLast7Days(List<DailyPayment> last7Days) { this.last7Days = last7Days; }

    public List<WeeklyPayment> getLast4Weeks() { return last4Weeks; }
    public void setLast4Weeks(List<WeeklyPayment> last4Weeks) { this.last4Weeks = last4Weeks; }

    public double getTodayCollection() { return todayCollection; }
    public void setTodayCollection(double todayCollection) { this.todayCollection = todayCollection; }

    public double getWeeklyAverage() { return weeklyAverage; }
    public void setWeeklyAverage(double weeklyAverage) { this.weeklyAverage = weeklyAverage; }

    public double getMonthlyTarget() { return monthlyTarget; }
    public void setMonthlyTarget(double monthlyTarget) { this.monthlyTarget = monthlyTarget; }

    public double getMonthlyProgress() { return monthlyProgress; }
    public void setMonthlyProgress(double monthlyProgress) { this.monthlyProgress = monthlyProgress; }
}
