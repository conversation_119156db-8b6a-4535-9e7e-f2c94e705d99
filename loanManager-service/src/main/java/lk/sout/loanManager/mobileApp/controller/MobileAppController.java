package lk.sout.loanManager.mobileApp.controller;

import lk.sout.loanManager.business.entity.LedgerRecord;
import lk.sout.loanManager.business.entity.Loan;
import lk.sout.loanManager.business.service.*;
import lk.sout.loanManager.borrower.entity.Borrower;
import lk.sout.loanManager.borrower.service.BorrowerService;
import lk.sout.loanManager.mobileApp.service.MobileAppService;
import lk.sout.loanManager.mobileApp.service.StatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@RestController
@RequestMapping("/mobileApp")
public class MobileAppController {

    @Autowired
    MobileAppService mobileAppService;

    @Autowired
    RouteService routeService;

    @Autowired
    BorrowerService borrowerService;

    @Autowired
    LoanPlanService loanPlanService;

    @Autowired
    LoanService loanService;

    @Autowired
    LoanRecordService loanRecordService;

    @Autowired
    RejectedLoanService rejectedLoanService;

    @Autowired
    LedgerRecordService ledgerRecordService;

    @Autowired
    LedgerService ledgerService;

    @Autowired
    StatService statService;

    @Autowired
    LoanPaymentService loanPaymentService;

    @RequestMapping(value = "/findAllRoutesForSelect", method = RequestMethod.GET)
    private ResponseEntity<?> findAllActiveForSelect() {
        try {
            return ResponseEntity.ok(routeService.findAllActiveForSelect());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/saveBorrower", method = RequestMethod.POST)
    private ResponseEntity<?> saveBorrower(@RequestBody Borrower borrower) {
        try {
            return ResponseEntity.ok(borrowerService.save(borrower));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllLoanPlans", method = RequestMethod.GET)
    public ResponseEntity<?> findAllLoanPlans() {
        return ResponseEntity.ok(loanPlanService.findAll());
    }


    @RequestMapping(value = "/findTodayList", method = RequestMethod.GET)
    public ResponseEntity<?> findTodayList() {
        return ResponseEntity.ok(mobileAppService.findTodayList());
    }

    @RequestMapping(value = "/findTodayRecordsByNic", method = RequestMethod.GET)
    public ResponseEntity<?> findTodayRecordsByNic(@RequestParam String nic) {
        return ResponseEntity.ok(mobileAppService.findTodayRecordsByNic(nic));
    }

    @RequestMapping(value = "/findTodayPaymentList", method = RequestMethod.GET)
    public ResponseEntity<?> findTodayPaymentList() {
        return ResponseEntity.ok(loanPaymentService.findTodayPaymentList());
    }

    @RequestMapping(value = "/findPaymentsByDate", method = RequestMethod.GET)
    public ResponseEntity<?> findPaymentsByDate(@RequestParam("date") String date) {
        return ResponseEntity.ok(loanPaymentService.findPaymentByDate(LocalDate.parse(date)));
    }

    @RequestMapping(value = "/findLoanRecordById", method = RequestMethod.GET)
    public ResponseEntity<?> findLoanRecordById(@RequestParam String id) {
        return ResponseEntity.ok(loanRecordService.findById(id));
    }

    @RequestMapping(value = "/findLoanByBorrowerNic", method = RequestMethod.GET)
    public ResponseEntity<?> findLoanByCustomer(@RequestParam String nic) {
        return ResponseEntity.ok(loanService.findByBorrowerNic(nic));
    }

    @RequestMapping(value = "/findTodayLoanByName", method = RequestMethod.GET)
    public ResponseEntity<?> findTodayLoanByName(@RequestParam String name) {
        return ResponseEntity.ok(mobileAppService.findTodayListByName(name));
    }

    @RequestMapping(value = "/findActiveLoanByNic", method = RequestMethod.GET)
    public ResponseEntity<?> findActiveLoanByNic(@RequestParam String nic) {
        return ResponseEntity.ok(loanService.findActiveLoanByNic(nic));
    }

    @RequestMapping(value = "/findArrearsList", method = RequestMethod.GET)
    public ResponseEntity<?> findArrearsList() {
        return ResponseEntity.ok(mobileAppService.findArrearsList());
    }

    @RequestMapping(value = "/findArrearsRecordsByNic", method = RequestMethod.GET)
    public ResponseEntity<?> findArrearsRecordsByNic(@RequestParam String nic) {
        return ResponseEntity.ok(mobileAppService.findArrearsRecordsByNic(nic));
    }

    @RequestMapping(value = "/findArrearsLoanByName", method = RequestMethod.GET)
    public ResponseEntity<?> findArrearsLoanByName(@RequestParam String name) {
        return ResponseEntity.ok(mobileAppService.findArrearsListByName(name));
    }

    @RequestMapping(value = "/findRecordsByLoanNo", method = RequestMethod.GET)
    public ResponseEntity<?> findRecordsByLoanNo(@RequestParam String loanNo) {
        return ResponseEntity.ok(loanRecordService.findAllRecordsByLoanNo(loanNo));
    }

    @RequestMapping(value = "/findLoanByLoanNo", method = RequestMethod.GET)
    public ResponseEntity<?> findLoanByLoanNo(@RequestParam String loanNo) {
        return ResponseEntity.ok(loanService.findByLoanNo(loanNo));
    }

    @RequestMapping(value = "/findPendingRecordsByLoanNo", method = RequestMethod.GET)
    public ResponseEntity<?> findPendingRecordsByLoanNo(@RequestParam String loanNo) {
        return ResponseEntity.ok(loanRecordService.findAllRecordsByLoanNo(loanNo));
    }

    @RequestMapping(value = "/findBorrowerByMobile", method = RequestMethod.GET)
    private ResponseEntity<?> findBorrowerByMobile(@RequestParam String tp) {
        try {
            return ResponseEntity.ok(borrowerService.findOneByTp(tp));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findBorrowerByNic", method = RequestMethod.GET)
    private ResponseEntity<?> findBorrowerByNic(@RequestParam String nic) {
        try {
            return ResponseEntity.ok(borrowerService.findByNic(nic));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findBorrowerByNameLike", method = RequestMethod.GET)
    public ResponseEntity<?> findBorrowerByNameLike(@RequestParam String name) {
        return ResponseEntity.ok(borrowerService.findAllByNameLikeIgnoreCaseAndActive(name, true));
    }

    @RequestMapping(value = "/findBorrowerByTpLike", method = RequestMethod.GET)
    private ResponseEntity<?> findBorrowerByTpLike(@RequestParam String tp) {
        try {
            return ResponseEntity.ok(borrowerService.findByTp(tp));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/saveLoan", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> saveLoan(@RequestBody Loan loan) {
        try {
            return ResponseEntity.ok(loanService.create(loan, "Mobile"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/payLoanRecord", method = RequestMethod.GET)
    private ResponseEntity<?> payLoanRecord(@RequestParam String loanRecordId, @RequestParam double payment,
                                            @RequestParam String appNo) {
        try {
            return ResponseEntity.ok(loanRecordService.pay(loanRecordId, payment, appNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/createAndPayLoanRecord", method = RequestMethod.GET)
    private ResponseEntity<?> createAndPayLoanRecord(@RequestParam String loanNo, @RequestParam double payment,
                                                     @RequestParam String appNo) {
        try {
            return ResponseEntity.ok(loanRecordService.createAndPayLoanRecord(loanNo, payment, appNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/ongoingLoanStat", method = RequestMethod.GET)
    private ResponseEntity<?> ongoingLoanStat() {
        try {
            return ResponseEntity.ok(statService.ongoingLoanStat());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/loanCountStat", method = RequestMethod.GET)
    private ResponseEntity<?> loanCountStat() {
        try {
            return ResponseEntity.ok(statService.loanCountStat());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/monthlyStat", method = RequestMethod.GET)
    private ResponseEntity<?> monthlyStat() {
        try {
            return ResponseEntity.ok(statService.monthlyStat());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/settledLoanStats", method = RequestMethod.GET)
    private ResponseEntity<?> settledLoanStats() {
        try {
            return ResponseEntity.ok(statService.settledLoanStats());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/allLoanStat", method = RequestMethod.GET)
    private ResponseEntity<?> allLoanStat() {
        try {
            return ResponseEntity.ok(statService.allLoanStat());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/amendPayment", method = RequestMethod.GET)
    private ResponseEntity<?> amendPayment(@RequestParam String loanRecordId,
                                           @RequestParam String paymentId, @RequestParam String appNo) {
        try {
            return ResponseEntity.ok(loanRecordService.amend(loanRecordId, paymentId, appNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/getLedgerByAppNo", method = RequestMethod.GET)
    private ResponseEntity<?> findLedger(@RequestParam String appNo) {
        try {
            return ResponseEntity.ok(ledgerService.findByAppNo(appNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/correctLedger", method = RequestMethod.GET)
    private ResponseEntity<?> correctLedger(@RequestParam String appNo, @RequestParam double amount) {
        try {
            return ResponseEntity.ok(ledgerService.correctBalance(appNo, amount));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/settleLoan", method = RequestMethod.GET)
    private ResponseEntity<?> settleLoan(@RequestParam String loanNo) {
        try {
            return ResponseEntity.ok(loanService.settleLoan(loanNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/saveLedgerRec", method = RequestMethod.POST)
    private ResponseEntity<?> saveLedgerRec(@RequestBody LedgerRecord ledgerRecord) {
        try {
            return ResponseEntity.ok(ledgerRecordService.save(ledgerRecord));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findLedgerRecByDateAndAppNo", method = RequestMethod.GET)
    private ResponseEntity<?> findLedgerRecByDateAndAppNo(@RequestParam String fromDate,
                                                          @RequestParam String toDate,
                                                          @RequestParam String appNo) {
        try {
            return ResponseEntity.ok(ledgerRecordService.findByDateAndAppNo(LocalDate.parse(fromDate),
                    LocalDate.parse(toDate), appNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/getLedgerByTypeDateAndAppNo", method = RequestMethod.GET)
    private ResponseEntity<?> getLedgerByTypeDateAndAppNo(@RequestParam String fromDate,
                                                          @RequestParam String toDate,
                                                          @RequestParam String appNo, @RequestParam String type) {
        try {
            return ResponseEntity.ok(ledgerRecordService.findByDateAndAppNoAndReason(LocalDate.parse(fromDate),
                    LocalDate.parse(toDate), appNo, type));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    // Pending Loans Management
    @RequestMapping(value = "/pendingLoans", method = RequestMethod.GET)
    public ResponseEntity<?> getPendingLoans(@RequestParam(defaultValue = "0") int page,
                                           @RequestParam(defaultValue = "10") int size) {
        try {
            return ResponseEntity.ok(loanService.findAllPending(org.springframework.data.domain.PageRequest.of(page, size)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(value = "/approveLoan", method = RequestMethod.POST)
    public ResponseEntity<?> approveLoan(@RequestParam String loanNo) {
        try {
            boolean result = loanService.approve(loanNo);
            if (result) {
                return ResponseEntity.ok("Loan approved successfully");
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Failed to approve loan");
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error approving loan: " + e.getMessage());
        }
    }

    @RequestMapping(value = "/rejectLoan", method = RequestMethod.POST)
    public ResponseEntity<?> rejectLoan(@RequestParam String loanNo, @RequestParam String reason) {
        try {
            if (reason == null || reason.trim().isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Rejection reason is required");
            }
            boolean result = loanService.reject(loanNo, reason.trim());
            if (result) {
                return ResponseEntity.ok("Loan rejected successfully");
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Failed to reject loan");
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error rejecting loan: " + e.getMessage());
        }
    }

    // Rejected Loans Management
    @RequestMapping(value = "/rejectedLoans", method = RequestMethod.GET)
    public ResponseEntity<?> getRejectedLoans(@RequestParam(defaultValue = "0") int page,
                                            @RequestParam(defaultValue = "10") int size) {
        try {
            return ResponseEntity.ok(rejectedLoanService.findAll(org.springframework.data.domain.PageRequest.of(page, size)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(value = "/rejectedLoans/search", method = RequestMethod.GET)
    public ResponseEntity<?> searchRejectedLoans(@RequestParam(required = false) String customerName,
                                                @RequestParam(required = false) String customerNic,
                                                @RequestParam(required = false) String telephone) {
        try {
            if (customerName != null && !customerName.trim().isEmpty()) {
                return ResponseEntity.ok(rejectedLoanService.findByCustomerName(customerName.trim()));
            } else if (customerNic != null && !customerNic.trim().isEmpty()) {
                return ResponseEntity.ok(rejectedLoanService.findByCustomerNic(customerNic.trim()));
            } else if (telephone != null && !telephone.trim().isEmpty()) {
                return ResponseEntity.ok(rejectedLoanService.findByTelephone(telephone.trim()));
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("At least one search parameter is required");
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

}
