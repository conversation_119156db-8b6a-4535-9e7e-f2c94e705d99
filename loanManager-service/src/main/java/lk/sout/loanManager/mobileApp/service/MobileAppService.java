package lk.sout.loanManager.mobileApp.service;

import lk.sout.loanManager.business.entity.LoanRecord;
import lk.sout.loanManager.mobileApp.model.GroupedLoanRecord;

import java.util.List;

public interface MobileAppService {

    List<GroupedLoanRecord> findTodayList();

    List<GroupedLoanRecord> findArrearsList();

    List<GroupedLoanRecord> findTodayListByName(String name);

    List<GroupedLoanRecord> findArrearsListByName(String name);

    List<LoanRecord> findArrearsRecordsByNic(String nic);

    List<LoanRecord> findTodayRecordsByNic(String nic);

}
