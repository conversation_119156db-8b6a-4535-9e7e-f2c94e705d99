package lk.sout.loanManager.mobileApp.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.core.service.MetaDataService;
import lk.sout.loanManager.business.entity.*;
import lk.sout.loanManager.business.repository.LoanRecordRepository;
import lk.sout.loanManager.business.service.LoanPaymentService;
import lk.sout.loanManager.business.service.LoanService;
import lk.sout.loanManager.mobileApp.model.LoanCountStats;
import lk.sout.loanManager.mobileApp.model.LoanStat;
import lk.sout.loanManager.mobileApp.model.RecentActivity;
import lk.sout.loanManager.mobileApp.model.LoanPerformanceMetrics;
import lk.sout.loanManager.mobileApp.model.PaymentTrends;
import lk.sout.loanManager.mobileApp.repository.StatRepository;
import lk.sout.loanManager.mobileApp.service.StatService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class StatServiceImpl implements StatService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StatServiceImpl.class);

    @Autowired
    LoanRecordRepository loanRecordRepository;

    @Autowired
    Response response;

    @Autowired
    LoanService loanService;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    LoanPaymentService loanPaymentService;

    @Autowired
    StatRepository statRepository;

    @Override
    public LoanStat ongoingLoanStat() {
        try {
            return statRepository.ongoingLoanStat();
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    @Override
    public LoanCountStats loanCountStat() {
        try {
            return statRepository.loanCountStat();
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    @Override
    public MonthlyStats monthlyStat() {
        try {
            return statRepository.monthlyStat();
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    @Override
    public LoanStat settledLoanStats() {
        try {
            return statRepository.settledLoanStat();
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    @Override
    public LoanStat allLoanStat() {
        try {
            return statRepository.allLoanStat();
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    @Override
    public RecentActivity getRecentActivity() {
        try {
            return statRepository.getRecentActivity();
        } catch (Exception ex) {
            LOGGER.error("Get Recent Activity Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public LoanPerformanceMetrics getLoanPerformanceMetrics() {
        try {
            return statRepository.getLoanPerformanceMetrics();
        } catch (Exception ex) {
            LOGGER.error("Get Loan Performance Metrics Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public PaymentTrends getPaymentTrends() {
        try {
            return statRepository.getPaymentTrends();
        } catch (Exception ex) {
            LOGGER.error("Get Payment Trends Failed: " + ex.getMessage());
            return null;
        }
    }
}
