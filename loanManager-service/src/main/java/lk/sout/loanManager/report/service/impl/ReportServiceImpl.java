package lk.sout.loanManager.report.service.impl;

import lk.sout.loanManager.borrower.entity.Borrower;
import lk.sout.loanManager.borrower.service.BorrowerService;
import lk.sout.loanManager.business.entity.Loan;
import lk.sout.loanManager.business.entity.Payment;
import lk.sout.loanManager.business.service.LoanPaymentService;
import lk.sout.loanManager.business.service.LoanService;
import lk.sout.loanManager.report.dto.BorrowerReportData;
import lk.sout.loanManager.report.dto.IncomeReportData;
import lk.sout.loanManager.report.service.ReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ReportServiceImpl implements ReportService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReportServiceImpl.class);

    @Autowired
    private BorrowerService borrowerService;

    @Autowired
    private LoanService loanService;

    @Autowired
    private LoanPaymentService loanPaymentService;

    @Override
    public List<BorrowerReportData> getBorrowerReport() {
        try {
            List<Borrower> borrowers = borrowerService.findAllActive(true);
            List<BorrowerReportData> reportData = new ArrayList<>();

            for (Borrower borrower : borrowers) {
                BorrowerReportData data = createBorrowerReportData(borrower);
                reportData.add(data);
            }

            return reportData;
        } catch (Exception ex) {
            LOGGER.error("Error generating borrower report: " + ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    @Override
    public List<BorrowerReportData> getBorrowerReportByDateRange(LocalDate startDate, LocalDate endDate) {
        try {
            // For now, return all borrowers - can be enhanced to filter by date range
            return getBorrowerReport();
        } catch (Exception ex) {
            LOGGER.error("Error generating borrower report by date range: " + ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    @Override
    public List<BorrowerReportData> getBorrowerReportByStatus(String status) {
        try {
            // For now, return all borrowers - can be enhanced to filter by status
            return getBorrowerReport();
        } catch (Exception ex) {
            LOGGER.error("Error generating borrower report by status: " + ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    @Override
    public List<IncomeReportData> getIncomeReport(LocalDate startDate, LocalDate endDate, String reportType) {
        try {
            List<Payment> payments = loanPaymentService.findPaymentByDateRange(startDate, endDate);
            List<IncomeReportData> reportData = new ArrayList<>();

            // Group payments by date
            Map<LocalDate, List<Payment>> paymentsByDate = payments.stream()
                    .collect(Collectors.groupingBy(payment -> payment.getDateTime().toLocalDate()));

            for (Map.Entry<LocalDate, List<Payment>> entry : paymentsByDate.entrySet()) {
                LocalDate date = entry.getKey();
                List<Payment> dailyPayments = entry.getValue();

                double loanPayments = dailyPayments.stream()
                        .mapToDouble(Payment::getAmount)
                        .sum();

                double interestEarned = dailyPayments.stream()
                        .mapToDouble(Payment::getProfit)
                        .sum();

                double penaltyCharges = 0; // Can be enhanced to calculate penalty charges

                double totalIncome = loanPayments + interestEarned + penaltyCharges;
                double expenses = 0; // Can be enhanced to calculate actual expenses
                double netIncome = totalIncome - expenses;

                IncomeReportData data = new IncomeReportData(
                        date, loanPayments, interestEarned, penaltyCharges,
                        0, totalIncome, expenses, netIncome
                );
                reportData.add(data);
            }

            return reportData;
        } catch (Exception ex) {
            LOGGER.error("Error generating income report: " + ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    @Override
    public List<IncomeReportData> getMonthlyIncomeReport(int year, int month) {
        try {
            LocalDate startDate = LocalDate.of(year, month, 1);
            LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
            return getIncomeReport(startDate, endDate, "monthly");
        } catch (Exception ex) {
            LOGGER.error("Error generating monthly income report: " + ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    @Override
    public List<IncomeReportData> getYearlyIncomeReport(int year) {
        try {
            LocalDate startDate = LocalDate.of(year, 1, 1);
            LocalDate endDate = LocalDate.of(year, 12, 31);
            return getIncomeReport(startDate, endDate, "yearly");
        } catch (Exception ex) {
            LOGGER.error("Error generating yearly income report: " + ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    private BorrowerReportData createBorrowerReportData(Borrower borrower) {
        try {
            // Get loans for this borrower
            Iterable<Loan> loans = loanService.findByBorrowerNic(borrower.getNic());
            List<Loan> loanList = new ArrayList<>();
            loans.forEach(loanList::add);

            int totalLoans = loanList.size();
            double totalAmount = loanList.stream().mapToDouble(Loan::getLoanAmount).sum();
            
            // Calculate total paid and outstanding amounts
            double totalPaid = 0;
            double totalOutstanding = 0;
            LocalDate lastLoanDate = null;

            for (Loan loan : loanList) {
                // This would need to be implemented based on your payment tracking
                // For now, using placeholder values
                totalPaid += loan.getLoanAmount() * 0.5; // Placeholder
                totalOutstanding += loan.getLoanAmount() * 0.5; // Placeholder
                
                if (lastLoanDate == null || loan.getCreatedDate().isAfter(lastLoanDate)) {
                    lastLoanDate = loan.getCreatedDate();
                }
            }

            String[] nameParts = borrower.getName().split(" ", 2);
            String firstName = nameParts.length > 0 ? nameParts[0] : "";
            String lastName = nameParts.length > 1 ? nameParts[1] : "";

            return new BorrowerReportData(
                    borrower.getId(),
                    firstName,
                    lastName,
                    borrower.getNic(),
                    borrower.getTelephone1(),
                    borrower.getAddress(),
                    totalLoans,
                    totalAmount,
                    totalPaid,
                    totalOutstanding,
                    lastLoanDate,
                    borrower.isActive() ? "Active" : "Inactive"
            );
        } catch (Exception ex) {
            LOGGER.error("Error creating borrower report data for borrower: " + borrower.getId(), ex);
            return new BorrowerReportData();
        }
    }
}
