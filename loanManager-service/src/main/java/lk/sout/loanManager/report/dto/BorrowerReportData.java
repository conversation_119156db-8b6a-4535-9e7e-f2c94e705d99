package lk.sout.loanManager.report.dto;

import java.time.LocalDate;

public class BorrowerReportData {
    private String id;
    private String firstName;
    private String lastName;
    private String nic;
    private String phoneNumber;
    private String address;
    private int totalLoans;
    private double totalAmount;
    private double totalPaid;
    private double totalOutstanding;
    private LocalDate lastLoanDate;
    private String status;

    // Default constructor
    public BorrowerReportData() {}

    // Constructor with all fields
    public BorrowerReportData(String id, String firstName, String lastName, String nic, 
                             String phoneNumber, String address, int totalLoans, 
                             double totalAmount, double totalPaid, double totalOutstanding, 
                             LocalDate lastLoanDate, String status) {
        this.id = id;
        this.firstName = firstName;
        this.lastName = lastName;
        this.nic = nic;
        this.phoneNumber = phoneNumber;
        this.address = address;
        this.totalLoans = totalLoans;
        this.totalAmount = totalAmount;
        this.totalPaid = totalPaid;
        this.totalOutstanding = totalOutstanding;
        this.lastLoanDate = lastLoanDate;
        this.status = status;
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }

    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }

    public String getNic() { return nic; }
    public void setNic(String nic) { this.nic = nic; }

    public String getPhoneNumber() { return phoneNumber; }
    public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }

    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }

    public int getTotalLoans() { return totalLoans; }
    public void setTotalLoans(int totalLoans) { this.totalLoans = totalLoans; }

    public double getTotalAmount() { return totalAmount; }
    public void setTotalAmount(double totalAmount) { this.totalAmount = totalAmount; }

    public double getTotalPaid() { return totalPaid; }
    public void setTotalPaid(double totalPaid) { this.totalPaid = totalPaid; }

    public double getTotalOutstanding() { return totalOutstanding; }
    public void setTotalOutstanding(double totalOutstanding) { this.totalOutstanding = totalOutstanding; }

    public LocalDate getLastLoanDate() { return lastLoanDate; }
    public void setLastLoanDate(LocalDate lastLoanDate) { this.lastLoanDate = lastLoanDate; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
}
