package lk.sout.loanManager.report.service;

import lk.sout.loanManager.report.dto.BorrowerReportData;
import lk.sout.loanManager.report.dto.IncomeReportData;

import java.time.LocalDate;
import java.util.List;

public interface ReportService {
    
    List<BorrowerReportData> getBorrowerReport();
    
    List<BorrowerReportData> getBorrowerReportByDateRange(LocalDate startDate, LocalDate endDate);
    
    List<BorrowerReportData> getBorrowerReportByStatus(String status);
    
    List<IncomeReportData> getIncomeReport(LocalDate startDate, LocalDate endDate, String reportType);
    
    List<IncomeReportData> getMonthlyIncomeReport(int year, int month);
    
    List<IncomeReportData> getYearlyIncomeReport(int year);
}
