package lk.sout.loanManager.report.dto;

import java.time.LocalDate;

public class IncomeReportData {
    private LocalDate date;
    private double loanPayments;
    private double interestEarned;
    private double penaltyCharges;
    private double otherIncome;
    private double totalIncome;
    private double expenses;
    private double netIncome;

    // Default constructor
    public IncomeReportData() {}

    // Constructor with all fields
    public IncomeReportData(LocalDate date, double loanPayments, double interestEarned, 
                           double penaltyCharges, double otherIncome, double totalIncome, 
                           double expenses, double netIncome) {
        this.date = date;
        this.loanPayments = loanPayments;
        this.interestEarned = interestEarned;
        this.penaltyCharges = penaltyCharges;
        this.otherIncome = otherIncome;
        this.totalIncome = totalIncome;
        this.expenses = expenses;
        this.netIncome = netIncome;
    }

    // Getters and Setters
    public LocalDate getDate() { return date; }
    public void setDate(LocalDate date) { this.date = date; }

    public double getLoanPayments() { return loanPayments; }
    public void setLoanPayments(double loanPayments) { this.loanPayments = loanPayments; }

    public double getInterestEarned() { return interestEarned; }
    public void setInterestEarned(double interestEarned) { this.interestEarned = interestEarned; }

    public double getPenaltyCharges() { return penaltyCharges; }
    public void setPenaltyCharges(double penaltyCharges) { this.penaltyCharges = penaltyCharges; }

    public double getOtherIncome() { return otherIncome; }
    public void setOtherIncome(double otherIncome) { this.otherIncome = otherIncome; }

    public double getTotalIncome() { return totalIncome; }
    public void setTotalIncome(double totalIncome) { this.totalIncome = totalIncome; }

    public double getExpenses() { return expenses; }
    public void setExpenses(double expenses) { this.expenses = expenses; }

    public double getNetIncome() { return netIncome; }
    public void setNetIncome(double netIncome) { this.netIncome = netIncome; }
}
