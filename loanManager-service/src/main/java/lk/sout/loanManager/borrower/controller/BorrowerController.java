package lk.sout.loanManager.borrower.controller;

import lk.sout.loanManager.borrower.entity.Borrower;
import lk.sout.loanManager.borrower.service.BorrowerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Created by Mad<PERSON>a Weerasinghe on 12/15/2019
 */
@RestController
@RequestMapping("/customer")
public class BorrowerController {

    @Autowired
    private BorrowerService borrowerService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody Borrower borrower) {
        try {
            return ResponseEntity.ok(borrowerService.save(borrower));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public ResponseEntity<?> findAllCustomers(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        return ResponseEntity.ok(borrowerService.findAll(PageRequest.of(Integer.parseInt(page),
                Integer.parseInt(pageSize))));
    }


    @RequestMapping(value = "/searchByName", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchByName(@RequestParam String name) {
        try {
            return ResponseEntity.ok(borrowerService.findAllByNameLikeIgnoreCaseAndActive(name, true));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchByTp", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchByTp(@RequestParam String tp) {
        try {
            return ResponseEntity.ok(borrowerService.findByTp(tp));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchByNic", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchByNic(@RequestParam String nic) {
        try {
            return ResponseEntity.ok(borrowerService.findAllByActiveAndNicLikeIgnoreCase(nic, true));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


    @RequestMapping(value = "/findById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findById(@RequestParam String id) {
        try {
            return ResponseEntity.ok(borrowerService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }

    }

    @RequestMapping(value = "/checkNic", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> checkNicAvailability(@RequestParam("nic") String nic) {
        try {
            return ResponseEntity.ok(borrowerService.checkNic(nic));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}
