package lk.sout.loanManager.borrower.service.impl;

import lk.sout.loanManager.borrower.entity.Borrower;
import lk.sout.loanManager.borrower.repository.BorrowerRepository;
import lk.sout.loanManager.borrower.service.BorrowerService;
import lk.sout.core.entity.Response;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by Mad<PERSON>a Weerasinghe on 12/15/2019
 */

@Service
public class BorrowerServiceImpl implements BorrowerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BorrowerServiceImpl.class);

    @Autowired
    BorrowerRepository borrowerRepository;

    @Autowired
    Response response;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    UserService userService;

    @Override
    public Response save(Borrower borrower) {
        try {
            Borrower existingByTp = borrowerRepository.findByTelephone1(borrower.getTelephone1().trim());
            Borrower existingByNic = borrowerRepository.findByNic(borrower.getNic().trim().toUpperCase());
            if (existingByTp != null || existingByNic != null) {
                response.setCode(300);
                response.setMessage("Borrower Already Created");
                response.setData(existingByTp != null ? existingByTp.getId() : existingByNic.getId());
                return response;
            }
            borrower.setActive(true);
            borrower.setNic(borrower.getNic().trim().toUpperCase());
            borrower.setTelephone1(borrower.getTelephone1().trim());
            if (borrower.getTelephone2() != null) {
                borrower.setTelephone2(borrower.getTelephone2().trim());
            }
            borrower = borrowerRepository.save(borrower);
            response.setCode(200);
            response.setMessage("Borrower Created Successfully");
            response.setData(borrower.getId());
            return response;
        } catch (Exception ex) {
            LOGGER.error("Creating Borrower Failed ", ex);
            response.setCode(401);
            response.setMessage("Creating Borrower Failed");
            response.setData(ex.getMessage());
            return response;
        }
    }

    @Override
    public Iterable<Borrower> findAll(Pageable pageable) {
        try {
            return borrowerRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Borrower Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Borrower> findAllActive(boolean result) {
        try {
            return borrowerRepository.findAllByActive(result);
        } catch (Exception ex) {
            LOGGER.error("Find All Borrowers Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Borrower findById(String id) {
        try {
            return borrowerRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.error("Find All Borrowers Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Borrower findOneByTp(String tp) {
        try {
            return borrowerRepository.findByTelephone1(tp);
        } catch (Exception ex) {
            LOGGER.error("Find All Borrowers Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Borrower findByNic(String nic) {
        try {
            return borrowerRepository.findByNic(nic);
        } catch (Exception ex) {
            LOGGER.error("Find by NIC Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Borrower> findByTp(String tp) {
        try {
            return borrowerRepository.findByTelephone1LikeOrTelephone2Like(tp);
        } catch (Exception ex) {
            LOGGER.error("Find by Tp Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Borrower> findAllByNameLikeIgnoreCaseAndActive(String code, Boolean active) {
        return borrowerRepository.findAllByActiveAndNameLikeIgnoreCase(active, code);
    }

    @Override
    public List<Borrower> findAllByActiveAndNicLikeIgnoreCase(String code, Boolean active) {
        return borrowerRepository.findAllByActiveAndNicLikeIgnoreCase(active, code);
    }

    public Borrower findDefaultCustomer() {
        Borrower borrower = borrowerRepository.findByName("Default Borrower");
        return borrower;
    }

    @Override
    public boolean checkNic(String nic) {
        try {
            if (null != borrowerRepository.findByNic(nic)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            LOGGER.error("check Borrower by nic Failed: " + ex.getMessage());
            return false;
        }
    }

}
