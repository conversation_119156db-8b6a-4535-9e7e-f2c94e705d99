package lk.sout.loanManager.borrower.service;

import lk.sout.core.entity.Response;
import lk.sout.loanManager.borrower.entity.Borrower;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 12/15/2019
 */
public interface BorrowerService {

    Response save(<PERSON>rrow<PERSON> borrower);

    Iterable<Borrower> findAll(Pageable pageable);

    List<Borrower> findAllActive(boolean result);

    Borrower findByNic(String nicBr);

    List<Borrower> findByTp(String tp);

    Borrower findById(String id);

    Borrower findOneByTp(String id);

    Borrower findDefaultCustomer();

    List<Borrower> findAllByNameLikeIgnoreCaseAndActive(String name, Boolean active);

    List<Borrower> findAllByActiveAndNicLikeIgnoreCase(String nic, Boolean active);

    boolean checkNic(String nic);
}
