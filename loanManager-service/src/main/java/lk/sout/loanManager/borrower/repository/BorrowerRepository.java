package lk.sout.loanManager.borrower.repository;

import lk.sout.loanManager.borrower.entity.Borrower;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 12/15/2019
 */
@Repository
public interface BorrowerRepository extends MongoRepository<Borrower, String> {

    Borrower findByName(String defaultCustomer);

    List<Borrower> findAllByActive(boolean active);

    List<Borrower> findAllByActiveAndNameLikeIgnoreCase(boolean active, String name);

    List<Borrower> findAllByActiveAndNicLikeIgnoreCase(boolean active, String name);

    Borrower findByNic(String nic);

    @Query("{'$or':[{'telephone1': {$regex : ?0, $options: 'i'}}, {'telephone2': {$regex : ?0, $options: 'i'}}]}")
    List<Borrower> findByTelephone1LikeOrTelephone2Like(String telephone);

    Borrower findByTelephone1(String telephone);

}
