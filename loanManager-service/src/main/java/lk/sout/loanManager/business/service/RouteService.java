package lk.sout.loanManager.business.service;

import lk.sout.loanManager.business.entity.Route;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface RouteService {

    boolean save(Route route);

    boolean remove(Route route);

    Iterable<Route> findAll(Pageable pageable);

    Iterable<Route> findAllActiveForSelect();

    Route findOne(String id);

    List<Route> findByName(String key);

    String delete(String id);
}
