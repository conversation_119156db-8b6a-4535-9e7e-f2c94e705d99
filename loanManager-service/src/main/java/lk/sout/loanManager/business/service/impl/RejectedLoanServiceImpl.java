package lk.sout.loanManager.business.service.impl;

import lk.sout.loanManager.business.entity.RejectedLoan;
import lk.sout.loanManager.business.repository.RejectedLoanRepository;
import lk.sout.loanManager.business.service.RejectedLoanService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class RejectedLoanServiceImpl implements RejectedLoanService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RejectedLoanServiceImpl.class);

    @Autowired
    private RejectedLoanRepository rejectedLoanRepository;

    @Override
    public RejectedLoan save(RejectedLoan rejectedLoan) {
        try {
            return rejectedLoanRepository.save(rejectedLoan);
        } catch (Exception ex) {
            LOGGER.error("Saving Rejected Loan Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public RejectedLoan findById(String id) {
        try {
            return rejectedLoanRepository.findById(id).orElse(null);
        } catch (Exception ex) {
            LOGGER.error("Find Rejected Loan by ID Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public RejectedLoan findByLoanNo(String loanNo) {
        try {
            return rejectedLoanRepository.findByLoanNo(loanNo);
        } catch (Exception ex) {
            LOGGER.error("Find Rejected Loan by Loan No Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<RejectedLoan> findByCustomerNic(String customerNic) {
        try {
            return rejectedLoanRepository.findByCustomerNic(customerNic);
        } catch (Exception ex) {
            LOGGER.error("Find Rejected Loans by Customer NIC Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<RejectedLoan> findByCustomerName(String customerName) {
        try {
            return rejectedLoanRepository.findByCustomerNameContainingIgnoreCase(customerName);
        } catch (Exception ex) {
            LOGGER.error("Find Rejected Loans by Customer Name Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<RejectedLoan> findByTelephone(String telephone) {
        try {
            return rejectedLoanRepository.findByTelephoneContaining(telephone);
        } catch (Exception ex) {
            LOGGER.error("Find Rejected Loans by Telephone Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<RejectedLoan> findByRejectedBy(String rejectedBy) {
        try {
            return rejectedLoanRepository.findByRejectedBy(rejectedBy);
        } catch (Exception ex) {
            LOGGER.error("Find Rejected Loans by Rejected By Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<RejectedLoan> findByRejectedDate(LocalDate rejectedDate) {
        try {
            return rejectedLoanRepository.findByRejectedDate(rejectedDate);
        } catch (Exception ex) {
            LOGGER.error("Find Rejected Loans by Rejected Date Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<RejectedLoan> findByDateRange(LocalDate startDate, LocalDate endDate) {
        try {
            return rejectedLoanRepository.findByRejectedDateBetween(startDate, endDate);
        } catch (Exception ex) {
            LOGGER.error("Find Rejected Loans by Date Range Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Page<RejectedLoan> findAll(Pageable pageable) {
        try {
            return rejectedLoanRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Rejected Loans (Pageable) Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<RejectedLoan> findAll() {
        try {
            return rejectedLoanRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("Find All Rejected Loans Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean delete(String id) {
        try {
            rejectedLoanRepository.deleteById(id);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Delete Rejected Loan Failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public long countByDate(LocalDate date) {
        try {
            return rejectedLoanRepository.countByRejectedDate(date);
        } catch (Exception ex) {
            LOGGER.error("Count Rejected Loans by Date Failed: " + ex.getMessage());
            return 0;
        }
    }

    @Override
    public long countByDateRange(LocalDate startDate, LocalDate endDate) {
        try {
            return rejectedLoanRepository.countByRejectedDateBetween(startDate, endDate);
        } catch (Exception ex) {
            LOGGER.error("Count Rejected Loans by Date Range Failed: " + ex.getMessage());
            return 0;
        }
    }

    @Override
    public long count() {
        try {
            return rejectedLoanRepository.count();
        } catch (Exception ex) {
            LOGGER.error("Count All Rejected Loans Failed: " + ex.getMessage());
            return 0;
        }
    }
}
