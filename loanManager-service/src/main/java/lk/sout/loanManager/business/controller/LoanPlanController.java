package lk.sout.loanManager.business.controller;

import lk.sout.loanManager.business.entity.Loan;
import lk.sout.loanManager.business.entity.LoanPlan;
import lk.sout.loanManager.business.service.ActionLogService;
import lk.sout.loanManager.business.service.LoanPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/loanPlan")
public class LoanPlanController {

    @Autowired
    private LoanPlanService loanPlanService;

    @Autowired
    private ActionLogService actionLogService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody LoanPlan loanPlan, @RequestParam(defaultValue = "System") String performedBy) {
        try {
            boolean result = loanPlanService.save(loanPlan);

            if (result) {
                // Log the action
                String description = loanPlan.getId() != null ?
                    "Updated loan plan: " + loanPlan.getName() :
                    "Created new loan plan: " + loanPlan.getName();

                actionLogService.logLoanPlanChange(
                    loanPlan.getId() != null ? loanPlan.getId() : "new",
                    description,
                    performedBy
                );
            }

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public ResponseEntity<?> findAllLoanPlans() {
        return ResponseEntity.ok(loanPlanService.findAll());
    }

    @RequestMapping(value = "/findAllPage", method = RequestMethod.GET)
    public ResponseEntity<?> findAllLoanPlansPage(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        return ResponseEntity.ok(loanPlanService.findAllPage(PageRequest.of(Integer.parseInt(page),
                Integer.parseInt(pageSize))));
    }

    @RequestMapping(value = "/searchByName", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchByName(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize,
                                           @RequestParam String statusId) {
        try {
            return ResponseEntity.ok(loanPlanService.findAllByStatus(statusId, PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findById(@RequestParam String id) {
        try {
            return ResponseEntity.ok(loanPlanService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}
