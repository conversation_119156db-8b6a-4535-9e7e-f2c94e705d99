package lk.sout.loanManager.business.service;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.loanManager.business.entity.Loan;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface LoanService {

    Loan save(Loan loan);

    Loan update(Loan loan);

    Response create(Loan loan, String app);

    boolean approve(String loanNo);

    boolean settleLoan(String loanNo);

    boolean reject(String loanNo, String reason);

    Page<Loan> findAll(Pageable pageable);

    Page<Loan> findAllPending(Pageable pageable);

    Page<Loan> findAllArrears(Pageable pageable);

    Iterable<Loan> findAllByStatus(String status, Pageable pageable);

    List<Loan> findAllByStatus(String status);

    List<Loan> findAllByStatusNot(MetaData status);

    List<Loan> findAllByStatusNotIn(List<MetaData> status);

    List<Loan> findAllByStatusIn(List<MetaData> status);

    Loan findById(String id);

    Loan findByLoanNo(String loanNo);

    List<Loan> findActiveLoanByNic(String nic);

    Iterable<Loan> findByBorrowerId(String id);

    Iterable<Loan> findByBorrowerNic(String id);

}
