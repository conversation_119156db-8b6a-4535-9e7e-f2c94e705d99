package lk.sout.loanManager.business.repository;

import lk.sout.loanManager.business.entity.Settings;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SettingsRepository extends MongoRepository<Settings, String> {
    
    Optional<Settings> findBySettingKeyAndActive(String settingKey, boolean active);

    Optional<Settings> findBySettingKey(String settingKey);

    List<Settings> findByActive(boolean active);

    boolean existsBySettingKey(String settingKey);
}
