package lk.sout.loanManager.business.repository;

import lk.sout.loanManager.business.entity.LoanPlan;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LoanPlanRepository extends MongoRepository<LoanPlan, String> {
    List<LoanPlan> findAllByActive(String status, Pageable pageable);

}
