package lk.sout.loanManager.business.controller;

import lk.sout.loanManager.business.entity.ActionLog;
import lk.sout.loanManager.business.service.ActionLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/actionLog")
@CrossOrigin
public class ActionLogController {

    @Autowired
    private ActionLogService actionLogService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> save(@RequestBody ActionLog actionLog) {
        try {
            return ResponseEntity.ok(actionLogService.save(actionLog));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findAll(@RequestParam(defaultValue = "0") int page,
                                     @RequestParam(defaultValue = "20") int size) {
        try {
            Page<ActionLog> actionLogs = actionLogService.findAll(page, size);
            return ResponseEntity.ok(actionLogs);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByUser", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByUser(@RequestParam String username,
                                        @RequestParam(defaultValue = "0") int page,
                                        @RequestParam(defaultValue = "20") int size) {
        try {
            Page<ActionLog> actionLogs = actionLogService.findByUser(username, page, size);
            return ResponseEntity.ok(actionLogs);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByAction", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByAction(@RequestParam String action,
                                          @RequestParam(defaultValue = "0") int page,
                                          @RequestParam(defaultValue = "20") int size) {
        try {
            Page<ActionLog> actionLogs = actionLogService.findByAction(action, page, size);
            return ResponseEntity.ok(actionLogs);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByDateRange", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByDateRange(@RequestParam String fromDate,
                                             @RequestParam String toDate,
                                             @RequestParam(defaultValue = "0") int page,
                                             @RequestParam(defaultValue = "20") int size) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
            LocalDateTime startDate = LocalDateTime.parse(fromDate + "T00:00:00");
            LocalDateTime endDate = LocalDateTime.parse(toDate + "T23:59:59");
            
            Page<ActionLog> actionLogs = actionLogService.findByDateRange(startDate, endDate, page, size);
            return ResponseEntity.ok(actionLogs);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}
