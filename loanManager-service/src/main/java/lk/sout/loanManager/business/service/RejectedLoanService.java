package lk.sout.loanManager.business.service;

import lk.sout.loanManager.business.entity.RejectedLoan;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

public interface RejectedLoanService {

    RejectedLoan save(RejectedLoan rejectedLoan);

    RejectedLoan findById(String id);

    RejectedLoan findByLoanNo(String loanNo);

    List<RejectedLoan> findByCustomerNic(String customerNic);

    List<RejectedLoan> findByCustomerName(String customerName);

    List<RejectedLoan> findByTelephone(String telephone);

    List<RejectedLoan> findByRejectedBy(String rejectedBy);

    List<RejectedLoan> findByRejectedDate(LocalDate rejectedDate);

    List<RejectedLoan> findByDateRange(LocalDate startDate, LocalDate endDate);

    Page<RejectedLoan> findAll(Pageable pageable);

    List<RejectedLoan> findAll();

    boolean delete(String id);

    long countByDate(LocalDate date);

    long countByDateRange(LocalDate startDate, LocalDate endDate);

    long count();
}
