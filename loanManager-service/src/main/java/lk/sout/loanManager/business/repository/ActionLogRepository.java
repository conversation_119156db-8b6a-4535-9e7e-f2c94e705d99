package lk.sout.loanManager.business.repository;

import lk.sout.loanManager.business.entity.ActionLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ActionLogRepository extends MongoRepository<ActionLog, String> {

    Page<ActionLog> findAllByOrderByPerformedAtDesc(Pageable pageable);

    Page<ActionLog> findByPerformedByOrderByPerformedAtDesc(String performedBy, Pageable pageable);

    Page<ActionLog> findByActionOrderByPerformedAtDesc(String action, Pageable pageable);

    Page<ActionLog> findByPerformedAtBetweenOrderByPerformedAtDesc(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    @Query("{'performedAt': {$gte: ?0, $lte: ?1}, 'performedBy': ?2}")
    Page<ActionLog> findByPerformedAtBetweenAndPerformedByOrderByPerformedAtDesc(
            LocalDateTime startDate, LocalDateTime endDate, String performedBy, Pageable pageable);

    @Query("{'performedAt': {$gte: ?0, $lte: ?1}, 'action': ?2}")
    Page<ActionLog> findByPerformedAtBetweenAndActionOrderByPerformedAtDesc(
            LocalDateTime startDate, LocalDateTime endDate, String action, Pageable pageable);

    List<ActionLog> findTop10ByOrderByPerformedAtDesc();

    long countByAction(String action);

    long countByPerformedBy(String performedBy);

    @Query("{'performedAt': {$gte: ?0, $lte: ?1}}")
    long countByPerformedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
}
