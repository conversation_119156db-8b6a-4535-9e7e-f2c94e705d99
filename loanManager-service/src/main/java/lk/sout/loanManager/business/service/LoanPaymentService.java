package lk.sout.loanManager.business.service;


import lk.sout.loanManager.business.entity.Payment;

import java.time.LocalDate;
import java.util.List;

public interface LoanPaymentService {

    Iterable<Payment> findByBorrowerTp(String customerId);

    boolean save(Payment payment);

    Payment findById(String id);

    List<Payment> findTodayPaymentList();

    List<Payment> findPaymentByDate(LocalDate date);

    List<Payment> findPaymentByDateRange(LocalDate fromDate, LocalDate toDate);

}
