package lk.sout.loanManager.business.repository;

import lk.sout.core.entity.MetaData;
import lk.sout.loanManager.business.entity.Loan;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LoanRepository extends MongoRepository<Loan, String> {

    Page<Loan> findAllByStatus(String status, Pageable pageable);

    List<Loan> findAllByStatus(String status);

    List<Loan> findAllByStatusNot(MetaData status);

    List<Loan> findAllByStatusNotIn(List<MetaData> status);

    List<Loan> findAllByStatusIn(List<MetaData> status);

    Page<Loan> findAll(Pageable pageable);

    List<Loan> findAllByBorrower(String id);

    List<Loan> findAllByBorrowerNic(String nic);

    List<Loan> findByBorrowerNicAndStatusNot(String nic, MetaData status);

    Loan findAllByLoanNo(String loanNo);
}
