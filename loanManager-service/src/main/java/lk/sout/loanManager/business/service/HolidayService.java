package lk.sout.loanManager.business.service;

import lk.sout.loanManager.business.entity.Holiday;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

public interface HolidayService {

    Holiday save(Holiday holiday);
    
    Holiday update(Holiday holiday);
    
    boolean delete(String id);
    
    Holiday findById(String id);
    
    List<Holiday> findAll();
    
    Page<Holiday> findAll(Pageable pageable);
    
    List<Holiday> findByName(String name);
    
    List<Holiday> findActiveHolidays();
    
    List<Holiday> findByYear(int year);
    
    List<Holiday> findByDateRange(LocalDate startDate, LocalDate endDate);
    
    boolean isHoliday(LocalDate date);
    
    List<Holiday> getUpcomingHolidays(int days);
    
    Holiday toggleStatus(String id);
    
    List<Holiday> findHolidaysInMonth(int year, int month);
    
    boolean isWorkingDay(LocalDate date);
}
