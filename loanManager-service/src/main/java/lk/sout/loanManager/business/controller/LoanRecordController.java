package lk.sout.loanManager.business.controller;

import lk.sout.loanManager.borrower.entity.Borrower;
import lk.sout.loanManager.borrower.service.BorrowerService;
import lk.sout.loanManager.business.entity.Loan;
import lk.sout.loanManager.business.service.*;
import lk.sout.loanManager.mobileApp.service.MobileAppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@RestController
@RequestMapping("/loanRecord")
public class LoanRecordController {

    @Autowired
    LoanRecordService loanRecordService;

    @RequestMapping(value = "/findTodayList", method = RequestMethod.GET)
    public ResponseEntity<?> findTodayList() {
        return ResponseEntity.ok(loanRecordService.findTodayList());
    }


    @RequestMapping(value = "/findTodayLoanByName", method = RequestMethod.GET)
    public ResponseEntity<?> findTodayLoanByName(@RequestParam String name) {
        return ResponseEntity.ok(loanRecordService.findTodayLoanByName(name));
    }

    @RequestMapping(value = "/findArrearsList", method = RequestMethod.GET)
    public ResponseEntity<?> findArrearsList() {
        return ResponseEntity.ok(loanRecordService.findArrearsList());
    }

    @RequestMapping(value = "/findArrearsLoanByName", method = RequestMethod.GET)
    public ResponseEntity<?> findArrearsLoanByName(@RequestParam String name) {
        return ResponseEntity.ok(loanRecordService.findArrearsLoanByName(name));
    }

    @RequestMapping(value = "/findRecordsByLoanNo", method = RequestMethod.GET)
    public ResponseEntity<?> findRecordsByLoanNo(@RequestParam String loanNo) {
        return ResponseEntity.ok(loanRecordService.findAllRecordsByLoanNo(loanNo));
    }

    @RequestMapping(value = "/findPendingRecordsByLoanNo", method = RequestMethod.GET)
    public ResponseEntity<?> findPendingRecordsByLoanNo(@RequestParam String loanNo) {
        return ResponseEntity.ok(loanRecordService.findPendingRecordsByLoanNo(loanNo));
    }

    @RequestMapping(value = "/findPendingRecordsByNic", method = RequestMethod.GET)
    public ResponseEntity<?> findPendingRecordsByNic(@RequestParam String nic) {
        return ResponseEntity.ok(loanRecordService.findPendingRecordsByNic(nic));
    }

    @RequestMapping(value = "/findPendingRecordsByTp1", method = RequestMethod.GET)
    public ResponseEntity<?> findPendingRecordsByTp1(@RequestParam String tp1) {
        return ResponseEntity.ok(loanRecordService.findPendingRecordsByTp1(tp1));
    }

    @RequestMapping(value = "/payLoanRecord", method = RequestMethod.GET)
    private ResponseEntity<?> payLoanRecord(@RequestParam String loanRecordId, @RequestParam double payment,
                                            @RequestParam String appNo) {
        try {
            return ResponseEntity.ok(loanRecordService.pay(loanRecordId, payment,appNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/createAndPayLoanRecord", method = RequestMethod.GET)
    private ResponseEntity<?> createAndPayLoanRecord(@RequestParam String loanNo, @RequestParam double payment,
                                                     @RequestParam String appNo) {
        try {
            return ResponseEntity.ok(loanRecordService.createAndPayLoanRecord(loanNo, payment, appNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/removeUnpaidRecordsForDate", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> removeUnpaidRecordsForDate(@RequestParam String date) {
        try {
            LocalDate localDate = LocalDate.parse(date);
            return ResponseEntity.ok(loanRecordService.removeUnpaidRecordsForDate(localDate));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}
