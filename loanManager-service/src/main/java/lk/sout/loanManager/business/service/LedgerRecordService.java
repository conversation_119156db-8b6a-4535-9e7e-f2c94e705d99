package lk.sout.loanManager.business.service;

import lk.sout.loanManager.business.entity.LedgerRecord;

import java.time.LocalDate;
import java.util.List;

public interface LedgerRecordService {

    boolean save(LedgerRecord ledgerRecord);

    boolean create(String appNo, double amount, String reason, String loanNo, String borrowerName, String operation);

    List<LedgerRecord> findByDateAndAppNo(LocalDate fromDate, LocalDate toDate, String appNo);

    List<LedgerRecord> findByDateAndAppNoAndReason(LocalDate fromDate, LocalDate toDate, String appNo, String reason);

}
