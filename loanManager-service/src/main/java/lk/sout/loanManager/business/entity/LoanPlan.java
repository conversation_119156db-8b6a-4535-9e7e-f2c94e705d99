package lk.sout.loanManager.business.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.time.temporal.TemporalAmount;
import java.util.List;

@Document
@Component
public class LoanPlan {

    @Id
    private String id;

    private String name;

    private double maxAmount;

    private double minAmount;

    private int durationInDays;

    private int paymentFrequencyInDays;

    private int totalNoOfInstallments;

    private double interestRate;

    private double dailyAmount;

    private double arrearsInterestRate;

    private int arrearsInterestDuration;

    private List<LoanRecord> loanRecords;

    private boolean active;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public double getMaxAmount() {
        return maxAmount;
    }

    public void setMaxAmount(double maxAmount) {
        this.maxAmount = maxAmount;
    }

    public double getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(double minAmount) {
        this.minAmount = minAmount;
    }

    public int getDurationInDays() {
        return durationInDays;
    }

    public void setDurationInDays(int durationInDays) {
        this.durationInDays = durationInDays;
    }

    public double getInterestRate() {
        return interestRate;
    }

    public void setInterestRate(double interestRate) {
        this.interestRate = interestRate;
    }

    public double getDailyAmount() {
        return dailyAmount;
    }

    public void setDailyAmount(double dailyAmount) {
        this.dailyAmount = dailyAmount;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public double getArrearsInterestRate() {
        return arrearsInterestRate;
    }

    public void setArrearsInterestRate(double arrearsInterestRate) {
        this.arrearsInterestRate = arrearsInterestRate;
    }

    public int getArrearsInterestDuration() {
        return arrearsInterestDuration;
    }

    public void setArrearsInterestDuration(int arrearsInterestDuration) {
        this.arrearsInterestDuration = arrearsInterestDuration;
    }

    public int getTotalNoOfInstallments() {
        return totalNoOfInstallments;
    }

    public void setTotalNoOfInstallments(int totalNoOfInstallments) {
        this.totalNoOfInstallments = totalNoOfInstallments;
    }

    public List<LoanRecord> getLoanRecords() {
        return loanRecords;
    }

    public void setLoanRecords(List<LoanRecord> loanRecords) {
        this.loanRecords = loanRecords;
    }

    public int getPaymentFrequencyInDays() {
        return paymentFrequencyInDays;
    }

    public void setPaymentFrequencyInDays(int paymentFrequencyInDays) {
        this.paymentFrequencyInDays = paymentFrequencyInDays;
    }
}
