package lk.sout.loanManager.business.repository;

import lk.sout.loanManager.business.entity.Route;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 4/18/2018
 */
@Repository
public interface RouteRepository extends MongoRepository<Route, String> {

    List<Route> findByNameLikeIgnoreCaseAndActive(String key, Boolean active);

    @Query(fields = "{'name': 1}")
    List<Route> findAllByActive(Boolean active);

    List<Route> findAllByActiveAndStartLikeIgnoreCase(Boolean active, String startingLocation);

    List<Route> findAllByActiveAndEndLikeIgnoreCase(Boolean active, String endingLocation);

}
