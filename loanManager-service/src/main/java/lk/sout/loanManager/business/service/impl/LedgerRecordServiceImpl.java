package lk.sout.loanManager.business.service.impl;

import lk.sout.loanManager.business.entity.LedgerRecord;
import lk.sout.loanManager.business.repository.LedgerRecordRepository;
import lk.sout.loanManager.business.service.LedgerRecordService;
import lk.sout.loanManager.business.service.LedgerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class LedgerRecordServiceImpl implements LedgerRecordService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LedgerRecordServiceImpl.class);

    @Autowired
    LedgerRecordRepository ledgerRecordRepository;

    @Autowired
    LedgerRecord record;

    @Autowired
    LedgerService ledgerService;

    @Override
    public boolean save(LedgerRecord ledgerRecord) {
        try {
            double amount = ledgerRecord.getAmount();
            String appNo = ledgerRecord.getAppNo();
            ledgerRecord.setId(null);
            ledgerRecord.setDateTime(LocalDateTime.now());

            if (ledgerRecord.getOperation().equals("+")) {
                ledgerRecord.setLedgerValue(ledgerService.findByAppNo(appNo).getCurrentBalance() + amount);
            } else if (ledgerRecord.getOperation().equals("-")) {
                ledgerRecord.setLedgerValue(ledgerService.findByAppNo(appNo).getCurrentBalance() - amount);
            } else {
                ledgerRecord.setLedgerValue(ledgerService.findByAppNo(appNo).getCurrentBalance());
            }
            ledgerRecordRepository.save(ledgerRecord);
            ledgerService.changeBalance(appNo, amount, ledgerRecord.getOperation());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Creating Ledger Record Failed " + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean create(String appNo, double amount, String reason, String loanNo, String borrowerName,
                          String operation) {
        try {
            record.setId(null);
            record.setAppNo(appNo);
            record.setAmount(amount);
            record.setDateTime(LocalDateTime.now());
            record.setReason(reason);
            if (operation.equals("+")) {
                record.setLedgerValue(ledgerService.findByAppNo(appNo).getCurrentBalance() + amount);
            } else if (operation.equals("-")) {
                record.setLedgerValue(ledgerService.findByAppNo(appNo).getCurrentBalance() - amount);
            } else {
                record.setLedgerValue(ledgerService.findByAppNo(appNo).getCurrentBalance());
            }
            record.setLoanNo(loanNo);
            record.setOperation(operation);
            record.setBorrowerName(borrowerName);
            ledgerRecordRepository.save(record);

            ledgerService.changeBalance(appNo, amount, operation);

            return true;
        } catch (Exception ex) {
            return false;
        }
    }

    @Override
    public List<LedgerRecord> findByDateAndAppNo(LocalDate fromDate, LocalDate toDate, String appNo) {
        try {
            return ledgerRecordRepository.findByDateTimeBetweenAndAppNo(fromDate.atStartOfDay(),
                    toDate.plusDays(1).atStartOfDay(), appNo);
        } catch (Exception ex) {
            LOGGER.error("Find All Page Loan Plans Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LedgerRecord> findByDateAndAppNoAndReason(LocalDate fromDate, LocalDate toDate, String appNo, String reason) {
        try {
            return ledgerRecordRepository.findByDateTimeBetweenAndAppNoAndReason(fromDate.atStartOfDay(),
                    toDate.plusDays(1).atStartOfDay(), appNo, reason);
        } catch (Exception ex) {
            LOGGER.error("Find All Page Loan Plans Failed " + ex.getMessage());
            return null;
        }
    }

}
