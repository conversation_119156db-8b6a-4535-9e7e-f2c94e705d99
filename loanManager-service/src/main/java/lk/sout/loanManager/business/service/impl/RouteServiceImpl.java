package lk.sout.loanManager.business.service.impl;

import lk.sout.loanManager.business.entity.Route;
import lk.sout.loanManager.business.repository.RouteRepository;
import lk.sout.loanManager.business.service.RouteService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class RouteServiceImpl implements RouteService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RouteService.class);

    @Autowired
    RouteRepository routeRepository;

    public boolean save(Route route) {
        try {
            routeRepository.save(route);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving failed." + route.getName());
            return false;
        }
    }

    public boolean remove(Route route) {
        try {
            routeRepository.delete(route);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Removing Failed." + route.getName());
            return false;
        }
    }

    public Iterable<Route> findAll(Pageable pageable) {
        try {
            return routeRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Failed");
            return null;
        }
    }

    @Override
    public Iterable<Route> findAllActiveForSelect() {
        try {
            Boolean active = true;
            return routeRepository.findAllByActive(active);
        } catch (Exception ex) {
            LOGGER.error("Retrieving All Route Failed");
            return null;
        }
    }

    public Route findOne(String id) {
        try {
            Optional<Route> route = routeRepository.findById(id);
            return route.get();
        } catch (Exception ex) {
            LOGGER.error("Find One Failed");
            return null;
        }
    }

    @Override
    public List<Route> findByName(String key) {
        try {
            return routeRepository.findByNameLikeIgnoreCaseAndActive(key,true);
        } catch (Exception ex) {
            LOGGER.error("Find by Name Failed");
            return null;
        }
    }

    @Override
    public String delete(String id) {
        try{
            routeRepository.deleteById(id);
            return "සාර්ථකයි";
        }catch (Exception ex){
            LOGGER.error("Delete Route Failed" + id + ". " + ex.getMessage());
            return "අසාර්ථකයි";
        }
    }


}
