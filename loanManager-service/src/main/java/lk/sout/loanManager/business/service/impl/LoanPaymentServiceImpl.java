package lk.sout.loanManager.business.service.impl;

import lk.sout.loanManager.business.entity.LoanPlan;
import lk.sout.loanManager.business.entity.Payment;
import lk.sout.loanManager.business.repository.PaymentRepository;
import lk.sout.loanManager.business.service.LoanPaymentService;
import lk.sout.loanManager.business.service.RouteService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class LoanPaymentServiceImpl implements LoanPaymentService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoanPaymentService.class);

    @Autowired
    PaymentRepository paymentRepository;

    @Override
    public Iterable<Payment> findByBorrowerTp(String tp) {
        return null;
    }

    @Override
    public boolean save(Payment payment) {
        try {
            paymentRepository.save(payment);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving failed." + ex.getMessage());
            return false;
        }
    }

    @Override
    public Payment findById(String id) {
        try {
            return paymentRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.error("Searching failed." + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Payment> findTodayPaymentList() {
        try {
            return paymentRepository.findByDateTimeBetween(LocalDate.now().atStartOfDay(),
                    LocalDate.now().plusDays(1).atStartOfDay());
        } catch (Exception ex) {
            LOGGER.error("Find All Loan Plans Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Payment> findPaymentByDate(LocalDate date) {
        try {
            return paymentRepository.findByDateTimeBetween(date.atStartOfDay(),
                    date.plusDays(1).atStartOfDay());
        } catch (Exception ex) {
            LOGGER.error("Find All Loan Plans Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Payment> findPaymentByDateRange(LocalDate fromDate, LocalDate toDate) {
        try {
            return paymentRepository.findByDateTimeBetween(fromDate.atStartOfDay(),
                    toDate.plusDays(1).atStartOfDay());
        } catch (Exception ex) {
            LOGGER.error("Find All Loan Plans Failed " + ex.getMessage());
            return null;
        }
    }
}
