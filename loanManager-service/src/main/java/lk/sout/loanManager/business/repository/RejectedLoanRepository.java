package lk.sout.loanManager.business.repository;

import lk.sout.loanManager.business.entity.RejectedLoan;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface RejectedLoanRepository extends MongoRepository<RejectedLoan, String> {

    List<RejectedLoan> findByCustomerNic(String customerNic);

    List<RejectedLoan> findByCustomerNameContainingIgnoreCase(String customerName);

    List<RejectedLoan> findByTelephoneContaining(String telephone);

    RejectedLoan findByLoanNo(String loanNo);

    List<RejectedLoan> findByRejectedBy(String rejectedBy);

    List<RejectedLoan> findByRejectedDate(LocalDate rejectedDate);

    List<RejectedLoan> findByRejectedDateBetween(LocalDate startDate, LocalDate endDate);

    Page<RejectedLoan> findAll(Pageable pageable);

    long countByRejectedDate(LocalDate rejectedDate);

    long countByRejectedDateBetween(LocalDate startDate, LocalDate endDate);
}
