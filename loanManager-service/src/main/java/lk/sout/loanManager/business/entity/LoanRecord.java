package lk.sout.loanManager.business.entity;

import lk.sout.core.entity.MetaData;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Component
@Document
public class LoanRecord {

    @Id
    private String id;

    private String loanNo;

    private String loanPlan;

    private String borrowerName;

    private String borrowerAddress;

    private String borrowerNic;

    private String borrowerTp1;

    private String borrowerTp2;

    private LocalDate installmentDate;

    private LocalDate dueDate;

    //date payment received
    private LocalDate paidDate;

    private String type;

    private double installmentWithoutInterest;

    private double installmentAmount;

    private double arrearsAmount;

    private double paidAmount;

    private double paidAmountByOverPaid;

    private String routeName;

    private double balance;

    //how many days passed without a payment
    private int daysDue;

    @DBRef
    private MetaData status;

    //have to keep this due to inability of run where query on status
    private String statusCode;

    @CreatedDate
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime createdDate;

    @LastModifiedDate
    private Date lastModifiedDate;

    @LastModifiedBy
    private String lastModifiedBy;

    public LocalDate getInstallmentDate() {
        return installmentDate;
    }

    public void setInstallmentDate(LocalDate installmentDate) {
        this.installmentDate = installmentDate;
    }

    public double getInstallmentAmount() {
        return installmentAmount;
    }

    public void setInstallmentAmount(double installmentAmount) {
        this.installmentAmount = installmentAmount;
    }

    public double getArrearsAmount() {
        return arrearsAmount;
    }

    public void setArrearsAmount(double arrearsAmount) {
        this.arrearsAmount = arrearsAmount;
    }

    public double getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(double paidAmount) {
        this.paidAmount = paidAmount;
    }

    public double getBalance() {
        return balance;
    }

    public void setBalance(double balance) {
        this.balance = balance;
    }

    public int getDaysDue() {
        return daysDue;
    }

    public double getInstallmentWithoutInterest() {
        return installmentWithoutInterest;
    }

    public void setInstallmentWithoutInterest(double installmentWithoutInterest) {
        this.installmentWithoutInterest = installmentWithoutInterest;
    }

    public void setDaysDue(int daysDue) {
        this.daysDue = daysDue;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getLoanPlan() {
        return loanPlan;
    }

    public void setLoanPlan(String loanPlan) {
        this.loanPlan = loanPlan;
    }

    public String getBorrowerName() {
        return borrowerName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setBorrowerName(String borrowerName) {
        this.borrowerName = borrowerName;
    }

    public String getBorrowerNic() {
        return borrowerNic;
    }

    public String getBorrowerAddress() {
        return borrowerAddress;
    }

    public void setBorrowerAddress(String borrowerAddress) {
        this.borrowerAddress = borrowerAddress;
    }

    public void setBorrowerNic(String borrowerNic) {
        this.borrowerNic = borrowerNic;
    }

    public String getBorrowerTp1() {
        return borrowerTp1;
    }

    public void setBorrowerTp1(String borrowerTp1) {
        this.borrowerTp1 = borrowerTp1;
    }

    public LocalDate getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDate dueDate) {
        this.dueDate = dueDate;
    }

    public LocalDate getPaidDate() {
        return paidDate;
    }

    public void setPaidDate(LocalDate paidDate) {
        this.paidDate = paidDate;
    }

    public String getBorrowerTp2() {
        return borrowerTp2;
    }

    public void setBorrowerTp2(String borrowerTp2) {
        this.borrowerTp2 = borrowerTp2;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public MetaData getStatus() {
        return status;
    }

    public void setStatus(MetaData status) {
        this.status = status;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public double getPaidAmountByOverPaid() {
        return paidAmountByOverPaid;
    }

    public void setPaidAmountByOverPaid(double paidAmountByOverPaid) {
        this.paidAmountByOverPaid = paidAmountByOverPaid;
    }
}
