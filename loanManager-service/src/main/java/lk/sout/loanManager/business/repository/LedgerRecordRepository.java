package lk.sout.loanManager.business.repository;

import lk.sout.loanManager.business.entity.LedgerRecord;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface LedgerRecordRepository extends MongoRepository<LedgerRecord, String> {

    List<LedgerRecord> findByDateTimeBetweenAndAppNo(LocalDateTime start, LocalDateTime end, String appNo);

    List<LedgerRecord> findByDateTimeBetweenAndAppNoAndReason(LocalDateTime start, LocalDateTime end, String appNo, String reason);
    //List<LedgerRecord> findByDateTimeBetweenAndAppNoAndReasonOrderByReasonAsc(LocalDateTime start, LocalDateTime end, String appNo, String reason);
}
