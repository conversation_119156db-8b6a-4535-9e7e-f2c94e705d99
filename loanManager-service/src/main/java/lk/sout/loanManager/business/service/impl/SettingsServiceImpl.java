package lk.sout.loanManager.business.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.loanManager.business.entity.Holiday;
import lk.sout.loanManager.business.entity.Settings;
import lk.sout.loanManager.business.repository.SettingsRepository;
import lk.sout.loanManager.business.service.HolidayService;
import lk.sout.loanManager.business.service.SettingsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
public class SettingsServiceImpl implements SettingsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SettingsServiceImpl.class);

    @Autowired
    private SettingsRepository settingsRepository;

    @Autowired
    private HolidayService holidayService;

    private Response response = new Response();

    @Override
    public Settings save(Settings settings) {
        try {
            return settingsRepository.save(settings);
        } catch (Exception ex) {
            LOGGER.error("Save Settings Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Settings> findAll() {
        try {
            return settingsRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("Find All Settings Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Settings> findAllActive() {
        try {
            return settingsRepository.findByActive(true);
        } catch (Exception ex) {
            LOGGER.error("Find All Active Settings Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Settings findById(String id) {
        try {
            Optional<Settings> settings = settingsRepository.findById(id);
            return settings.orElse(null);
        } catch (Exception ex) {
            LOGGER.error("Find Settings by ID Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Settings findBySettingKey(String settingKey) {
        try {
            Optional<Settings> settings = settingsRepository.findBySettingKeyAndActive(settingKey, true);
            return settings.orElse(null);
        } catch (Exception ex) {
            LOGGER.error("Find Settings by Key Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public String getSettingValue(String settingKey) {
        Settings setting = findBySettingKey(settingKey);
        return setting != null ? setting.getSettingValue() : null;
    }

    @Override
    public boolean getBooleanSetting(String settingKey, boolean defaultValue) {
        String value = getSettingValue(settingKey);
        if (value != null) {
            return Boolean.parseBoolean(value);
        }
        return defaultValue;
    }

    @Override
    public int getIntegerSetting(String settingKey, int defaultValue) {
        String value = getSettingValue(settingKey);
        if (value != null) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException ex) {
                LOGGER.error("Invalid integer setting value for key: " + settingKey);
            }
        }
        return defaultValue;
    }

    @Override
    public double getDoubleSetting(String settingKey, double defaultValue) {
        String value = getSettingValue(settingKey);
        if (value != null) {
            try {
                return Double.parseDouble(value);
            } catch (NumberFormatException ex) {
                LOGGER.error("Invalid double setting value for key: " + settingKey);
            }
        }
        return defaultValue;
    }

    @Override
    public Response updateSetting(String settingKey, String settingValue) {
        try {
            Settings setting = findBySettingKey(settingKey);
            if (setting != null) {
                setting.setSettingValue(settingValue);
                settingsRepository.save(setting);
                response.setCode(200);
                response.setMessage("Setting updated successfully");
            } else {
                response.setCode(404);
                response.setMessage("Setting not found");
            }
        } catch (Exception ex) {
            LOGGER.error("Update Setting Failed: " + ex.getMessage());
            response.setCode(500);
            response.setMessage("Failed to update setting");
        }
        return response;
    }

    @Override
    public Response createDefaultSettings() {
        try {
            // Create default setting for due date calculation
            if (!settingsRepository.existsBySettingKey("SKIP_HOLIDAYS_FOR_DUE_DATE")) {
                Settings dueDateSetting = new Settings();
                dueDateSetting.setSettingKey("SKIP_HOLIDAYS_FOR_DUE_DATE");
                dueDateSetting.setSettingValue("false");
                dueDateSetting.setDescription("Skip Holidays for Loan Due Dates");
                dueDateSetting.setDataType("BOOLEAN");
                dueDateSetting.setActive(true);
                settingsRepository.save(dueDateSetting);
            }

            response.setCode(200);
            response.setMessage("Default settings created successfully");
        } catch (Exception ex) {
            LOGGER.error("Create Default Settings Failed: " + ex.getMessage());
            response.setCode(500);
            response.setMessage("Failed to create default settings");
        }
        return response;
    }

    @Override
    public LocalDate calculateDueDate(LocalDate baseDate, int paymentFrequencyInDays) {
        LocalDate dueDate = baseDate.plusDays(paymentFrequencyInDays);

        // Check if we should skip holidays for due dates
        boolean skipHolidays = getBooleanSetting("SKIP_HOLIDAYS_FOR_DUE_DATE", false);

        if (skipHolidays) {
            dueDate = getNextNonHolidayDate(dueDate);
        }

        return dueDate;
    }

    private LocalDate getNextNonHolidayDate(LocalDate date) {
        LocalDate nonHolidayDate = date;

        // Skip only holidays, not weekends
        while (isHoliday(nonHolidayDate)) {
            nonHolidayDate = nonHolidayDate.plusDays(1);
        }

        return nonHolidayDate;
    }

    private boolean isHoliday(LocalDate date) {
        try {
            List<Holiday> holidays = holidayService.findAll();
            return holidays.stream().anyMatch(holiday -> holiday.getDate().equals(date));
        } catch (Exception ex) {
            LOGGER.error("Error checking holiday: " + ex.getMessage());
            return false;
        }
    }
}
