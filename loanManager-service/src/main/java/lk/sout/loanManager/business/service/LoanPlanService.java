package lk.sout.loanManager.business.service;

import lk.sout.loanManager.business.entity.Loan;
import lk.sout.loanManager.business.entity.LoanPlan;
import org.springframework.data.domain.Pageable;

public interface LoanPlanService {

    boolean save(LoanPlan loanPlan);

    Iterable<LoanPlan> findAll();

    Iterable<LoanPlan> findAllPage(Pageable pageable);

    Iterable<LoanPlan> findAllByStatus(String status, Pageable pageable);

    LoanPlan findById(String id);
}
