package lk.sout.loanManager.business.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.loanManager.business.entity.Ledger;
import lk.sout.loanManager.business.entity.LoanPlan;
import lk.sout.loanManager.business.repository.LedgerRepository;
import lk.sout.loanManager.business.repository.LoanPlanRepository;
import lk.sout.loanManager.business.service.LedgerRecordService;
import lk.sout.loanManager.business.service.LedgerService;
import lk.sout.loanManager.business.service.LoanPlanService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
public class LedgerServiceImpl implements LedgerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LedgerServiceImpl.class);

    @Autowired
    LedgerRepository ledgerRepository;

    @Autowired
    LedgerRecordService ledgerRecordService;

    @Override
    public boolean save(Ledger ledger) {
        try {
            ledgerRepository.save(ledger);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Creating Ledger Failed " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Ledger findByAppNo(String appNo) {
        try {
            return ledgerRepository.findByAppNo(appNo);
        } catch (Exception ex) {
            LOGGER.error("Find All Page Loan Plans Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean correctBalance(String appNo, double amount) {
        try {
            Ledger ledger = ledgerRepository.findByAppNo(appNo);
            ledger.setCurrentBalance(amount);
            ledgerRepository.save(ledger);
            ledgerRecordService.create(appNo, amount, "Ledger Correction", "N/A",
                    "N/A", "N/A");
            return true;
        } catch (Exception ex) {
            LOGGER.error("Correcting Ledger Failed " + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean changeBalance(String appNo, double amount, String operation) {
        try {
            Ledger ledger = ledgerRepository.findByAppNo(appNo);
            if (operation.equals("+")) {
                ledger.setCurrentBalance(ledger.getCurrentBalance() + amount);
            }
            if (operation.equals("-")) {
                ledger.setCurrentBalance(ledger.getCurrentBalance() - amount);
            }
            ledgerRepository.save(ledger);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Updating Ledger Failed " + ex.getMessage());
            return false;
        }
    }

}
