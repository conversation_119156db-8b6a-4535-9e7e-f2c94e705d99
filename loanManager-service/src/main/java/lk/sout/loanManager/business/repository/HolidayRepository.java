package lk.sout.loanManager.business.repository;

import lk.sout.loanManager.business.entity.Holiday;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface HolidayRepository extends MongoRepository<Holiday, String> {

    List<Holiday> findByNameContainingIgnoreCase(String name);
    
    List<Holiday> findByActive(boolean active);
    
    List<Holiday> findByDate(LocalDate date);
    
    List<Holiday> findByDateBetween(LocalDate startDate, LocalDate endDate);
    
    @Query("{ 'date': { $gte: ?0, $lte: ?1 } }")
    List<Holiday> findByDateRange(LocalDate startDate, LocalDate endDate);
    
    @Query("{ $expr: { $eq: [{ $year: '$date' }, ?0] } }")
    List<Holiday> findByYear(int year);
    
    @Query("{ $expr: { $and: [{ $eq: [{ $year: '$date' }, ?0] }, { $eq: [{ $month: '$date' }, ?1] }] } }")
    List<Holiday> findByYearAndMonth(int year, int month);
    
    boolean existsByDate(LocalDate date);
    
    @Query("{ 'active': true, 'date': ?0 }")
    List<Holiday> findActiveHolidaysByDate(LocalDate date);
    
    @Query("{ 'active': true, 'date': { $gte: ?0, $lte: ?1 } }")
    List<Holiday> findActiveHolidaysByDateRange(LocalDate startDate, LocalDate endDate);
    
    Page<Holiday> findByActiveOrderByDateDesc(boolean active, Pageable pageable);
    
    Page<Holiday> findAllByOrderByDateDesc(Pageable pageable);
}
