package lk.sout.loanManager.business.controller;

import lk.sout.loanManager.business.service.LoanRecordService;
import lk.sout.loanManager.business.service.LoanPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/payment")
public class LoanPaymentController {

    @Autowired
    LoanPaymentService loanPaymentService;

    @Autowired
    LoanRecordService loanRecordService;

    @RequestMapping(value = "/findTodayPaymentList", method = RequestMethod.GET)
    public ResponseEntity<?> findTodayPaymentList() {
        return ResponseEntity.ok(loanPaymentService.findTodayPaymentList());
    }

    @RequestMapping(value = "/findPaymentsByDate", method = RequestMethod.GET)
    public ResponseEntity<?> findPaymentsByDate(@RequestParam("date") String date) {
        return ResponseEntity.ok(loanPaymentService.findPaymentByDate(LocalDate.parse(date)));
    }

    @RequestMapping(value = "/findPaymentsByDateRange", method = RequestMethod.GET)
    public ResponseEntity<?> findPaymentsByDateRange(@RequestParam("fromDate") String fromDate,
                                                     @RequestParam("toDate") String toDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
        return ResponseEntity.ok(loanPaymentService.findPaymentByDateRange(LocalDate.parse(fromDate, formatter),
                LocalDate.parse(toDate, formatter)));
    }

}
