package lk.sout.loanManager.business.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lk.sout.core.entity.Response;
import lk.sout.loanManager.business.entity.ActionLog;
import lk.sout.loanManager.business.repository.ActionLogRepository;
import lk.sout.loanManager.business.service.ActionLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Service
public class ActionLogServiceImpl implements ActionLogService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ActionLogServiceImpl.class);

    @Autowired
    private ActionLogRepository actionLogRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private Response response = new Response();

    @Override
    public Response save(ActionLog actionLog) {
        try {
            if (actionLog.getPerformedAt() == null) {
                actionLog.setPerformedAt(LocalDateTime.now());
            }
            
            ActionLog savedLog = actionLogRepository.save(actionLog);
            LOGGER.info("Action logged: {} by {}", actionLog.getAction(), actionLog.getPerformedBy());
            
            response.setCode(200);
            response.setMessage("Action logged successfully");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Failed to save action log: " + ex.getMessage());
            response.setCode(500);
            response.setMessage("Failed to save action log");
            response.setData(ex.getMessage());
            return response;
        }
    }

    @Override
    public Page<ActionLog> findAll(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return actionLogRepository.findAllByOrderByPerformedAtDesc(pageable);
    }

    @Override
    public Page<ActionLog> findByUser(String username, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return actionLogRepository.findByPerformedByOrderByPerformedAtDesc(username, pageable);
    }

    @Override
    public Page<ActionLog> findByAction(String action, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return actionLogRepository.findByActionOrderByPerformedAtDesc(action, pageable);
    }

    @Override
    public Page<ActionLog> findByDateRange(LocalDateTime startDate, LocalDateTime endDate, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return actionLogRepository.findByPerformedAtBetweenOrderByPerformedAtDesc(startDate, endDate, pageable);
    }

    @Override
    public Page<ActionLog> findByDateRangeAndUser(LocalDateTime startDate, LocalDateTime endDate, String username, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return actionLogRepository.findByPerformedAtBetweenAndPerformedByOrderByPerformedAtDesc(startDate, endDate, username, pageable);
    }

    @Override
    public Page<ActionLog> findByDateRangeAndAction(LocalDateTime startDate, LocalDateTime endDate, String action, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return actionLogRepository.findByPerformedAtBetweenAndActionOrderByPerformedAtDesc(startDate, endDate, action, pageable);
    }

    @Override
    public void logAction(String action, String description, String performedBy, Object details) {
        try {
            ActionLog actionLog = new ActionLog(action, description, performedBy);
            
            if (details != null) {
                Map<String, Object> detailsMap = objectMapper.convertValue(details, Map.class);
                actionLog.setDetails(detailsMap);
            }
            
            actionLogRepository.save(actionLog);
        } catch (Exception ex) {
            LOGGER.error("Failed to log action: " + ex.getMessage());
        }
    }

    @Override
    public void logLoanPlanChange(String loanPlanId, String changes, String performedBy) {
        Map<String, Object> details = new HashMap<>();
        details.put("loanPlanId", loanPlanId);
        details.put("changes", changes);
        
        logAction("LOAN_PLAN_CHANGE", "Loan plan modified: " + changes, performedBy, details);
    }

    @Override
    public void logLoanEdit(String loanId, String changes, String performedBy) {
        Map<String, Object> details = new HashMap<>();
        details.put("loanId", loanId);
        details.put("changes", changes);
        
        logAction("LOAN_EDIT", "Loan manually edited: " + changes, performedBy, details);
    }

    @Override
    public void logUserAction(String action, String description, String performedBy) {
        logAction(action, description, performedBy, null);
    }
}
