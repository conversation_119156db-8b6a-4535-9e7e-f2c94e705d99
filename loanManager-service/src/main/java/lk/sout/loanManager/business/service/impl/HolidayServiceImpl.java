package lk.sout.loanManager.business.service.impl;

import lk.sout.loanManager.business.entity.Holiday;
import lk.sout.loanManager.business.repository.HolidayRepository;
import lk.sout.loanManager.business.service.HolidayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class HolidayServiceImpl implements HolidayService {

    private static final Logger LOGGER = LoggerFactory.getLogger(HolidayServiceImpl.class);

    @Autowired
    private HolidayRepository holidayRepository;

    @Override
    public Holiday save(Holiday holiday) {
        try {
            holiday.setCreatedDate(LocalDateTime.now());
            holiday.setModifiedDate(LocalDateTime.now());
            return holidayRepository.save(holiday);
        } catch (Exception ex) {
            LOGGER.error("Save Holiday failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Holiday update(Holiday holiday) {
        try {
            holiday.setModifiedDate(LocalDateTime.now());
            return holidayRepository.save(holiday);
        } catch (Exception ex) {
            LOGGER.error("Update Holiday failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean delete(String id) {
        try {
            holidayRepository.deleteById(id);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Delete Holiday failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Holiday findById(String id) {
        try {
            Optional<Holiday> holiday = holidayRepository.findById(id);
            return holiday.orElse(null);
        } catch (Exception ex) {
            LOGGER.error("Find Holiday by ID failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Holiday> findAll() {
        try {
            return holidayRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("Find All Holidays failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Page<Holiday> findAll(Pageable pageable) {
        try {
            return holidayRepository.findAllByOrderByDateDesc(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Holidays with pagination failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Holiday> findByName(String name) {
        try {
            return holidayRepository.findByNameContainingIgnoreCase(name);
        } catch (Exception ex) {
            LOGGER.error("Find Holidays by name failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Holiday> findActiveHolidays() {
        try {
            return holidayRepository.findByActive(true);
        } catch (Exception ex) {
            LOGGER.error("Find Active Holidays failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Holiday> findByYear(int year) {
        try {
            return holidayRepository.findByYear(year);
        } catch (Exception ex) {
            LOGGER.error("Find Holidays by year failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Holiday> findByDateRange(LocalDate startDate, LocalDate endDate) {
        try {
            return holidayRepository.findByDateRange(startDate, endDate);
        } catch (Exception ex) {
            LOGGER.error("Find Holidays by date range failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean isHoliday(LocalDate date) {
        try {
            List<Holiday> holidays = holidayRepository.findActiveHolidaysByDate(date);
            return !holidays.isEmpty();
        } catch (Exception ex) {
            LOGGER.error("Check if date is holiday failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public List<Holiday> getUpcomingHolidays(int days) {
        try {
            LocalDate today = LocalDate.now();
            LocalDate futureDate = today.plusDays(days);
            return holidayRepository.findActiveHolidaysByDateRange(today, futureDate);
        } catch (Exception ex) {
            LOGGER.error("Get upcoming holidays failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Holiday toggleStatus(String id) {
        try {
            Optional<Holiday> holidayOpt = holidayRepository.findById(id);
            if (holidayOpt.isPresent()) {
                Holiday holiday = holidayOpt.get();
                holiday.setActive(!holiday.isActive());
                holiday.setModifiedDate(LocalDateTime.now());
                return holidayRepository.save(holiday);
            }
            return null;
        } catch (Exception ex) {
            LOGGER.error("Toggle Holiday status failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Holiday> findHolidaysInMonth(int year, int month) {
        try {
            return holidayRepository.findByYearAndMonth(year, month);
        } catch (Exception ex) {
            LOGGER.error("Find Holidays in month failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean isWorkingDay(LocalDate date) {
        try {
            // Check if it's a weekend (Saturday or Sunday)
            DayOfWeek dayOfWeek = date.getDayOfWeek();
            if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) {
                return false;
            }
            
            // Check if it's a holiday
            return !isHoliday(date);
        } catch (Exception ex) {
            LOGGER.error("Check if working day failed: " + ex.getMessage());
            return true; // Default to working day if check fails
        }
    }
}
