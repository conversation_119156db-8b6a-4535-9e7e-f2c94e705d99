package lk.sout.loanManager.business.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.loanManager.business.entity.Loan;
import lk.sout.loanManager.business.entity.LoanPlan;
import lk.sout.loanManager.business.repository.LoanPlanRepository;
import lk.sout.loanManager.business.service.ActionLogService;
import lk.sout.loanManager.business.service.LoanPlanService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
public class LoanPlanServiceImpl implements LoanPlanService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoanPlanServiceImpl.class);

    @Autowired
    LoanPlanRepository loanPlanRepository;

    @Autowired
    ActionLogService actionLogService;

    @Autowired
    Response response;

    @Override
    public boolean save(LoanPlan loanPlan) {
        try {
            loanPlanRepository.save(loanPlan);
            response.setCode(200);
            response.setMessage("Loan Plan Created Successfully");
            return true;
        } catch (Exception ex) {
            LOGGER.error("Creating Loan Plan Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating Loan Plan Failed");
            response.setData(ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<LoanPlan> findAll() {
        try {
            return loanPlanRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("Find All Loan Plans Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<LoanPlan> findAllPage(Pageable pageable) {
        try {
            return loanPlanRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Page Loan Plans Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<LoanPlan> findAllByStatus(String status, Pageable pageable) {
        try {
            return loanPlanRepository.findAllByActive(status, pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Loan Plans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public LoanPlan findById(String id) {
        try {
            return loanPlanRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.error("Find Loan Plan by Id Failed " + ex.getMessage());
            return null;
        }
    }
}
