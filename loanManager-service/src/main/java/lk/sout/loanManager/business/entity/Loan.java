package lk.sout.loanManager.business.entity;

import lk.sout.core.entity.MetaData;
import lk.sout.loanManager.borrower.entity.Borrower;
import org.springframework.data.annotation.*;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Component
@Document
public class Loan {

    @Id
    private String id;

    private String loanNo;

    private LocalDate dateTime;

    private LocalDate settlementDate;

    //this is the due date to current payment record
    private LocalDate nextPaymentRecordDate;

    @DBRef
    private LoanPlan loanPlan;

    @DBRef
    private Borrower borrower;

    //Records from LoanRecord collection will be saved here after the loan is settled
    private List<LoanRecord> loanRecords;

    private String borrowerNic;

    private double loanAmount;

    private double loanAmountWithInterest;

    //this is the loan amount with interest and no arrears amount added to this.
    private double loanSettlementAmount;

    private double installmentAmount;

    private double installmentWithoutInterest;

    private int installmentLeft;

    //not calculating yet, cause can count arrears records too.
    private int arrearsRecordCount;

    private double balance;

    private double paidAmount;

    private double overPaidAmount;

    private double arrearsAmount;

    @DBRef
    private MetaData status;

    //have to keep this due to inability of run where query on status
    private String statusCode;

    private LocalDate approvedDate;

    private String approvedBy;

    private String rejectedReason;

    private LocalDate rejectedDate;

    private boolean deductFromLedger;

    // active means status != settled
    private boolean active;

    private String appNo;

    @CreatedDate
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDate createdDate;

    @CreatedBy
    private String createdBy;

    @LastModifiedDate
    private Date lastModifiedDate;

    @LastModifiedBy
    private String lastModifiedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public LocalDate getDateTime() {
        return dateTime;
    }

    public void setDateTime(LocalDate dateTime) {
        this.dateTime = dateTime;
    }

    public void setNextPaymentRecordDate(LocalDate nextPaymentRecordDate) {
        this.nextPaymentRecordDate = nextPaymentRecordDate;
    }

    public double getArrearsAmount() {
        return arrearsAmount;
    }

    public void setArrearsAmount(double arrearsAmount) {
        this.arrearsAmount = arrearsAmount;
    }

    public LocalDate getNextPaymentRecordDate() {
        return nextPaymentRecordDate;
    }

    public LoanPlan getLoanPlan() {
        return loanPlan;
    }

    public void setLoanPlan(LoanPlan loanPlan) {
        this.loanPlan = loanPlan;
    }

    public Borrower getBorrower() {
        return borrower;
    }

    public double getLoanSettlementAmount() {
        return loanSettlementAmount;
    }

    public void setLoanSettlementAmount(double loanSettlementAmount) {
        this.loanSettlementAmount = loanSettlementAmount;
    }

    public void setBorrower(Borrower borrower) {
        this.borrower = borrower;
    }

    public double getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(double loanAmount) {
        this.loanAmount = loanAmount;
    }

    public int getArrearsRecordCount() {
        return arrearsRecordCount;
    }

    public void setArrearsRecordCount(int arrearsRecordCount) {
        this.arrearsRecordCount = arrearsRecordCount;
    }

    public double getBalance() {
        return balance;
    }

    public double getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(double paidAmount) {
        this.paidAmount = paidAmount;
    }

    public void setBalance(double balance) {
        this.balance = balance;
    }

    public MetaData getStatus() {
        return status;
    }

    public double getInstallmentWithoutInterest() {
        return installmentWithoutInterest;
    }

    public void setInstallmentWithoutInterest(double installmentWithoutInterest) {
        this.installmentWithoutInterest = installmentWithoutInterest;
    }

    public LocalDate getSettlementDate() {
        return settlementDate;
    }

    public void setSettlementDate(LocalDate settlementDate) {
        this.settlementDate = settlementDate;
    }

    public void setStatus(MetaData status) {
        this.status = status;
    }

    public LocalDate getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDate createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public String getBorrowerNic() {
        return borrowerNic;
    }

    public void setBorrowerNic(String borrowerNic) {
        this.borrowerNic = borrowerNic;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public LocalDate getApprovedDate() {
        return approvedDate;
    }

    public void setApprovedDate(LocalDate approvedDate) {
        this.approvedDate = approvedDate;
    }

    public String getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(String approvedBy) {
        this.approvedBy = approvedBy;
    }

    public List<LoanRecord> getLoanRecords() {
        return loanRecords;
    }

    public void setLoanRecords(List<LoanRecord> loanRecords) {
        this.loanRecords = loanRecords;
    }

    public double getInstallmentAmount() {
        return installmentAmount;
    }

    public void setInstallmentAmount(double installmentAmount) {
        this.installmentAmount = installmentAmount;
    }

    public double getLoanAmountWithInterest() {
        return loanAmountWithInterest;
    }

    public void setLoanAmountWithInterest(double loanAmountWithInterest) {
        this.loanAmountWithInterest = loanAmountWithInterest;
    }

    public double getOverPaidAmount() {
        return overPaidAmount;
    }

    public void setOverPaidAmount(double overPaidAmount) {
        this.overPaidAmount = overPaidAmount;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public LocalDate getRejectedDate() {
        return rejectedDate;
    }

    public void setRejectedDate(LocalDate rejectedDate) {
        this.rejectedDate = rejectedDate;
    }

    public String getRejectedReason() {
        return rejectedReason;
    }

    public void setRejectedReason(String rejectedReason) {
        this.rejectedReason = rejectedReason;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public int getInstallmentLeft() {
        return installmentLeft;
    }

    public void setInstallmentLeft(int installmentLeft) {
        this.installmentLeft = installmentLeft;
    }

    public boolean isDeductFromLedger() {
        return deductFromLedger;
    }

    public void setDeductFromLedger(boolean deductFromLedger) {
        this.deductFromLedger = deductFromLedger;
    }

    public String getAppNo() {
        return appNo;
    }

    public void setAppNo(String appNo) {
        this.appNo = appNo;
    }
}
