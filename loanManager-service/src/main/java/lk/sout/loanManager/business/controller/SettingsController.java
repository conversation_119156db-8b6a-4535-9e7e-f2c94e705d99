package lk.sout.loanManager.business.controller;

import lk.sout.loanManager.business.entity.Settings;
import lk.sout.loanManager.business.service.SettingsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Settings Controller for managing application settings
 */
@RestController
@RequestMapping("/settings")
@CrossOrigin(origins = "*")
public class SettingsController {

    @Autowired
    private SettingsService settingsService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> save(@RequestBody Settings settings) {
        try {
            Settings savedSettings = settingsService.save(settings);
            if (savedSettings != null) {
                return ResponseEntity.ok(savedSettings);
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public ResponseEntity<?> findAll() {
        try {
            return ResponseEntity.ok(settingsService.findAll());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(value = "/findAllActive", method = RequestMethod.GET)
    public ResponseEntity<?> findAllActive() {
        try {
            return ResponseEntity.ok(settingsService.findAllActive());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET)
    public ResponseEntity<?> findById(@RequestParam String id) {
        try {
            Settings settings = settingsService.findById(id);
            if (settings != null) {
                return ResponseEntity.ok(settings);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(value = "/findByKey", method = RequestMethod.GET)
    public ResponseEntity<?> findByKey(@RequestParam String settingKey) {
        try {
            Settings settings = settingsService.findBySettingKey(settingKey);
            if (settings != null) {
                return ResponseEntity.ok(settings);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(value = "/getValue", method = RequestMethod.GET)
    public ResponseEntity<?> getValue(@RequestParam String settingKey) {
        try {
            String value = settingsService.getSettingValue(settingKey);
            if (value != null) {
                return ResponseEntity.ok(value);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(value = "/updateSetting", method = RequestMethod.PUT)
    public ResponseEntity<?> updateSetting(@RequestParam String settingKey, @RequestParam String settingValue) {
        try {
            return ResponseEntity.ok(settingsService.updateSetting(settingKey, settingValue));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(value = "/createDefaults", method = RequestMethod.POST)
    public ResponseEntity<?> createDefaults() {
        try {
            return ResponseEntity.ok(settingsService.createDefaultSettings());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
