package lk.sout.loanManager.business.repository;

import lk.sout.loanManager.business.entity.Payment;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface PaymentRepository extends MongoRepository<Payment, String> {

    List<Payment> findAllByBorrowerTp(String tp);

    List<Payment> findByDateTimeBetween(LocalDateTime start, LocalDateTime end);

}
