package lk.sout.loanManager.business.service;

import lk.sout.core.entity.Response;
import lk.sout.loanManager.business.entity.Settings;

import java.time.LocalDate;
import java.util.List;

public interface SettingsService {
    
    Settings save(Settings settings);
    
    List<Settings> findAll();
    
    List<Settings> findAllActive();
    
    Settings findById(String id);
    
    Settings findBySettingKey(String settingKey);
    
    String getSettingValue(String settingKey);
    
    boolean getBooleanSetting(String settingKey, boolean defaultValue);
    
    int getIntegerSetting(String settingKey, int defaultValue);
    
    double getDoubleSetting(String settingKey, double defaultValue);
    
    Response updateSetting(String settingKey, String settingValue);
    
    Response createDefaultSettings();
    
    LocalDate calculateDueDate(LocalDate baseDate, int paymentFrequencyInDays);
}
