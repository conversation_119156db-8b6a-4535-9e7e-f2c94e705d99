package lk.sout.loanManager.business.service;

import lk.sout.core.entity.Response;
import lk.sout.loanManager.business.entity.ActionLog;
import org.springframework.data.domain.Page;

import java.time.LocalDateTime;

public interface ActionLogService {

    Response save(ActionLog actionLog);

    Page<ActionLog> findAll(int page, int size);

    Page<ActionLog> findByUser(String username, int page, int size);

    Page<ActionLog> findByAction(String action, int page, int size);

    Page<ActionLog> findByDateRange(LocalDateTime startDate, LocalDateTime endDate, int page, int size);

    Page<ActionLog> findByDateRangeAndUser(LocalDateTime startDate, LocalDateTime endDate, String username, int page, int size);

    Page<ActionLog> findByDateRangeAndAction(LocalDateTime startDate, LocalDateTime endDate, String action, int page, int size);

    void logAction(String action, String description, String performedBy, Object details);

    void logLoanPlanChange(String loanPlanId, String changes, String performedBy);

    void logLoanEdit(String loanId, String changes, String performedBy);

    void logUserAction(String action, String description, String performedBy);
}
