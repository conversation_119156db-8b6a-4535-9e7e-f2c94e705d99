package lk.sout.loanManager.business.entity;

import org.springframework.data.annotation.*;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Date;

@Component
@Document
public class RejectedLoan {

    @Id
    private String id;

    private String loanNo;

    private String customerName;

    private String customerNic;

    private String telephone;

    private double amount;

    private String loanPlanName;

    private String rejectionReason;

    private String rejectedBy;

    private LocalDate rejectedDate;

    @CreatedDate
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDate createdDate;

    @CreatedBy
    private String createdBy;

    @LastModifiedDate
    private Date lastModifiedDate;

    @LastModifiedBy
    private String lastModifiedBy;

    // Default constructor
    public RejectedLoan() {}

    // Constructor with essential fields
    public RejectedLoan(String loanNo, String customerName, String customerNic, String telephone, 
                       double amount, String loanPlanName, String rejectionReason, String rejectedBy) {
        this.loanNo = loanNo;
        this.customerName = customerName;
        this.customerNic = customerNic;
        this.telephone = telephone;
        this.amount = amount;
        this.loanPlanName = loanPlanName;
        this.rejectionReason = rejectionReason;
        this.rejectedBy = rejectedBy;
        this.rejectedDate = LocalDate.now();
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getLoanNo() { return loanNo; }
    public void setLoanNo(String loanNo) { this.loanNo = loanNo; }

    public String getCustomerName() { return customerName; }
    public void setCustomerName(String customerName) { this.customerName = customerName; }

    public String getCustomerNic() { return customerNic; }
    public void setCustomerNic(String customerNic) { this.customerNic = customerNic; }

    public String getTelephone() { return telephone; }
    public void setTelephone(String telephone) { this.telephone = telephone; }

    public double getAmount() { return amount; }
    public void setAmount(double amount) { this.amount = amount; }

    public String getLoanPlanName() { return loanPlanName; }
    public void setLoanPlanName(String loanPlanName) { this.loanPlanName = loanPlanName; }

    public String getRejectionReason() { return rejectionReason; }
    public void setRejectionReason(String rejectionReason) { this.rejectionReason = rejectionReason; }

    public String getRejectedBy() { return rejectedBy; }
    public void setRejectedBy(String rejectedBy) { this.rejectedBy = rejectedBy; }

    public LocalDate getRejectedDate() { return rejectedDate; }
    public void setRejectedDate(LocalDate rejectedDate) { this.rejectedDate = rejectedDate; }

    public LocalDate getCreatedDate() { return createdDate; }
    public void setCreatedDate(LocalDate createdDate) { this.createdDate = createdDate; }

    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }

    public Date getLastModifiedDate() { return lastModifiedDate; }
    public void setLastModifiedDate(Date lastModifiedDate) { this.lastModifiedDate = lastModifiedDate; }

    public String getLastModifiedBy() { return lastModifiedBy; }
    public void setLastModifiedBy(String lastModifiedBy) { this.lastModifiedBy = lastModifiedBy; }
}
