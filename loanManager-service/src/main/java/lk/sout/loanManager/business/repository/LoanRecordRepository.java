package lk.sout.loanManager.business.repository;

import lk.sout.core.entity.MetaData;
import lk.sout.loanManager.business.entity.LoanRecord;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface LoanRecordRepository extends MongoRepository<LoanRecord, String> {

    List<LoanRecord> findAllByLoanNo(String loanNo);

    List<LoanRecord> findAllByLoanNoAndStatusNot(String loanNo, MetaData status);

    List<LoanRecord> findAllByBorrowerNicAndStatusNot(String loanNo, MetaData status);

    List<LoanRecord> findAllByBorrowerTp1AndStatusNot(String loanNo, MetaData status);

    List<LoanRecord> findByInstallmentDate(LocalDate date);

    List<LoanRecord> findByInstallmentDateAndLoanNo(LocalDate date, String loanNo);

    List<LoanRecord> findByStatus(MetaData status);

    List<LoanRecord> findAllByStatus(MetaData status);

    List<LoanRecord> findByStatusAndDueDateBefore(MetaData status, LocalDate date);

    List<LoanRecord> findByStatusAndBorrowerNameLike(MetaData status, String name);

    List<LoanRecord> findByStatusAndLoanNo(MetaData status, String loanNo);

    List<LoanRecord> findByStatusAndLoanNoOrderByBalance(MetaData status, String loanNo);

    Integer countByStatusAndLoanNo(MetaData status, String loanNo);

    Integer countByLoanNo(String loanNo);

    List<LoanRecord> findByInstallmentDateAndStatus(LocalDate date, MetaData paymentPending);

    List<LoanRecord> findByInstallmentDateAndStatusAndBorrowerNameLike(LocalDate date, MetaData paymentPending, String name);

    List<LoanRecord> findByInstallmentDateAndPaidAmount(LocalDate date, Double paidAmount);

}
