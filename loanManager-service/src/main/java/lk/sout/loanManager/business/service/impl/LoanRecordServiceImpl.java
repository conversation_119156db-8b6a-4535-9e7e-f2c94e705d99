package lk.sout.loanManager.business.service.impl;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.core.service.MetaDataService;
import lk.sout.loanManager.business.entity.*;
import lk.sout.loanManager.business.repository.LoanRecordRepository;
import lk.sout.loanManager.business.service.LedgerService;
import lk.sout.loanManager.business.service.LoanPaymentService;
import lk.sout.loanManager.business.service.LoanRecordService;
import lk.sout.loanManager.business.service.LoanService;
import lk.sout.loanManager.business.service.SettingsService;
import lk.sout.loanManager.business.service.HolidayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class LoanRecordServiceImpl implements LoanRecordService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoanRecordServiceImpl.class);

    @Autowired
    LoanRecordRepository loanRecordRepository;

    @Autowired
    Response response;

    @Autowired
    LoanService loanService;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    Payment payment;

    @Autowired
    LoanPaymentService loanPaymentService;

    @Autowired
    LedgerService ledgerService;

    @Autowired
    LedgerRecordServiceImpl ledgerRecordService;

    @Autowired
    SettingsService settingsService;

    @Autowired
    HolidayService holidayService;

    @Autowired
    LedgerRecord ledgerRecord;

    @Override
    public boolean save(LoanRecord loanRecord) {
        try {
            loanRecordRepository.save(loanRecord);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving Loan Record Failed " + ex.getMessage());
        }
        return false;
    }

    @Override
    public List<LoanRecord> findAll() {
        try {
            return loanRecordRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("Find all Failed" + ex.getMessage());
        }
        return null;
    }

    @Override
    public LoanRecord findById(String id) {
        try {
            return loanRecordRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.error("Find By Id Failed" + ex.getMessage());
        }
        return null;
    }

    @Override
    public List<LoanRecord> findByStatus(MetaData metaData) {
        try {
            return loanRecordRepository.findByStatus(metaData);
        } catch (Exception ex) {
            LOGGER.error("Find By Id Failed" + ex.getMessage());
        }
        return null;
    }

    @Override
    public List<LoanRecord> findByStatusAndLoanNo(MetaData metaData, String loanNo) {
        try {
            return loanRecordRepository.findByStatusAndLoanNo(metaData, loanNo);
        } catch (Exception ex) {
            LOGGER.error("Find By Id Failed" + ex.getMessage());
        }
        return null;
    }

    @Override
    public List<LoanRecord> findByStatusAndLoanNoOrderByBalance(MetaData metaData, String loanNo) {
        try {
            return loanRecordRepository.findByStatusAndLoanNoOrderByBalance(metaData, loanNo);
        } catch (Exception ex) {
            LOGGER.error("Find By Id Failed" + ex.getMessage());
        }
        return null;
    }

    @Override
    public int countByLoanNo(String loanNo) {
        try {
            return loanRecordRepository.countByLoanNo(loanNo);
        } catch (Exception ex) {
            LOGGER.error("Find By Id Failed" + ex.getMessage());
        }
        return 0;
    }

    @Override
    public int countByStatusAndLoanNo(MetaData metaData, String loanNo) {
        try {
            return loanRecordRepository.countByStatusAndLoanNo(metaData, loanNo);
        } catch (Exception ex) {
            LOGGER.error("Find By Id Failed" + ex.getMessage());
        }
        return 0;
    }

    @Override
    public List<LoanRecord> findAllRecordsByLoanNo(String loan) {
        try {
            return loanRecordRepository.findAllByLoanNo(loan);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LoanRecord> findAllRecordsByLoanNoAndStatusNot(String loanNo, MetaData status) {
        try {
            return loanRecordRepository.findAllByLoanNoAndStatusNot(loanNo, status);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LoanRecord> findPendingRecordsByNic(String nic) {
        try {
            MetaData settled = metaDataService.searchMetaData("Settled", "Loan Record Status");
            return loanRecordRepository.findAllByBorrowerNicAndStatusNot(nic, settled);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LoanRecord> findPendingRecordsByTp1(String tp1) {
        try {
            MetaData settled = metaDataService.searchMetaData("Settled", "Loan Record Status");
            return loanRecordRepository.findAllByBorrowerTp1AndStatusNot(tp1, settled);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LoanRecord> findPendingRecordsByLoanNo(String loan) {
        try {
            MetaData settled = metaDataService.searchMetaData("Settled", "Loan Record Status");
            return loanRecordRepository.findAllByLoanNoAndStatusNot(loan, settled);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LoanRecord> findTodayList() {
        try {
            MetaData paymentPending = metaDataService.searchMetaData("Payment Pending", "Loan Record Status");
            return loanRecordRepository.findAllByStatus(paymentPending);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LoanRecord> findTodayLoanByName(String name) {
        try {
            MetaData paymentPending = metaDataService.searchMetaData("Payment Pending", "Loan Record Status");
            return loanRecordRepository.findByInstallmentDateAndStatusAndBorrowerNameLike(LocalDate.now(), paymentPending, name);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LoanRecord> findArrearsList() {
        try {
            MetaData arrears = metaDataService.searchMetaData("Arrears", "Loan Record Status");
            return loanRecordRepository.findAllByStatus(arrears);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LoanRecord> findArrearsLoanByName(String name) {
        try {
            MetaData arrears = metaDataService.searchMetaData("Arrears", "Loan Record Status");
            return loanRecordRepository.findByStatus(arrears);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LoanRecord> findUnPaidTodayList() {
        try {
            MetaData pending = metaDataService.searchMetaData("Payment Pending", "Loan Record Status");
            return loanRecordRepository.findByStatusAndDueDateBefore(pending, LocalDate.now().plusDays(1));
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    //this will make a payment record for the loan installment
    //this will also update the loan record status and loan status
    @Override
    @Transactional
    public Response create(String loanNo, String mode, double installment) {
        try {
            MetaData loanRecordStatus = metaDataService.searchMetaData("Payment Pending", "Loan Record Status");

            Loan loan = loanService.findByLoanNo(loanNo);

            LoanRecord newRecord = new LoanRecord();
            newRecord.setLoanNo(loan.getLoanNo());
            newRecord.setStatus(loanRecordStatus);
            newRecord.setStatusCode(loanRecordStatus.getName());
            newRecord.setBorrowerName(loan.getBorrower().getName());
            newRecord.setBorrowerAddress(loan.getBorrower().getAddress());
            newRecord.setBorrowerNic(loan.getBorrower().getNic());
            newRecord.setBorrowerTp1(loan.getBorrower().getTelephone1());
            newRecord.setBorrowerTp2(loan.getBorrower().getTelephone2());
            newRecord.setRouteName(loan.getBorrower().getRoute().getName());
            newRecord.setLoanPlan(loan.getLoanPlan().getName());
            newRecord.setInstallmentDate(LocalDate.now());
            if (mode.equals("Auto")) {
                newRecord.setInstallmentAmount(loan.getInstallmentAmount());
                newRecord.setInstallmentWithoutInterest(loan.getInstallmentWithoutInterest());
                newRecord.setBalance(loan.getInstallmentAmount());
            }
            if (mode.equals("Manual")) {
                newRecord.setInstallmentAmount(installment);
                newRecord.setInstallmentWithoutInterest(installment - (installment *
                        loan.getLoanPlan().getInterestRate() / 100 / 30 * loan.getLoanPlan().getDurationInDays()));
                newRecord.setBalance(installment);
            }
            newRecord.setDaysDue(0);

            // Check setting to determine due date calculation
            boolean skipHolidays = settingsService.getBooleanSetting("SKIP_HOLIDAYS_FOR_DUE_DATE", false);

            if (skipHolidays) {
                // Option 1: Set next working day as due date (skipping holidays only)
                LocalDate nextWorkingDay = LocalDate.now().plusDays(1);
                while (isHoliday(nextWorkingDay)) {
                    nextWorkingDay = nextWorkingDay.plusDays(1);
                }
                newRecord.setDueDate(nextWorkingDay);
            } else {
                // Option 2: Set due date according to loan plan
                newRecord.setDueDate(LocalDate.now().plusDays(loan.getLoanPlan().getPaymentFrequencyInDays()));
            }

            newRecord = loanRecordRepository.save(newRecord);

            if (mode.equals("Auto")) {
                LocalDate nextWorkingDay = LocalDate.now().plusDays(loan.getLoanPlan().getPaymentFrequencyInDays());
                while (isHoliday(nextWorkingDay)) {
                    nextWorkingDay = nextWorkingDay.plusDays(1);
                }
                loan.setNextPaymentRecordDate(nextWorkingDay);
                loanService.save(loan);
            }

            response.setCode(200);
            response.setData(newRecord.getId());
            response.setMessage("Loan installment Successfully Paid");
            return response;
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Loan installment Payment Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Loan installment Payment Failed");
            response.setData(ex.getMessage());
            return response;
        }
    }

    @Override
    @Transactional
    public Response pay(String loanRecId, double payingAmount, String appNo) {
        try {
            MetaData issuedStatus = metaDataService.searchMetaData("Issued", "Loan Status");
            MetaData currentStatus = metaDataService.searchMetaData("Current", "Loan Status");
            MetaData arrears = metaDataService.searchMetaData("Arrears", "Loan Record Status");

            MetaData loanRecordStatusPending = metaDataService.searchMetaData("Payment Pending", "Loan Record Status");
            MetaData loanRecordStatusSettled = metaDataService.searchMetaData("Settled", "Loan Record Status");

            LoanRecord loanRecord = loanRecordRepository.findById(loanRecId).get();
            loanRecord.setPaidAmount(loanRecord.getPaidAmount() + payingAmount);
            loanRecord.setPaidDate(LocalDate.now());
            double balance = loanRecord.getBalance() - payingAmount;
            loanRecord.setBalance(balance);

            if (balance <= 0) {
                loanRecord.setStatus(loanRecordStatusSettled);
                loanRecord.setStatusCode("3"); // Set to "3" for Settled loan record status
            } else {
                loanRecord.setStatus(loanRecordStatusPending);
                loanRecord.setStatusCode("1"); // Set to "1" for Pending loan record status
            }

            loanRecordRepository.save(loanRecord);

            Loan loan = loanService.findByLoanNo(loanRecord.getLoanNo());
            loan.setBalance(loan.getBalance() - payingAmount);
            loan.setPaidAmount(loan.getPaidAmount() + payingAmount);
            loan.setInstallmentLeft(loan.getInstallmentLeft() - 1);
            if (loan.getStatus().getId().equals(issuedStatus.getId())) {
                loan.setStatus(currentStatus);
                loan.setStatusCode("3"); // Set to "3" for Current status
            }

            if (loan.getStatus().getId().equals(arrears.getId()) &&
                    loanRecordRepository.countByStatusAndLoanNo(arrears, loan.getLoanNo()) <=
                            loan.getLoanPlan().getArrearsInterestDuration()) {
                loan.setStatus(currentStatus);
                loan.setStatusCode("3"); // Set to "3" for Current status
            }

            loan.setOverPaidAmount(loan.getOverPaidAmount() + (balance * -1));

            loan = loanService.save(loan);

            if (loan.getPaidAmount() >= loan.getLoanAmountWithInterest()) {
                loanService.settleLoan(loan.getLoanNo());
            }

            payment.setId(null);
            payment.setAmount(payingAmount);
            payment.setLoanPlan(loanRecord.getLoanPlan());
            payment.setBorrowerTp(loanRecord.getBorrowerTp1());
            payment.setRouteName(loanRecord.getRouteName());
            payment.setBorrowerName(loanRecord.getBorrowerName());
            payment.setLoanNo(loanRecord.getLoanNo());
            payment.setLoanRecordId(loanRecId);
            payment.setDateTime(LocalDateTime.now());
            payment.setAppNo(appNo);
            payment.setProfit(loanRecord.getInstallmentAmount() - loanRecord.getInstallmentWithoutInterest());
            loanPaymentService.save(payment);

            ledgerRecordService.create(appNo, payingAmount, "Loan Installment Payment", loan.getLoanNo(),
                    loan.getBorrower().getName(), "+");

            response.setCode(200);
            response.setMessage("Paid Successfully");
            return response;
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Payment unsuccessful " + ex.getMessage());
            response.setCode(401);
            response.setData(ex.getMessage());
            response.setMessage("Something went Wrong");
            return response;
        }
    }

    @Override
    @Transactional
    public Response amend(String loanRecId, String paymentId, String appNo) {
        try {
            MetaData loanRecordStatusPending = metaDataService.searchMetaData("Payment Pending", "Loan Record Status");

            LoanRecord loanRecord = loanRecordRepository.findById(loanRecId).get();
            Payment payment = loanPaymentService.findById(paymentId);

            double paidAmount = payment.getAmount();

            loanRecord.setStatus(loanRecordStatusPending);
            loanRecord.setStatusCode(loanRecordStatusPending.getName());
            loanRecord.setPaidAmount(loanRecord.getPaidAmount() - paidAmount);
            loanRecord.setBalance(loanRecord.getBalance() + paidAmount);
            loanRecordRepository.save(loanRecord);

            Loan loan = loanService.findByLoanNo(loanRecord.getLoanNo());
            loan.setBalance(loan.getBalance() + payment.getAmount());
            loan.setPaidAmount(loan.getPaidAmount() - payment.getAmount());
            loan.setInstallmentLeft(loan.getInstallmentLeft() + 1);
            if (loan.getInstallmentAmount() < paidAmount) {
                loan.setOverPaidAmount(loan.getOverPaidAmount() - (paidAmount - loan.getInstallmentAmount()));
            }
            loanService.save(loan);

            payment.setAmount(0.0);
            payment.setDateTime(LocalDateTime.now());
            payment.setProfit(0.0);
            payment.setAppNo(appNo);
            loanPaymentService.save(payment);

            ledgerRecordService.create(appNo, paidAmount, "Payment Cancellation", loan.getLoanNo(),
                    loan.getBorrower().getName(), "-");

            response.setCode(200);
            response.setMessage("Paid Successfully");
            return response;
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Payment unsuccessful " + ex.getMessage());
            response.setCode(401);
            response.setData(ex.getMessage());
            response.setMessage("Something went Wrong");
            return response;
        }
    }

    @Override
    @Transactional
    public Response createAndPayLoanRecord(String loanNo, double payingAmount, String appNo) {
        try {
            MetaData issuedStatus = metaDataService.searchMetaData("Issued", "Loan Status");
            MetaData currentStatus = metaDataService.searchMetaData("Current", "Loan Status");

            MetaData loanRecordStatusPending = metaDataService.searchMetaData("Payment Pending", "Loan Record Status");
            MetaData loanRecordStatusSettled = metaDataService.searchMetaData("Settled", "Loan Record Status");

            Response rps = create(loanNo, "Manual", payingAmount);

            LoanRecord loanRecord = loanRecordRepository.findById(rps.getData()).get();
            loanRecord.setPaidAmount(loanRecord.getPaidAmount() + payingAmount);
            loanRecord.setPaidDate(LocalDate.now());
            double balance = loanRecord.getBalance() - payingAmount;
            loanRecord.setBalance(balance);

            if (balance <= 0) {
                loanRecord.setStatus(loanRecordStatusSettled);
                loanRecord.setStatusCode("3"); // Set to "3" for Settled loan record status
            } else {
                loanRecord.setStatus(loanRecordStatusPending);
                loanRecord.setStatusCode("1"); // Set to "1" for Pending loan record status
            }

            loanRecordRepository.save(loanRecord);

            Loan loan = loanService.findByLoanNo(loanRecord.getLoanNo());
            loan.setBalance(loan.getBalance() - payingAmount);
            loan.setPaidAmount(loan.getPaidAmount() + payingAmount);
            loan.setInstallmentLeft(loan.getInstallmentLeft() - 1);
            if (loan.getStatus().getId().equals(issuedStatus.getId())) {
                loan.setStatus(currentStatus);
                loan.setStatusCode("3"); // Set to "3" for Current status
            }
            if (balance < 0) {
                loan.setOverPaidAmount(loan.getOverPaidAmount() + (balance * -1));
            }
            loan = loanService.save(loan);

            if (loan.getBalance() <= 0) {
                loanService.settleLoan(loan.getLoanNo());
            }

            payment.setId(null);
            payment.setAmount(payingAmount);
            payment.setLoanPlan(loanRecord.getLoanPlan());
            payment.setAppNo(appNo);
            payment.setLoanRecordId(loanRecord.getId());
            payment.setBorrowerTp(loanRecord.getBorrowerTp1());
            payment.setRouteName(loanRecord.getRouteName());
            payment.setBorrowerName(loanRecord.getBorrowerName());
            payment.setLoanNo(loanRecord.getLoanNo());
            payment.setDateTime(LocalDateTime.now());
            payment.setProfit(loanRecord.getInstallmentAmount() - loanRecord.getInstallmentWithoutInterest());
            loanPaymentService.save(payment);

            ledgerRecordService.create(appNo, payingAmount, "Custom Loan Payment", loan.getLoanNo(),
                    loan.getBorrower().getName(), "+");

            response.setCode(200);
            response.setMessage("Paid Successfully");
            return response;
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Payment unsuccessful " + ex.getMessage());
            response.setCode(401);
            response.setData(ex.getMessage());
            response.setMessage("Something went Wrong");
            return response;
        }
    }

    @Override
    @Transactional
    public Response removeUnpaidRecordsForDate(LocalDate date) {
        try {
            // Find all loan records for the specified date with no payments (paidAmount = 0)
            List<LoanRecord> unpaidRecords = loanRecordRepository.findByInstallmentDateAndPaidAmount(date, 0.0);

            if (unpaidRecords.isEmpty()) {
                response.setCode(200);
                response.setMessage("No unpaid records found for the selected date");
                return response;
            }

            int deletedCount = 0;
            for (LoanRecord record : unpaidRecords) {
                // Double check that paidAmount is 0 for safety
                if (record.getPaidAmount() == 0 || record.getPaidAmount() == 0.0) {
                    loanRecordRepository.delete(record);
                    deletedCount++;
                    LOGGER.info("Deleted unpaid loan record: " + record.getId() + " for loan: " + record.getLoanNo());
                }
            }

            response.setCode(200);
            response.setMessage("Successfully removed " + deletedCount + " unpaid loan records for " + date);
            return response;
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Removing unpaid records failed: " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Failed to remove unpaid records: " + ex.getMessage());
            return response;
        }
    }

    private boolean isHoliday(LocalDate date) {
        try {
            return holidayService.findAll().stream()
                    .anyMatch(holiday -> holiday.getDate().equals(date));
        } catch (Exception ex) {
            LOGGER.error("Error checking holiday: " + ex.getMessage());
            return false;
        }
    }

}
