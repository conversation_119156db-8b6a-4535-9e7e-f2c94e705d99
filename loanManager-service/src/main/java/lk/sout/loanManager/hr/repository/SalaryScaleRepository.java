package lk.sout.loanManager.hr.repository;


import lk.sout.loanManager.hr.entity.SalaryScale;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SalaryScaleRepository extends MongoRepository<SalaryScale, String> {

    Page<SalaryScale> findAll(Pageable pageable);

    List<SalaryScale> findAllByNameLikeIgnoreCase(String any);

    SalaryScale findSalaryScaleById(String id);
    SalaryScale findByName(String name);
}


