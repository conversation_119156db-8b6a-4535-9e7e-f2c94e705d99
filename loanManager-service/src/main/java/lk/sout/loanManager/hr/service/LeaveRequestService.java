package lk.sout.loanManager.hr.service;

import lk.sout.loanManager.hr.entity.LeaveRequest;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

public interface LeaveRequestService {
    boolean save(LeaveRequest leaveRequest);

    Iterable<LeaveRequest> findAll(Pageable pageable);


    List<LeaveRequest> findByEpf(String any);

    boolean findByDatesAndEmployee(Date from, Date to, String employee);
}

