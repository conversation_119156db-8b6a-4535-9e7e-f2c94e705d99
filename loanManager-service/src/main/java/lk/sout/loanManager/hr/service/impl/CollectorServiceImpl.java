package lk.sout.loanManager.hr.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.loanManager.hr.entity.Collector;
import lk.sout.loanManager.hr.repository.CollectorRepository;
import lk.sout.loanManager.hr.service.CollectorService;
import lk.sout.util.FileUtil;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

@Service
public class CollectorServiceImpl implements CollectorService {

    public static final Logger LOGGER = (Logger) LoggerFactory.getLogger(CollectorServiceImpl.class);

    @Autowired
    CollectorRepository collectorRepository;

    @Autowired
    Response response;

    @Autowired
    FileUtil fileUtil;

    @Value("${uploadPath}")
    private String uploadPath;

    @Override
    @Transactional
    public Response save(Collector collector, List<MultipartFile> images) {
        try {
            collectorRepository.save(collector);
            if (null != images) {
                int counter = 1;
                List<String> urls = new ArrayList<>();
                for (MultipartFile file : images) {
                    String fileExtension = FilenameUtils.getExtension(file.getOriginalFilename());
                    String filePath = collector.getName().trim().replaceAll(" ", "_")
                            + "_" + counter;
                    fileUtil.fileUploadService(file, collector.getId(), filePath);
                    urls.add(uploadPath + collector.getId() + "/" + filePath + "." + fileExtension);
                    counter++;
                }
                collector.setImageUrls(urls);
                collectorRepository.save(collector);
            }
            response.setCode(200);
            response.setMessage("Collector Created Successfully");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Creating collector Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating collector Failed");
            response.setData(ex.getMessage());
            return response;
        }

    }

    @Override
    public Iterable<Collector> findAll(Pageable pageable) {
        return collectorRepository.findAll(pageable);
    }

    @Override
    public Iterable<Collector> findAllActive(Pageable pageable) {
        try {
            Iterable<Collector> u = collectorRepository.findAllByActive(pageable, true);
            return collectorRepository.findAllByActive(pageable, true);
        } catch (Exception ex) {
            LOGGER.error("Find All Item Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Collector> findAllInactive(Pageable pageable) {
        try {
            Boolean active = false;
            return collectorRepository.findAllByActive(pageable, active);
        } catch (Exception ex) {
            LOGGER.error("Retrieving All collectors failed");
            return null;
        }
    }


    @Override
    public List<Collector> searchByName(String name) {
        try {
            Boolean active = true;
            return collectorRepository.findAllByNameLikeIgnoreCaseAndActive(name, active);
        } catch (Exception ex) {
            LOGGER.error("Retrieving a collector failed");
            return null;
        }
    }

    @Override
    public List<Collector> searchInactiveByName(String name) {
        try {
            Boolean active = false;
            return collectorRepository.findAllByNameLikeIgnoreCaseAndActive(name, active);
        } catch (Exception ex) {
            LOGGER.error("Retrieving a collector failed");
            return null;
        }
    }

    @Override
    public List<Collector> searchByNicNumber(String nicNumber) {
        try {
            Boolean active = true;
            return collectorRepository.findAllByNicNumberLikeIgnoreCaseAndActive(nicNumber, active);
        } catch (Exception ex) {
            LOGGER.error("Retrieving a collector failed");
            return null;
        }
    }

    @Override
    public List<Collector> searchInactiveByNicNumber(String nicNumber) {
        try {
            Boolean active = false;
            return collectorRepository.findAllByNicNumberLikeIgnoreCaseAndActive(nicNumber, active);
        } catch (Exception ex) {
            LOGGER.error("Retrieving a collector failed");
            return null;
        }
    }


    @Override
    public String delete(String id) {

        try {
            collectorRepository.deleteById(id);
            return "success";
        } catch (Exception ex) {
            LOGGER.error("Removing collector failed " + id + ". " + ex.getMessage());
            return "failed";
        }
    }

    @Override
    public Boolean checkNicNumberAvailability(String nicNumber) {
        try {
            if (null != collectorRepository.findCollectorByNicNumberIgnoreCase(nicNumber)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            LOGGER.error("check Vehicle by Vehicle Number Failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Boolean checkLicenseNoAvailability(String licenseNo) {
        return null;
    }
//
//    @Override
//    public boolean saveLogo(String logo) {
//        try {
////            Collector collector = collectorRepository.findAll().get(0);
////            collector.setLogo(logo);
////            collectorRepository.save(collector);
////            LOGGER.info("Collector info saved");
//            return true;
//
//        } catch (Exception ex) {
//            LOGGER.error("Collector saved" + ex.getMessage());
//            return false;
//        }
//
//    }
}
