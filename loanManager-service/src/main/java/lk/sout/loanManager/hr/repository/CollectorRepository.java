package lk.sout.loanManager.hr.repository;

import lk.sout.loanManager.hr.entity.Collector;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CollectorRepository extends MongoRepository<Collector, String> {

    Page<Collector> findAllByActive(Pageable pageable, boolean value);

    List<Collector> findAllByNameLikeIgnoreCaseAndActive(String key, Boolean active);

    List<Collector> findAllByNicNumberLikeIgnoreCaseAndActive(String key, Boolean active);

    Collector findCollectorByNicNumberIgnoreCase(String nicNumber);
}
