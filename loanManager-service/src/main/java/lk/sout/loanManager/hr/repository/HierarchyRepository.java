package lk.sout.loanManager.hr.repository;

import lk.sout.loanManager.hr.entity.Employee;
import lk.sout.loanManager.hr.entity.Hierarchy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface HierarchyRepository extends MongoRepository<Hierarchy,String> {
    Page<Hierarchy> findAll(Pageable pageable);

    List<Hierarchy> findAllByReportingManager(Employee employee);

}
