package lk.sout.loanManager.hr.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lk.sout.loanManager.hr.entity.Collector;
import lk.sout.loanManager.hr.service.CollectorService;
import lk.sout.util.FileUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/collector")
public class CollectorController {

    @Autowired
    CollectorService collectorService;

    @Autowired
    FileUtil fileUtil;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(
            @RequestParam("collector") String collector,
            @RequestParam(required = false) List<MultipartFile> images) {
        try {
            ObjectMapper mapper = JsonMapper.builder()
                    .addModule(new JavaTimeModule())
                    .build();
            Collector drvr = mapper.readValue(collector, Collector.class);
            return ResponseEntity.ok(collectorService.save(drvr, images));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    private ResponseEntity<?> findAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(collectorService.findAllActive(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllInactive", method = RequestMethod.GET)
    private ResponseEntity<?> findAllInactive(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(collectorService.findAllInactive(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    //    Search helper details using by name
    @RequestMapping(value = "/search_by_name", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchByName(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(collectorService.searchByName(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    //    Search inactive helper details
    @RequestMapping(value = "/search_inactive_by_name", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchInactiveByName(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(collectorService.searchInactiveByName(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/search_by_nicNumber", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchByNicNumber(@RequestParam("nicNumber") String nicNumber) {
        try {
            return ResponseEntity.ok(collectorService.searchByNicNumber(nicNumber));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/search_inactive_by_nicNumber", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchInactiveByNicNumber(@RequestParam("nicNumber") String nicNumber) {
        try {
            return ResponseEntity.ok(collectorService.searchInactiveByNicNumber(nicNumber));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/delete", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> delete(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(collectorService.delete(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


    @RequestMapping(value = "/checkNumber", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> checkNicNumberAvailability(@RequestParam("nicNumber") String nicNumber) {
        try {
            return ResponseEntity.ok(collectorService.checkNicNumberAvailability(nicNumber));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


}


