package lk.sout.loanManager.hr.service.impl;

import lk.sout.loanManager.hr.entity.Leave;
import lk.sout.loanManager.hr.repository.LeaveRepository;
import lk.sout.loanManager.hr.service.LeaveService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class LeaveServiceImpl implements LeaveService {

    final static Logger LOGGER = LoggerFactory.getLogger(Leave.class);

    @Autowired
    LeaveRepository leaveRepository;

    @Override
    public boolean save(Leave leave) {
        try {
            leaveRepository.save(leave);
            return true;
        } catch (Exception ex) {
            LOGGER.error("add leave saving failed" + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<Leave> findAll(Pageable pageable) {
        try {
            return leaveRepository.findAll(pageable);

        } catch (Exception ex) {
            LOGGER.error("add leave   failed" + ex.getMessage());
            return null;
        }

    }

    @Override
    public List<Leave> findAll() {
        try {
            return leaveRepository.findAll();

        } catch (Exception ex) {
            LOGGER.error("add leave   failed" + ex.getMessage());
            return null;
        }
    }

    @Override
    public Leave findById(String id) {
        try {
            return leaveRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.error("designation  failed" + ex.getMessage());
            return null;
        }
    }


    @Override
    public boolean findByName(String name) {
        try {
            Leave leave = leaveRepository.findByLeaveType(name);
            if (leave != null) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            LOGGER.error("check addLeave Failed: " + ex.getMessage());
            return false;
        }
    }
}




