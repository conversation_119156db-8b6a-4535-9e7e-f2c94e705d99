package lk.sout.loanManager.hr.service;

import lk.sout.core.entity.Response;
import lk.sout.loanManager.hr.entity.Collector;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface CollectorService {

    Response save(Collector driver,  List<MultipartFile> images);

    Iterable<Collector> findAll(Pageable pageable);

    Iterable<Collector> findAllActive(Pageable pageable);

    Iterable<Collector> findAllInactive(Pageable pageable);

    List<Collector> searchByName(String name);

    List<Collector> searchInactiveByName(String name);

    List<Collector> searchByNicNumber(String nicNumber);

    List<Collector> searchInactiveByNicNumber(String nicNumber);

    String delete(String id);

    Boolean checkNicNumberAvailability(String nicNumber);

    Boolean checkLicenseNoAvailability(String licenseNo);
}
