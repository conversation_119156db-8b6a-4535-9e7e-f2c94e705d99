package lk.sout.loanManager.hr.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 6/18/2018
 */
@Document @Component
public class SalaryScale{

    @Id
    private String id;
    private String name;
    private double basicSalary;
    private double epf;
    private double etf;
    private double livingAllowance;
    private double mealAllowance;
    private double vehicleAllowance;
    private double specialAllowance;
    private double housingAllowance;
    private double attendanceIncentive;
    private double noPayDeductionBasic;
    private double noPayDeductionEpf;
    private double otRatePerHour;
    private double budgetaryAllowance;
    private boolean active;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public double getBasicSalary() {
        return basicSalary;
    }

    public void setBasicSalary(double basicSalary) {
        this.basicSalary = basicSalary;
    }

    public double getEpf() {
        return epf;
    }

    public void setEpf(double epf) {
        this.epf = epf;
    }

    public double getEtf() {
        return etf;
    }

    public void setEtf(double etf) {
        this.etf = etf;
    }

    public double getLivingAllowance() {
        return livingAllowance;
    }

    public void setLivingAllowance(double livingAllowance) {
        this.livingAllowance = livingAllowance;
    }

    public double getMealAllowance() {
        return mealAllowance;
    }

    public void setMealAllowance(double mealAllowance) {
        this.mealAllowance = mealAllowance;
    }

    public double getVehicleAllowance() {
        return vehicleAllowance;
    }

    public void setVehicleAllowance(double vehicleAllowance) {
        this.vehicleAllowance = vehicleAllowance;
    }

    public double getSpecialAllowance() {
        return specialAllowance;
    }

    public void setSpecialAllowance(double specialAllowance) {
        this.specialAllowance = specialAllowance;
    }

    public double getHousingAllowance() {
        return housingAllowance;
    }

    public void setHousingAllowance(double housingAllowance) {
        this.housingAllowance = housingAllowance;
    }

    public double getAttendanceIncentive() {
        return attendanceIncentive;
    }

    public void setAttendanceIncentive(double attendanceIncentive) {
        this.attendanceIncentive = attendanceIncentive;
    }

    public double getNoPayDeductionBasic() {
        return noPayDeductionBasic;
    }

    public void setNoPayDeductionBasic(double noPayDeductionBasic) {
        this.noPayDeductionBasic = noPayDeductionBasic;
    }

    public double getNoPayDeductionEpf() {
        return noPayDeductionEpf;
    }

    public void setNoPayDeductionEpf(double noPayDeductionEpf) {
        this.noPayDeductionEpf = noPayDeductionEpf;
    }

    public double getOtRatePerHour() {
        return otRatePerHour;
    }

    public void setOtRatePerHour(double otRatePerHour) {
        this.otRatePerHour = otRatePerHour;
    }

    public double getBudgetaryAllowance() {
        return budgetaryAllowance;
    }

    public void setBudgetaryAllowance(double budgetaryAllowance) {
        this.budgetaryAllowance = budgetaryAllowance;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        active = active;
    }
}
