package lk.sout.core.service.impl;

import lk.sout.core.entity.WorkingSection;
import lk.sout.core.repository.WorkingSectionRepository;
import lk.sout.core.service.WorkingSectionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 6/18/2018
 */
@Service
public class WorkingSectionServiceImpl implements WorkingSectionService {
    final static Logger LOGGER = LoggerFactory.getLogger(WorkingSectionServiceImpl.class);

    @Autowired
    WorkingSectionRepository workingSectionRepository;

    @Override
    public  String save(WorkingSection workingSection){
        try{
            workingSectionRepository.save(workingSection);
            return "success";
        }catch (Exception ex){
            LOGGER.error("workingSection saving failed" + ex.getMessage());
            return ex.getMessage();
        }
    }

    @Override
    public List<WorkingSection> findAll(){
        try{
            return workingSectionRepository.findAll();
        }catch (Exception ex){
            LOGGER.error("workingSection retrieving failed" + ex.getMessage());
            return null;
        }
    }

    @Override
    public String remove(WorkingSection workingSection){
        try{
            workingSectionRepository.delete(workingSection);
            return "success";
        }catch (Exception ex){
            LOGGER.error("workingSection deleting failed" + ex.getMessage());
            return ex.getMessage();
        }
    }
}
