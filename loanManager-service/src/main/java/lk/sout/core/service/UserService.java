package lk.sout.core.service;

import lk.sout.core.entity.Module;
import lk.sout.core.entity.Permission;
import lk.sout.core.entity.Response;
import lk.sout.core.entity.User;

import java.util.List;
import java.util.Optional;

public interface UserService {

    boolean save(User user);

    List<User> findAll();

    User getCurrentUser();

    boolean isAdmin();

    String delete(String id);

    User findOne(String username);

    Optional<User> findById(String id);

    User search(String username);

    List<Permission> findAvailablePermissions(String userName);

    List<Permission> findDesktopPermissions(String userName);

    boolean saveDesktopPerms(String username, List<Permission> permission);

    User findLoggedInUser();

    List<Module> getEnabledModules();

    Module findModule(String moduleId);

    Permission findPermission(String permissionId);

    List<Permission> findPermissionsByModule(String moduleId);

    Boolean checkName(String username);

    Response update(User user);

    User searchByUsername(String username);

    Permission findPermissionsByName(String permName);

}
