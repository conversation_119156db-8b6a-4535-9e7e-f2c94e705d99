package lk.sout.core.service.impl;

import lk.sout.core.entity.Permission;
import lk.sout.core.repository.PermissionRepository;
import lk.sout.core.service.PermissionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PermissionServiceImpl implements PermissionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionServiceImpl.class);

    @Autowired
    PermissionRepository permissionRepository;

    @Override
    public boolean saveIfUnavailable(Permission permission) {
        try {
            Permission perm = permissionRepository.findByName(permission.getName());
            if (null == perm) {
                permissionRepository.save(permission);
            }
            return true;
        } catch (Exception ex) {
            LOGGER.error("Save Metadata failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public List<Permission> findAll() {
        try {
            return permissionRepository.findAll();
        } catch (Exception ex) {
            return null;
        }
    }

}
