package lk.sout.core.service;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Transaction;

import java.time.LocalDate;
import java.util.List;

public interface TransactionService {

    boolean save(Transaction transactions);

    Iterable<Transaction> findAll(org.springframework.data.domain.Pageable pageable);

    List<Transaction> findByType(MetaData type);

    List<Transaction> findByThirdParty(String thirdParty);

    List<Transaction> findByRangeFilterAndOperator(String rangeId, String operator);

    List<Transaction> findByDateRangeAndOperator(LocalDate startDate, LocalDate endDate, String operator);

    List<Transaction> findAllByDateBetween(LocalDate startDate, LocalDate endDate);

    Transaction findByRefNoAndRefType(String refNo, String refType);

    List<Transaction> findByRefNo(String refNo);

}
