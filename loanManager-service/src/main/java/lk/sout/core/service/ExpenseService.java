package lk.sout.core.service;

import lk.sout.core.entity.Expense;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

public interface ExpenseService {

    boolean save(Expense expense);

    Iterable<Expense> findAll(Pageable pageable);

    Iterable<Expense> findByExpenseCategory(String s, Pageable pageable);

    Iterable<Expense> findByExpenseType(String s, Pageable pageable);

    Iterable<Expense> findByDateBetween(LocalDate from, LocalDate to, Pageable pageable);

    Iterable<Expense> findByDateRangeFilter(String rangeId, Pageable pageable);

    Iterable<Expense> findByEmployee(String s, Pageable pageable);

    List<Expense> findByTypeAndDateBetween(String typeId, LocalDate from, LocalDate to);

    List<Expense> findByEmployeeAndDateBetween(String empId, LocalDate from, LocalDate to);

    Iterable<Expense> findByEmployeeAndType(String empId, String typeId, Pageable pageable);

    List<Expense> findByTypeAndEmployeeAndDateBetween(String typeId, String empId, LocalDate from, LocalDate to);
}
