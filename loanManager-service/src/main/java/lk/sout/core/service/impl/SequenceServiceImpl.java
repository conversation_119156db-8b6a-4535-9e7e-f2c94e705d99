package lk.sout.core.service.impl;


import lk.sout.core.entity.Sequence;
import lk.sout.core.repository.SequenceRepository;
import lk.sout.core.service.SequenceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
public class SequenceServiceImpl implements SequenceService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SequenceServiceImpl.class);

    @Autowired
    SequenceRepository sequenceRepository;

    @Override
    public boolean save(Sequence sequence) {
        try {
            sequenceRepository.save(sequence);
            LOGGER.info("Sequence  saved. " + sequence.getName());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving Sequence Failed. " + sequence.getName());
            return false;
        }
    }
    public Iterable<Sequence> findAll(Pageable pageable) {
        try {
            return sequenceRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Retrieving All brands failed");
            return null;
        }
    }

    @Override
    public Sequence findSequenceByName(String name) {
        return sequenceRepository.findSequenceByName(name);
    }

    public boolean incrementSequence(String name) {
        try {
            Sequence s = sequenceRepository.findSequenceByName(name);
            s.setCounter(s.getCounter() + 1);
            sequenceRepository.save(s);
            return true;
        } catch (Exception e) {
            LOGGER.info("Sequence incrementing failed. " + e.getMessage());
            return false;
        }
    }
}
