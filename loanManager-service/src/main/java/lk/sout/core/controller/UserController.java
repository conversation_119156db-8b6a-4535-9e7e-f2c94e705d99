package lk.sout.core.controller;

import lk.sout.core.entity.DesktopPerm;
import lk.sout.core.entity.Module;
import lk.sout.core.entity.Permission;
import lk.sout.core.entity.User;
import lk.sout.core.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public List<User> listUser() {
        return userService.findAll();
    }

    @RequestMapping(value = "/findAvailablePermissions", method = RequestMethod.GET)
    public List<Permission> findAvailablePermissions(@RequestParam String username) {
        return userService.findAvailablePermissions(username);
    }

    @RequestMapping(value = "/findDesktopPermissions", method = RequestMethod.GET)
    public List<Permission> findDesktopPermissions(@RequestParam String username) {
        return userService.findDesktopPermissions(username);
    }

    @RequestMapping(value = "/getModule", method = RequestMethod.GET)
    public Module getModule(@RequestParam String moduleId) {
        return userService.findModule(moduleId);
    }

    @RequestMapping(value = "/getPermission", method = RequestMethod.GET)
    public Permission getPermission(@RequestParam String permissionId) {
        return userService.findPermission(permissionId);
    }

    @RequestMapping(value = "/findPermissionByModule", method = RequestMethod.GET)
    public List<Permission> findPermissionsByModule(@RequestParam String moduleId) {
        return userService.findPermissionsByModule(moduleId);
    }

    @RequestMapping(value = "/getEnabledModules", method = RequestMethod.GET)
    public List<Module> getEnabledModules() {
        return userService.getEnabledModules();
    }

    @RequestMapping(value = "/saveDesktopPerms", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody DesktopPerm desktopPerm) {
        try {
            return ResponseEntity.ok(userService.saveDesktopPerms(desktopPerm.getUsername(), desktopPerm.getPermissions()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody User user) {
        try {
            return ResponseEntity.ok(userService.save(user));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/delete", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> delete(@RequestParam String id) {
        try {
            return ResponseEntity.ok(userService.delete(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


    @RequestMapping(value = "/checkForUserName", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> checkNameAvailability(@RequestParam("username") String username) {
        try {
            return ResponseEntity.ok(userService.checkName(username));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByUsername", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public User findByUsername(@RequestParam("username") String username) {
        return userService.findOne(username);
    }


    @RequestMapping(value = "/updateDesktopPerm", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> Update(@RequestBody User user) {
        try {
            return ResponseEntity.ok(userService.update(user));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


    @RequestMapping(value = "/searchByName", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public User searchByUsername(@RequestParam("username") String username) {
        return userService.searchByUsername(username);
    }

    @RequestMapping(value = "/getPermissionByName", method = RequestMethod.GET)
    public Permission findPermissionsByName(@RequestParam String permName) {
        return userService.findPermissionsByName(permName);
    }

}
