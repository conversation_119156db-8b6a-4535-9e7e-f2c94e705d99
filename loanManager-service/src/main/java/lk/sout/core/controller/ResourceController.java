package lk.sout.core.controller;

import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Created by <PERSON><PERSON><PERSON> on 10/3/2022
 */

@RestController
@RequestMapping(value = "/uploads")
public class ResourceController {

    @Value("${uploadPath}")
    private String uploadPath;

    @Value("${baseDir}")
    private String baseDir;

    @GetMapping(value = "/${sout.customer}/images/{folderName}/{imageName:.+}", produces = {MediaType.IMAGE_JPEG_VALUE,
            MediaType.IMAGE_GIF_VALUE, MediaType.IMAGE_PNG_VALUE})
    public @ResponseBody byte[] getImageWithMediaType(@PathVariable(name = "folderName") String folderName,
                                                      @PathVariable(name = "imageName") String fileName) throws IOException {
        String basePath = baseDir + uploadPath;
        Path destination = Paths.get(basePath + folderName + "/");
        Path filePath = destination.resolve(fileName);
        return IOUtils.toByteArray(filePath.toUri());
    }

}
