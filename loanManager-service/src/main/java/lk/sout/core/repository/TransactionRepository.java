package lk.sout.core.repository;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Transaction;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.time.LocalDateTime;
import java.util.List;

public interface TransactionRepository extends MongoRepository<Transaction, String> {

    List<Transaction> findAllByType(MetaData type);

    List<Transaction> findAllByThirdParty(String thirdParty);

    List<Transaction> findAllByRefNo(String no);

    List<Transaction> findByRefNoOrderByCreatedDate(String refNo);

    List<Transaction> findAllByDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    List<Transaction> findByOperatorAndDateBetween(String operator, LocalDateTime startDate, LocalDateTime endDate);

    Transaction findByRefNoAndRefType(String refNo, String refType);
}
