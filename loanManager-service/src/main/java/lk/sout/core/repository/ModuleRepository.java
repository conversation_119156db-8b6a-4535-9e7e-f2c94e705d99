package lk.sout.core.repository;


import lk.sout.core.entity.Module;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> We<PERSON> on 2/6/2019
 */

@Repository
public interface ModuleRepository extends MongoRepository<Module, String> {

    Module findByName(String name);

    List<Module> findAllByActivated(boolean activated);

    Module findByNameAndActivated(String name, Boolean status);
}
