package lk.sout.event;

import lk.sout.config.CascadeSave;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.List;

public class CascadeCallback implements ReflectionUtils.FieldCallback {

    private Object source;
    private MongoOperations mongoOperations;

    CascadeCallback(final Object source, final MongoOperations mongoOperations) {
        this.source = source;
        this.setMongoOperations(mongoOperations);
    }

    @Override
    public void doWith(final Field field) throws IllegalArgumentException, IllegalAccessException {
        ReflectionUtils.makeAccessible(field);

        if (field.isAnnotationPresent(DBRef.class) && field.isAnnotationPresent(CascadeSave.class)) {

            if (field.get(getSource()) instanceof List<?>) {
                final List<Object> objects = (List<Object>) field.get(getSource());
                for (Object obj : objects) {
                    if (obj != null) {
                        final FieldCallback callback = new FieldCallback();
                        ReflectionUtils.doWithFields(obj.getClass(), callback);
                        getMongoOperations().save(obj);
                    }
                }
            } else {
                final Object fieldValue = field.get(getSource());
                if (fieldValue != null) {
                    final FieldCallback callback = new FieldCallback();
                    ReflectionUtils.doWithFields(fieldValue.getClass(), callback);
                    getMongoOperations().save(fieldValue);
                }
            }
        }
    }

    private Object getSource() {
        return source;
    }

    public void setSource(final Object source) {
        this.source = source;
    }

    private MongoOperations getMongoOperations() {
        return mongoOperations;
    }

    private void setMongoOperations(final MongoOperations mongoOperations) {
        this.mongoOperations = mongoOperations;
    }
}
