package lk.sout.config;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;
import lk.sout.core.entity.LogEntry;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.time.LocalDateTime;

public class MongoDBLogAppender extends AppenderBase<ILoggingEvent> {

    private final MongoTemplate mongoTemplate;

    public MongoDBLogAppender(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    protected void append(ILoggingEvent event) {
        LogEntry logEntry = new LogEntry();
        logEntry.setTimestamp(LocalDateTime.now());
        logEntry.setLevel(event.getLevel().toString());
        logEntry.setLogger(event.getLoggerName());
        logEntry.setMessage(event.getFormattedMessage());
        logEntry.setThreadName(event.getThreadName());

        try {
            mongoTemplate.save(logEntry);
        } catch (Exception e) {
            System.err.println("Failed to save log entry: " + e.getMessage());
        }
    }
}
