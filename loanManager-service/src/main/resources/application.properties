specs.dir=/specs/

#sout.customer=Eh
#sout.customer=Demo
#sout.customer=Eh2
sout.customer=Rewin

#mongodb
spring.data.mongodb.host=localhost
#spring.data.mongodb.host=**************
spring.data.mongodb.port=27017
spring.data.mongodb.database=loanManager${sout.customer}
spring.data.mongodb.authDatabase=loanManager${sout.customer}
spring.data.mongodb.username=loanManager
spring.data.mongodb.password=awer@#$cdfDDF!@S_+(

spring.main.allow-circular-references=true

#logging
logging.level.root=info
logging.pattern.console=%d{dd-MM-yyyy HH:mm:ss.SSS} %magenta([%thread]) %highlight(%-5level) %logger.%M - %msg%n
logging.file.path=logs
logging.pattern.file=%d{dd-MM-yyyy HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%M - %msg%n
#logging.level.org.springframework.data=DEBUG
#logging.level.org.apache.http=DEBUG
#logging.level.org.apache.http.wire=DEBUG
#org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG
location.company.logo=../webapps/uploads/image/company/

# MULTIPART (MultipartProperties)
# Enable multipart uploads
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.max-file-size=200MB
spring.servlet.multipart.max-request-size=215MB
spring.jackson.time-zone=Asia/Colombo

dateFormat=MM/dd/yyyy
dateTimeFormat=MM/dd/yyyy HH:mm:ss

baseDir=/opt/tomcat/
uploadPath=uploads/${sout.customer}/images/

#SMS
#sms.enable=true
#sms.api.url=http://app.newsletters.lk/smsAPI
#sms.api.key=suCMXPobcxYVR9wsZkmAq4r2vPNue40S
#sms.api.token=6ACk1666108211
#sms.sender.id=PRM
#sms.type=sms
#sms.country.code=94
#sms.sendsms=sendsms
#sms.groupstatus=groupstatus

#Schedule
loan.cron.manageOverPaidAmnt=0 10 0 * * *
loan.cron.generateLoanRecord=0 30 2 * * *
loan.cron.createArrearsRecords=0 40 2 * * *
loan.cron.setLoanAsArrears=0 50 2 * * *
loan.cron.updateArrearsRecords=0 0 3 * * *
loan.cron.moveOldRecords=0 30 3 * * *

loan.cron.timezone=Asia/Colombo
