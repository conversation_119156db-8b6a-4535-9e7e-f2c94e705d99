import {Component, Input, OnInit} from '@angular/core';
import {AlertController, IonModal, LoadingController, ViewWillEnter} from "@ionic/angular";
import {LoanRecordService} from "../service/loanRecord.service";
import {LoanService} from "../service/loan.service";

@Component({
  selector: 'app-history',
  templateUrl: './pay-loan.component.html',
  styleUrls: ['./pay-loan.component.scss']
})
export class PayLoanComponent implements OnInit, ViewWillEnter {

  @Input() loanNo: string = "";
  @Input() borrowerName: string = "";
  @Input() loanRecordId: string = "";
  @Input() installment: string = "";
  @Input() installmentDate: string = "";
  @Input() outOfOrderPayment: boolean = false;

  payment: number = 0;

  modal: IonModal | undefined;
  disable = false;

  loading: any;

  constructor(private loanRecordService: LoanRecordService, private alertCtrl: AlertController,
              private loadingCtrl: LoadingController, private loanService: LoanService) {
  }

  ngOnInit() {

  }

  ionViewWillEnter(): void {
    this.clear();
  }

  closeModal() {
    this.modal?.dismiss();
  }

  savePayment() {
    if (this.payment <= 0) {
      this.showAlert("කරුණාකර නිවැරදිව ගෙවීමක් ඇතුල් කරන්න");
      return;
    }
    this.showLoading();
    this.disable = true;
    if(this.outOfOrderPayment){
      this.loanRecordService.createAndPay(this.loanNo, this.payment).subscribe((data: any) => {
        if (data.code === 200) {
          this.loading.dismiss();
          this.showAlert(data.message);
          this.closeModal();
        } else {
          this.loading.dismiss();
          this.showAlert(data.message);
          this.disable = false;
        }
      });
    }else {
      this.loanRecordService.pay(this.loanRecordId, this.payment).subscribe((data: any) => {
        if (data.code === 200) {
          this.loading.dismiss();
          this.showAlert(data.message);
          this.closeModal();
        } else {
          this.loading.dismiss();
          this.showAlert(data.message);
          this.disable = false;
        }
      });
    }
    this.outOfOrderPayment = false;
  }

  async showLoading() {
    this.loading = await this.loadingCtrl.create({
      message: 'Loading...',
      spinner: 'bubbles',
      backdropDismiss: true
    });
    await this.loading.present();
  }

  async showAlert(message: string) {
    const alert = await this.alertCtrl.create({
      header: 'දැන්වීම',
      message: message,
      buttons: ['වසන්න']
    });
    await alert.present();
  }

  clear() {
    this.payment = 0;
    this.disable = false;
  }

}
