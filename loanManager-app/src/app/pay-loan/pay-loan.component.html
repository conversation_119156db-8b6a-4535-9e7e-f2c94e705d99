<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>
      <h3>SmartLoan</h3>
    </ion-title>
    <ion-title slot="end">ගෙවීම්</ion-title>
    <ion-button slot="end" fill="none" color="danger" size="medium" (click)="closeModal()">X</ion-button>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">ගෙවීම්</ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-list class="ion-justify-content-center" lines="none">
    <ion-item>
      <ion-label>වාරික දිනය</ion-label>
      <ion-text>{{installmentDate}}</ion-text>
    </ion-item>
    <ion-item>
      <ion-label>ණය ගනුම්කාරු</ion-label>
      <ion-text>{{borrowerName}}</ion-text>
    </ion-item>
    <ion-item>
      <ion-label>ණය මුදල</ion-label>
      <ion-text>{{loanNo}}</ion-text>
    </ion-item>
    <ion-item>
      <ion-label>වාරිකය</ion-label>
      <ion-text>{{installment}}</ion-text>
    </ion-item>
    <ion-item>
      <ion-input type="number" label="ගෙවන මුදල" required [(ngModel)]="payment"></ion-input>
    </ion-item>
    <ion-item>
      <ion-button slot="end" [disabled]="disable" size="medium" (click)="savePayment()">ගෙවන්න</ion-button>
    </ion-item>
  </ion-list>

</ion-content>
