import {Component, OnInit} from '@angular/core';
import {Alert<PERSON>ontroller, IonModal, LoadingController, ModalController, ViewWillEnter} from "@ionic/angular";
import {LoanRecord} from "../model/loanRecord";
import {LoanRecordService} from "../service/loanRecord.service";
import {Loan} from "../model/loan";
import {LoanService} from "../service/loan.service";
import {PayLoanComponent} from "../pay-loan/pay-loan.component";

@Component({
  selector: 'app-history',
  templateUrl: './loan-details.component.html',
  styleUrls: ['./loan-details.component.scss']
})
export class LoanDetailsComponent implements OnInit, ViewWillEnter {

  loan: Loan = new Loan();
  loanRecords: Array<LoanRecord> = [];

  loanNo: string = "";
  modal: IonModal | undefined;

  totalPayment = 0.0;

  constructor(private alertCtrl: AlertController, private loanRecordService: LoanRecordService,
              private loanService: LoanService, private modalController: ModalController,
              private loadingCtrl: LoadingController) {
  }

  ngOnInit() {
  }

  ionViewWillEnter() {
    this.clear();
    this.openLoan();
  }

  findPendingLoanRecords() {
    this.loanRecordService.findPendingRecordsByLoanNo(this.loan.loanNo).subscribe((data: Array<LoanRecord>) => {
      if (data) {
        this.loanRecords = data;
      }
    });
  }

  async pay(loanNo: any, borrowerName: any, loanRecordId: any, installment: any) {
    const modal = await this.modalController.create({
      component: PayLoanComponent,
      componentProps: {loanNo, borrowerName, loanRecordId, installment}
    });
    modal.onDidDismiss().then((data) => {
      this.findPendingLoanRecords();
    });
    return await modal.present();
  }

  clear() {
    this.loan = new Loan();
    this.clearRecords();
    this.loadingCtrl.dismiss();
  }

  clearRecords() {
    this.loanRecords = [];
    this.loadingCtrl.dismiss();
  }

  async showLoading() {
    const loading = await this.loadingCtrl.create({
      message: 'Loading...',
      spinner: 'bubbles',
      backdropDismiss: true
    });
    await loading.present();
  }

  async showAlert(message: string) {
    const alert = await this.alertCtrl.create({
      header: 'දැන්වීම',
      message: message,
      buttons: ['වසන්න']
    });
    await alert.present();
  }

  openLoan() {
    this.showLoading();
    this.loanService.findByLoanNo(this.loanNo).subscribe((data: any) => {
      if (data) {
        this.loan = data;
        this.findPendingLoanRecords();
        this.loadingCtrl.dismiss();
      } else {
        this.showAlert("ණය ලබා දීම හමු නොවිය");
        this.clear();
      }
    });
  }

  calculateTotal(){
    this.totalPayment = 0.0;
    for(let rec of this.loanRecords){
      // @ts-ignore
      this.totalPayment = totalPayment + rec.paidAmount;
    }
  }

  closeModal() {
    this.modal?.dismiss();
  }

}
