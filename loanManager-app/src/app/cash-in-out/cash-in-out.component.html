<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>
      <h3>SmartLoan</h3>
    </ion-title>
    <ion-title slot="end">මුදල් පොත</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">මුදල් පොත</ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-list class="ion-justify-content-center">
    <ion-item>
      <ion-select label="වියදම්" [(ngModel)]="ledgerRecord.reason">
        <ion-select-option *ngFor="let expense of expenses" [value]="expense">{{ expense }}</ion-select-option>
      </ion-select>
    </ion-item>
    <ion-item>
      <ion-label>සටහන</ion-label>
      <ion-input [(ngModel)]="ledgerRecord.remark"></ion-input>
    </ion-item>
    <ion-item>
      <ion-label>මුදල</ion-label>
      <ion-input type="number" [(ngModel)]="ledgerRecord.amount"></ion-input>
    </ion-item>
    <ion-item>
      <ion-button slot="end" (click)="saveCashOut()">සුරකින්න</ion-button>
    </ion-item>
  </ion-list>
</ion-content>
