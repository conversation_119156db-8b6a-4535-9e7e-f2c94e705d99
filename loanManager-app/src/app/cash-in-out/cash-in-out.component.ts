import {Component, OnInit} from '@angular/core';
import {AlertController, ViewWillEnter} from "@ionic/angular";
import {LedgerRecordService} from "../service/ledgerRecord.service";
import {LedgerRecord} from "../model/ledgerRecord";
import {environment} from "../../environments/environment";

@Component({
  selector: 'app-history',
  templateUrl: './cash-in-out.component.html',
  styleUrls: ['./cash-in-out.component.scss']
})
export class CashInOutComponent implements OnInit, ViewWillEnter {

  expenses: Array<Object> = [];

  ledgerRecord: LedgerRecord = new LedgerRecord();
  constructor(private ledgerRecordService: LedgerRecordService, private alertCtrl: AlertController) {
  }

  ngOnInit() {
    this.expenses = ["Salary", "Fuel", "Internet", "Bank", "Stationary", "Other"];
  }

  ionViewWillEnter() {
    this.clear();
  }

  saveCashOut() {
    if (this.ledgerRecord.reason == "") {
      this.showAlert("කරුණාකර විකල්පයක් තෝරන්න");
    } else {
      this.ledgerRecord.appNo = environment.appNo;
      this.ledgerRecord.operation = "-";
      this.ledgerRecord.loanNo = "N/A";
      this.ledgerRecord.borrowerName = "N/A";
      this.ledgerRecordService.save(this.ledgerRecord).subscribe((data: any) => {
        if (data === true) {
          this.showAlert("වියදම් සාර්ථකව සුරැකුනි");
          this.clear();
        } else {
          this.showAlert("වියදම් සුරැකුකීම සිදු නොවීය");
        }
      });
    }
  }

  clear() {
    this.ledgerRecord = new LedgerRecord();
  }

  async showAlert(message: string) {
    const alert = await this.alertCtrl.create({
      header: 'දැන්වීම',
      message: message,
      buttons: ['වසන්න']
    });
    await alert.present();
  }

}
