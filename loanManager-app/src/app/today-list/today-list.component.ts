import {Component, OnInit} from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LoadingController, ModalController, ViewWillEnter} from "@ionic/angular";
import {LoanRecord} from "../model/loanRecord";
import {LoanRecordService} from "../service/loanRecord.service";
import {PayLoanComponent} from "../pay-loan/pay-loan.component";
import {LoanDetailsComponent} from "../loan-details/loan-details.component";
import {GroupedLoanRecord} from "../model/groupedLoanRecord";

@Component({
  selector: 'app-profile',
  templateUrl: './today-list.component.html',
  styleUrls: ['./today-list.component.scss']
})
export class TodayListComponent implements OnInit, ViewWillEnter {

  loanRecordGroup: Array<GroupedLoanRecord> = [];
  loanRecords: Array<LoanRecord> = [];

  keyName: string = "";
  tempNic: string | undefined = "";
  tapCount: number = 0

  loading: any;

  constructor(private loanRecordService: LoanRecordService, private alertCtrl: AlertController,
              private modalController: ModalController, private loadingCtrl: LoadingController) {
  }

  async ngOnInit() {

  }

  async ionViewWillEnter() {
    this.findTodayList();
  }

  findTodayList() {
    this.loanRecordService.findTodayList().subscribe((data: any) => {
      if (data != null) {
        this.loanRecordGroup = data;
      }
    });
  }

  findLoanRecord(event: any, nic: string | undefined) {
    this.loanRecords = [];
    this.tapCount++;
    if (this.tempNic == nic && this.tapCount == 2) {
      this.loanRecords = [];
      this.tapCount = 0;
      this.tempNic = "";
      return;
    }
    if (this.tempNic != nic) {
      this.tempNic = nic;
      this.showLoading();
      if (nic != undefined) {
        this.loanRecordService.findTodayRecordsByNic(nic).subscribe({
          next: (data: any) => {
            if (data != null) {
              this.loanRecords = data;
            }
          },
          complete: () => {
            this.loadingCtrl.dismiss();
          },
          error: () => {
            this.loadingCtrl.dismiss();
          }
        });
      }
    }
  }

  searchByName() {
    this.loanRecordService.findTodayLoanByName(this.keyName).subscribe((data: any) => {
      if (data != null) {
        this.loanRecordGroup = data;
      }
    });
  }

  manageList(childIndex: number, parentIndex: number) {
    this.loanRecords.splice(childIndex, 1);
    if (this.loanRecords.length == 0) {
      this.loanRecordGroup.splice(parentIndex, 1);
    }
  }

  async pay(loanNo: any, borrowerName: any, loanRecordId: any, installment: any, installmentDate: any,
  childIndex: number, parentIndex: number) {
    const modal = await this.modalController.create({
      component: PayLoanComponent,
      componentProps: {loanNo, borrowerName, loanRecordId, installment, installmentDate}
    });
    modal.onDidDismiss().then((data) => {
      this.manageList(childIndex, parentIndex);
    });
    return await modal.present();
  }

  async openLoan(loanNo: any) {
    const modal = await this.modalController.create({
      component: LoanDetailsComponent,
      componentProps: {loanNo}
    });
    modal.onDidDismiss().then((data) => {
      //this.findTodayList();
    });
    return await modal.present();
  }

  async showMessage() {
    const alert = await this.alertCtrl.create({
      header: 'දැන්වීම',
      message: 'කරුණාකර මෙම පෝරමය පුරවන්න'
    });
    await alert.present();
  }

  async showAlert() {
    const alert = await this.alertCtrl.create({
      header: 'දැන්වීම',
      message: "සාර්ථකව සුරකුනි",
      buttons: ['වසන්න']
    });
    await alert.present();
  }

  async showLoading() {
    this.loading = await this.loadingCtrl.create({
      message: 'Loading...',
      spinner: 'bubbles',
      backdropDismiss: true
    });
    await this.loading.present();
  }

}
