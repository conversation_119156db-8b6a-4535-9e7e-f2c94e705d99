<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>
      <h3>SmartLoan</h3>
    </ion-title>
    <ion-title slot="end">අද දින වාරික</ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="findTodayList()">
        <ion-icon slot="icon-only" name="reload-outline" size="" color="primary"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">අද දින වාරික</ion-title>
    </ion-toolbar>
  </ion-header>
  <ion-item class="custom-item">
    <ion-input label="නම" [(ngModel)]="keyName">
      <ion-button slot="end" (click)="searchByName()">
        <ion-icon name="search-outline"></ion-icon>
      </ion-button>
    </ion-input>
  </ion-item>

  <ion-accordion-group [animated]="true">
    <ion-accordion *ngFor="let record of loanRecordGroup; let j = index">
      <ion-item slot="header" color="light" (click)="findLoanRecord($event, record.borrowerNic)">
        <ion-label>{{ j + 1 + ". " + record.borrowerName }}</ion-label>
      </ion-item>
      <div slot="content" *ngFor="let loanRecord of loanRecords; let i = index">
        <ion-grid lines="none">
          <ion-row *ngIf="i == 0">
            <ion-col size="8">
              <ion-label style="font-weight: normal; font-size: small;"
                         class="ion-no-margin">{{ loanRecord.borrowerAddress }}
              </ion-label>
            </ion-col>
            <ion-col size="4" class="ion-text-end">
              <ion-list>
                <ion-note><a [href]="'දු.ක:'+loanRecord.borrowerTp1">{{ loanRecord.borrowerTp1 }}</a></ion-note>
                <br>
                <ion-note><a [href]="'දු.ක:'+loanRecord.borrowerTp2">{{ loanRecord.borrowerTp2 }}</a></ion-note>
              </ion-list>
            </ion-col>
          </ion-row>
        </ion-grid>
        <ion-item-group [ngClass]="i > 0 ? 'margin-top' : ''">
          <ion-item lines="none">
            <ion-label class="ion-no-margin">ණය සැලැස්ම</ion-label>
            <ion-text>{{ loanRecord.loanPlan }}</ion-text>
          </ion-item>
          <ion-item lines="none">
            <ion-label class="ion-no-margin">වාරිකය</ion-label>
            <ion-text>{{ loanRecord.installmentAmount | number: '1.2-2' }}</ion-text>
          </ion-item>
          <ion-item lines="none">
            <ion-label class="ion-no-margin">ශේෂය</ion-label>
            <ion-text>{{ loanRecord.balance | number: '1.2-2' }}</ion-text>
          </ion-item>
        </ion-item-group>
        <ion-row class="ion-float-end">
          <ion-button class="" size="medium" (click)="openLoan(loanRecord.loanNo)">විස්තර</ion-button>
          <ion-button color="success" size="medium"
                      (click)="pay(loanRecord.loanNo,loanRecord.borrowerName,
                      loanRecord.id,loanRecord.installmentAmount,loanRecord.installmentDate,i,j)">
            ගෙවන්න
          </ion-button>
        </ion-row>
        <ion-item-divider></ion-item-divider>
      </div>
    </ion-accordion>
  </ion-accordion-group>

</ion-content>
