import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {AppConstant} from '../app.constant';
import {<PERSON>rrow<PERSON>} from "../model/borrower";

@Injectable({
  providedIn: 'root'
})
export class BorrowerService {

  constructor(private http: HttpClient) {
  }

  public save(borrower: <PERSON>rrow<PERSON>): any {
    return this.http.post(AppConstant.SAVE_BORROWER, borrower);
  }

  public findByMobile(tp: string): any {
    return this.http.get(AppConstant.FIND_BORROWER_BY_MOBILE, {params: {tp}});
  }

  public findByNic(nic: string): any {
    return this.http.get(AppConstant.FIND_BORROWER_BY_NIC, {params: {nic}});
  }

  public findByNameLike(name: string): any {
    return this.http.get(AppConstant.FIND_BORROWER_BY_NAME_LIKE, {params: {name}});
  }

  public findByTpLike(tp: string): any {
    return this.http.get(AppConstant.FIND_BORROWER_BY_TP_LIKE, {params: {tp}});
  }

}
