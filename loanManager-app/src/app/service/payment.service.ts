import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {AppConstant} from '../app.constant';

@Injectable({
  providedIn: 'root'
})
export class PaymentService {

  constructor(private http: HttpClient) {
  }

  public findTodayPayments(): any {
    return this.http.get(AppConstant.FIND_TODAY_PAYMENT_LIST);
  }

  public findPaymentsByDate(date: string): any {
    return this.http.get(AppConstant.FIND_PAYMENTS_BY_DATE, {params: {date: date}});
  }

}
