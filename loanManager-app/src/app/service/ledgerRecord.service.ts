import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {AppConstant} from '../app.constant';
import {LedgerRecord} from "../model/ledgerRecord";

@Injectable({
  providedIn: 'root'
})

export class LedgerRecordService {

  constructor(private http: HttpClient) {
  }

  public save(record: LedgerRecord): any {
    return this.http.post(AppConstant.SAVE_LEDGER_RECORD, record);
  }

}
