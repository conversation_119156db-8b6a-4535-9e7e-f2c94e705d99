<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>
      <h3>SmartLoan</h3>
    </ion-title>
    <ion-title slot="end">ගෙවීම් ලැයිස්තුව</ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="findTodayPayments()">
        <ion-icon slot="icon-only" name="reload-outline" size="" color="primary"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">ගෙවීම් ලැයිස්තුව</ion-title>
    </ion-toolbar>
  </ion-header>
  <ion-item lines="none">
    <ion-label>දිනය මගින් සොයන්න</ion-label>
    <ion-datetime-button name="issueDate" label="නිකුත් කළ දිනය" required datetime="datePicker"></ion-datetime-button>
  </ion-item>
  <ion-item>
    <ion-label class="ion-no-margin">මුළු මුදල</ion-label>
    <ion-text>{{totalAmount | number: '1.2-2'}}</ion-text>
  </ion-item>
  <ion-modal [keepContentsMounted]="true">
    <ng-template>
      <ion-datetime name="dateTime" presentation="date" id="datePicker" [(ngModel)]="dateSelected"
                    (ionChange)="findPaymentByDate()"></ion-datetime>
    </ng-template>
  </ion-modal>
  <ion-list class="ion-justify-content-center" *ngFor="let payment of payments; let i = index" lines="none"
            [inset]="true">
    <ion-grid class="ion-no-margin">
      <ion-row>
        <ion-col size="9">
          <ion-label class="ion-no-margin">{{i + 1 + ". " + payment.borrowerName}}</ion-label>
        </ion-col>
        <ion-col size="3" class="ion-text-end">
          <ion-list>
            <ion-note><a [href]="'දු.ක:'+payment.borrowerTp">{{payment.borrowerTp}}</a></ion-note>
          </ion-list>
        </ion-col>
      </ion-row>
    </ion-grid>
    <ion-item>
      <ion-label class="ion-no-margin">දිනය වේලාව</ion-label>
      <ion-text>{{payment.dateTime | date: 'yyyy-MM-dd HH:mm:ss' }}</ion-text>
    </ion-item>
    <ion-item>
      <ion-label class="ion-no-margin">ණය සැලැස්ම</ion-label>
      <ion-text>{{payment.loanPlan}}</ion-text>
    </ion-item>
    <ion-item>
      <ion-label class="ion-no-margin">මාර්ගය</ion-label>
      <ion-text>{{payment.routeName}}</ion-text>
    </ion-item>
    <ion-item>
      <ion-label class="ion-no-margin">ගෙවීම</ion-label>
      <ion-text>{{payment.amount | number: '1.2-2'}}</ion-text>
    </ion-item>
  </ion-list>

</ion-content>
