import {Component} from '@angular/core';
import {Modal<PERSON>ontroller, ViewWillEnter} from "@ionic/angular";
import {PaymentService} from "../service/payment.service";
import {Payment} from "../model/payment";
import {DatePipe} from "@angular/common";

@Component({
  selector: 'app-profile',
  templateUrl: './payment-list.component.html',
  styleUrls: ['./payment-list.component.scss']
})
export class PaymentListComponent implements  ViewWillEnter {

  payments: Array<Payment> = [];
  dateSelected: string;
  keyName: string = "";
  totalAmount = 0;

  constructor(private paymentService: PaymentService, private datePipe: DatePipe,
              private modalController: ModalController) {
    this.dateSelected = new Date().toISOString();
  }

  async ionViewWillEnter() {
    this.findTodayPayments();
  }

  findTodayPayments() {
    this.payments = [];
    this.paymentService.findTodayPayments().subscribe((data: any) => {
      if (data != null) {
        this.payments = data;
        this.calcTotalAmount();
      }
    });
  }

  findPaymentByDate() {
    this.payments = [];
    this.modalController.dismiss();
    this.paymentService.findPaymentsByDate(this.datePipe.transform(this.dateSelected, 'yyyy-MM-dd')!.toString()).subscribe((data: any) => {
      if (data != null) {
        this.payments = data;
        this.calcTotalAmount();
      }
    });
  }

  calcTotalAmount() {
    this.totalAmount = 0;
    for (let payment of this.payments) {
      this.totalAmount = this.totalAmount + payment.amount;
    }
  }

}
