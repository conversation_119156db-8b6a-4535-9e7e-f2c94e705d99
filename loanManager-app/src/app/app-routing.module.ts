import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import {NewLoanComponent} from "./new-loan/new-loan.component";
import {TodayListComponent} from "./today-list/today-list.component";
import {ArrearsListComponent} from "./arrears-list/arrears-list.component";
import {PaymentListComponent} from "./payment-list/payment-list.component";
import {NewBorrowerComponent} from "./new-borrower/new-borrower.component";
import {LoanLookupComponent} from "./loan-lookup/loan-lookup.component";
import {PayLoanComponent} from "./pay-loan/pay-loan.component";
import {ProfileComponent} from "./profile/profile.component";
import {HomeComponent} from "./home/<USER>";
import {CashInOutComponent} from "./cash-in-out/cash-in-out.component";

const routes: Routes = [
  {
    path: '',
    redirectTo: 'today-list',
    pathMatch: 'full',
  },
  {
    path: 'new-loan',
    component: NewLoanComponent
  },
  {
    path: 'today-list',
    component: TodayListComponent
  },
  {
    path: 'arrears-list',
    component: ArrearsListComponent
  },
  {
    path: 'payment-list',
    component: PaymentListComponent
  },
  {
    path: 'borrower',
    component: NewBorrowerComponent
  },
  {
    path: 'borrower-lookup',
    component: LoanLookupComponent
  },
  {
    path: 'pay-loan',
    component: PayLoanComponent
  },
  {
    path: 'profile',
    component: ProfileComponent
  },
  {
    path: 'cash-in-out',
    component: CashInOutComponent
  },
  {
    path: 'home',
    component: HomeComponent
  }
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule {}
