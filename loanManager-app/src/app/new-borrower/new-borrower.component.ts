import {Component, OnInit} from '@angular/core';
import {<PERSON>rrow<PERSON>} from "../model/borrower";
import {BorrowerService} from "../service/borrower.service";
import {Route} from "../model/route";
import {RouteService} from "../service/route.service";
import {<PERSON><PERSON><PERSON><PERSON>roller, IonModal, ViewWillEnter} from "@ionic/angular";

@Component({
  selector: 'app-history',
  templateUrl: './new-borrower.component.html',
  styleUrls: ['./new-borrower.component.scss']
})
export class NewBorrowerComponent implements OnInit, ViewWillEnter {

  borrower: Borrower = new Borrower();
  borrowers: Array<Borrower> = [];

  routeList: Array<Route> = [];
  modal: IonModal | undefined;
  submitDisabled = false;

  isItemAvailable = false;
  items: Array<String> = [];

  constructor(private borrowerService: BorrowerService, private routeService: RouteService,
              private alertCtrl: AlertController) {
  }

  ngOnInit() {

  }

  ionViewWillEnter() {
    this.loadRoutes();
    this.submitDisabled = false;
  }

  loadRoutes() {
    this.routeService.findAllForSelect().subscribe((data: Array<Route>) => {
      return this.routeList = data;
    });
  }

  closeModal() {
    this.modal?.dismiss();
  }

  setBorrower() {
    this.modal?.dismiss(this.borrower);
  }

  setSelectedBorrower(borrower: Borrower) {
    this.borrower = borrower;
    this.borrowers = [];
  }

  searchBorrower() {
    if (this.borrower.name.length > 2) {
      this.borrowerService.findByNameLike(this.borrower.name).subscribe((data: any) => {
        if (data != null) {
          this.borrowers = data;
        } else {
          this.showAlert("ණය ගැනුම්කරු සොයාගත නොහැකි ය");
        }
      });
    }
  }

  save() {
    this.submitDisabled = true;
    this.borrowerService.save(this.borrower).subscribe((data: any) => {
      if (data.code === 200) {
        this.borrower.id = data.data;
        this.showAlert(data.message);
        this.setBorrower();
        this.submitDisabled = false;
      } else if (data.code === 300) {
        this.showAlert(data.message);
        this.borrower.id = data.data;
      } else {
        this.showAlert(data.message);
        this.submitDisabled = false;
      }
    });
  }

  async showAlert(message: string) {
    const alert = await this.alertCtrl.create({
      header: 'දැන්වීම',
      message: message,
      buttons: ['වසන්න']
    });
    await alert.present();
  }

  clear() {
    this.borrower = new Borrower();
    this.submitDisabled = false;
  }

}
