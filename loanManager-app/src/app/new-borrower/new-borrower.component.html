<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>
      <h3>SmartLoan</h3>
    </ion-title>
    <ion-title slot="end">නව ණය ගැනුම්කරු</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">නව ණය ගැනුම්කරු</ion-title>
    </ion-toolbar>
  </ion-header>
  <form #borrowerForm="ngForm">
    <ion-list class="ion-justify-content-center">
      <ion-item>
        <ion-input name="telephoneNo1" [(ngModel)]="borrower.telephone1" pattern="^\d{10}$"
                   label="ප්‍රධාන දුරකථන අංකය" required>
          <ion-button (click)="searchBorrower()" slot="end">
            <ion-icon name="search-outline"></ion-icon>
          </ion-button>
        </ion-input>
      </ion-item>
      <ion-item>
        <ion-input name="nic" [(ngModel)]="borrower.nic" required label="හඳුනම්පත් අංකය"
                   pattern="([0-9]{9}[x|X|v|V]|[0-9]{12})$"></ion-input>
      </ion-item>
      <ion-item>
        <ion-input name="name" label="නම" required [(ngModel)]="borrower.name">
          <ion-button (click)="searchBorrower()" slot="end">
            <ion-icon name="search-outline"></ion-icon>
          </ion-button>
        </ion-input>
      </ion-item>
      <ion-list *ngIf="borrowers.length > 0" class="ion-padding">
        <ion-item *ngFor="let br of borrowers;let i = index" (click)="setSelectedBorrower(br)">
          <ion-label>{{(i + 1) + '. ' + br.name}}</ion-label>
          <ion-label>{{br.address}}</ion-label>
        </ion-item>
      </ion-list>
      <ion-item>
        <ion-input name="address" label="ලිපිනය" required [(ngModel)]="borrower.address"></ion-input>
      </ion-item>
      <ion-item>
        <ion-input name="telephone2" [(ngModel)]="borrower.telephone2" pattern="^\d{10}$"
                   label="අමතර දුරකථන අංකය"></ion-input>
      </ion-item>
      <ion-item>
        <ion-select name="route" label="මාර්ගය" [(ngModel)]="borrower.route" required>
          <ion-select-option *ngFor="let route of routeList" [value]="route">
            {{route.name}}
          </ion-select-option>
        </ion-select>
      </ion-item>
    </ion-list>
    <ion-row>
      <ion-col size="12">
        <ion-button expand="block" (click)="setBorrower()">ණය ගැනුම්කරු තෝරන්න</ion-button>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col size="4">
        <ion-button expand="block" (click)="save()" [disabled]="!borrowerForm.form.valid || submitDisabled">
          සුරකින්න
        </ion-button>
      </ion-col>
      <ion-col size="4">
        <ion-button expand="block" (click)="closeModal()">
          ඉවත් වන්න
        </ion-button>
      </ion-col>
      <ion-col size="4">
        <ion-button expand="block" (click)="clear()">
          හිස් කරන්න
        </ion-button>
      </ion-col>
    </ion-row>
  </form>
</ion-content>
