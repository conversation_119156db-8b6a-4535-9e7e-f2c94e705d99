import {Component, OnInit} from '@angular/core';
import {LoanService} from "../service/loan.service";
import {DatePipe} from "@angular/common";
import {Router} from "@angular/router";
import {Storage} from "@ionic/storage-angular";
import {Alert<PERSON>ontroller, LoadingController} from "@ionic/angular";

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {
  constructor(private bookingService: LoanService, private datePipe: DatePipe,
              private storage: Storage, private router: Router,
              private alertCtrl: AlertController, private loadingCtrl: LoadingController) {

  }

  async ngOnInit() {
    await this.storage.create();
  }

}
