<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>
      <h3>SmartLoan</h3>
    </ion-title>
    <ion-title slot="end">User Login</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">User</ion-title>
    </ion-toolbar>
  </ion-header>
  <ion-list>
    <ion-item>
      <ion-input label="User Name" required [(ngModel)]="user.username"></ion-input>
    </ion-item>
    <ion-item>
      <ion-input label="Password" required [(ngModel)]="user.password"></ion-input>
    </ion-item>
  </ion-list>

  <ion-row class="ion-justify-content-end ion-margin-top">
    <ion-button (click)="login()">Login</ion-button>
  </ion-row>

  <ion-row *ngIf="!ready">
    <ion-col>
      <div class="ion-text-center">
        <ion-text color="danger">
          <h3>Please Login to Continue</h3>
        </ion-text>
      </div>
    </ion-col>
  </ion-row>

</ion-content>
