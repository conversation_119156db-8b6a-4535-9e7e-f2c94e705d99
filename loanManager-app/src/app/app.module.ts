import {NgModule} from '@angular/core';
import {BrowserModule} from '@angular/platform-browser';
import {RouteReuseStrategy} from '@angular/router';

import {IonicModule, IonicRouteStrategy} from '@ionic/angular';

import {AppComponent} from './app.component';
import {AppRoutingModule} from './app-routing.module';
import {NewLoanComponent} from "./new-loan/new-loan.component";
import {HomeComponent} from "./home/<USER>";
import {TodayListComponent} from "./today-list/today-list.component";
import {ArrearsListComponent} from "./arrears-list/arrears-list.component";
import {NewBorrowerComponent} from "./new-borrower/new-borrower.component";
import {ProfileComponent} from "./profile/profile.component";
import {LoanLookupComponent} from "./loan-lookup/loan-lookup.component";
import {PayLoanComponent} from "./pay-loan/pay-loan.component";
import {LoanDetailsComponent} from "./loan-details/loan-details.component";
import {PaymentListComponent} from "./payment-list/payment-list.component";
import {HttpClientModule} from "@angular/common/http";
import {FormsModule} from "@angular/forms";
import {CommonModule, DatePipe} from "@angular/common";
import {IonicStorageModule} from "@ionic/storage-angular";
import {CashInOutComponent} from "./cash-in-out/cash-in-out.component";

@NgModule({
  declarations: [AppComponent, NewLoanComponent, HomeComponent, TodayListComponent, ArrearsListComponent,
    NewBorrowerComponent, ProfileComponent, LoanLookupComponent, PayLoanComponent, LoanDetailsComponent,
    PaymentListComponent, CashInOutComponent],
  imports: [BrowserModule, IonicModule.forRoot(), AppRoutingModule, CommonModule,
    HttpClientModule, FormsModule, IonicStorageModule.forRoot(), FormsModule],
  providers: [{provide: RouteReuseStrategy, useClass: IonicRouteStrategy}, DatePipe],
  bootstrap: [AppComponent],
})
export class AppModule {
}
