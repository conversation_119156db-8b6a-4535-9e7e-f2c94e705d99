<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>
      <h3>SmartLoan</h3>
    </ion-title>
    <ion-title slot="end">නව ණය ලබා දීම</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">නව ණය ලබා දීම</ion-title>
    </ion-toolbar>
  </ion-header>

  <form #loanForm="ngForm">
    <ion-list class="">
      <ion-item lines="none">
        <ion-input name="borrower" label="ණය ගැනුම්කරු" required [(ngModel)]="loan.borrower.name">
          <ion-button slot="end" (click)="openBorrowers()">+</ion-button>
        </ion-input>
      </ion-item>

      <ion-item lines="none">
        <ion-select name="loanPlan" label="ණය සැලසුම්" [(ngModel)]="loan.loanPlan">
          <ion-select-option *ngFor="let plan of loanPlans" [value]="plan">
            {{ plan.name }}
          </ion-select-option>
        </ion-select>
      </ion-item>

      <ion-item lines="none">
        <ion-label>අයදුම් කළ දිනය</ion-label>
        <ion-datetime-button name="issueDate" label="issueDate" required datetime="datePicker"></ion-datetime-button>
      </ion-item>
      <ion-modal [keepContentsMounted]="true">
        <ng-template>
          <ion-datetime name="dateTime" presentation="date" id="datePicker" [(ngModel)]="dateSelected"></ion-datetime>
        </ng-template>
      </ion-modal>
      <ion-item lines="none">
        <ion-input name="loanAmount" label="ණය මුදල" type="number" required
                   [(ngModel)]="loan.loanAmount" (ngModelChange)="calculateAmounts()"
                   [min]="loan.loanPlan?.minAmount || 0"
                   [max]="loan.loanPlan?.maxAmount || 999999999"></ion-input>
      </ion-item>
      <ion-item lines="none" *ngIf="loan.loanPlan">
        <ion-note slot="start">
          පරාසය: {{loan.loanPlan.minAmount | number:'1.2-2'}} - {{loan.loanPlan.maxAmount | number:'1.2-2'}}
        </ion-note>
      </ion-item>
      <ion-item lines="none">
        <ion-input name="loanInstallment" label="ණය වාරිකය" type="number" required
                   [(ngModel)]="loanInstallment" [value]="loanInstallment | number: '1.2-2'"></ion-input>
      </ion-item>
      <ion-item lines="none">
        <ion-input name="repaymentAmount" label="පොලිය සමග ණය මුදල" type="number" required
                   [(ngModel)]="repaymentAmount" [value]="repaymentAmount | number: '1.2-2'"></ion-input>
      </ion-item>
      <ion-item lines="none">
        <ion-label>අතැති මුදලින් අඩු කරන්නද ?</ion-label>
        <ion-checkbox name="deductFromLedger" required [(ngModel)]="loan.deductFromLedger"></ion-checkbox>
      </ion-item>
    </ion-list>

    <ion-row class="ion-justify-content-end">
      <ion-button (click)="clear()">Clear</ion-button>
      <ion-button (click)="save()" [disabled]="!loanForm.form.valid || submitDisabled">Save</ion-button>
    </ion-row>
  </form>
</ion-content>
