<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>
      <h4>SmartLoan</h4>
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large"></ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-list lines="none">
    <ion-item style="color: blue;">
      <ion-label>අවසන් කළ ණය දීම් සංඛාව</ion-label>
      <ion-text slot="end">{{ loanCountStat.settledLoanCount }}</ion-text>
    </ion-item>
    <ion-item style="color: blue;">
      <ion-label>ක්‍රියාත්මක ණය දීම් සංඛාව</ion-label>
      <ion-text slot="end">{{ loanCountStat.activeLoanCount }}</ion-text>
    </ion-item>
    <ion-item style="color: red;">
      <ion-label>හිඟ ණය දීම් සංඛාව</ion-label>
      <ion-text slot="end">{{ loanCountStat.arrearsLoanCount }}</ion-text>
    </ion-item>
    <ion-item>
      <ion-label>මුළු ණය දීම් සංඛාව</ion-label>
      <ion-text slot="end">{{ loanCountStat.totalLoanCount }}</ion-text>
    </ion-item>

    <ion-item-divider color="dark">
      <ion-label>මාසික සාරාංශය</ion-label>
    </ion-item-divider>

    <ion-item color="primary">
      <ion-label>එකතු කළ මුදල</ion-label>
      <ion-text slot="end" color="primary">{{ monthlyStats.monthlyCollection | number: '1.2-2' }}</ion-text>
    </ion-item>
    <ion-item color="primary">
      <ion-label>ලාභය</ion-label>
      <ion-text slot="end" color="primary">{{ monthlyStats.monthlyProfit | number: '1.2-2' }}</ion-text>
    </ion-item>

    <ion-item-divider color="dark">
      <ion-label>ක්‍රියාත්මක ණය දීම්</ion-label>
    </ion-item-divider>

    <ion-item color="danger">
      <ion-label>ණයට දුන් මුදල</ion-label>
      <ion-text slot="end" color="danger">{{ ongoingLoanStat.amountLent | number: '1.2-2' }}</ion-text>
    </ion-item>
    <ion-item color="primary">
      <ion-label>එකතු කළ මුදල</ion-label>
      <ion-text slot="end" color="primary">{{ ongoingLoanStat.amountCollected | number: '1.2-2' }}</ion-text>
    </ion-item>
    <ion-item color="danger">
      <ion-label>එකතු කිරීමට ඇති මුදල</ion-label>
      <ion-text slot="end" color="danger">{{ (ongoingLoanStat.profit) * -1 | number: '1.2-2' }}</ion-text>
    </ion-item>

    <ion-item-divider color="dark">
      <ion-label>නිම කළ ණය දීම්</ion-label>
    </ion-item-divider>
    <ion-item style="color: red;">
      <ion-label>ණයට දුන් මුදල</ion-label>
      <ion-text slot="end">{{ settledLoanStat.amountLent | number: '1.2-2' }}</ion-text>
    </ion-item>
    <ion-item style="color: blue;">
      <ion-label>එකතු කළ මුදල</ion-label>
      <ion-text slot="end">{{ settledLoanStat.amountCollected | number: '1.2-2' }}</ion-text>
    </ion-item>
    <ion-item style="color: blue;">
      <ion-label>ලාභය</ion-label>
      <ion-text slot="end">{{ settledLoanStat.profit | number: '1.2-2' }}</ion-text>
    </ion-item>

    <ion-item-divider color="dark">
      <ion-label>මුළු ණය සාරාංශය</ion-label>
    </ion-item-divider>
    <ion-item color="danger">
      <ion-label>ණයට දුන් මුළු මුදල</ion-label>
      <ion-text slot="end" color="danger">{{ allLoanStat.amountLent | number: '1.2-2' }}</ion-text>
    </ion-item>
    <ion-item color="danger">
      <ion-label>පොලිය සමග මුළු මුදල</ion-label>
      <ion-text slot="end" color="danger">{{ allLoanStat.collectibleAmount | number: '1.2-2' }}</ion-text>
    </ion-item>
    <ion-item color="primary">
      <ion-label>එකතු කළ මුළු මුදල</ion-label>
      <ion-text slot="end" color="primary">{{ allLoanStat.amountCollected | number: '1.2-2' }}</ion-text>
    </ion-item>
    <ion-item color="danger">
      <ion-label>එකතු කිරීමට ඇති මුදල</ion-label>
      <ion-text slot="end" color="danger">{{ (allLoanStat.amountLent - allLoanStat.amountCollected)  | number: '1.2-2' }}</ion-text>
    </ion-item>
    <ion-item color="primary">
      <ion-label>ලැබීමට නියමිත ලාභය</ion-label>
      <ion-text slot="end" color="primary">{{ (allLoanStat.profit)  | number: '1.2-2' }}</ion-text>
    </ion-item>

    <ion-item-divider color="dark">
      <ion-label>අතැති මුදල් විස්තර</ion-label>
    </ion-item-divider>
    <ion-item color="primary">
      <ion-label>මුදල</ion-label>
      <ion-text slot="end" color="primary">{{ cashInHand | number: '1.2-2' }}</ion-text>
    </ion-item>
  </ion-list>

</ion-content>
