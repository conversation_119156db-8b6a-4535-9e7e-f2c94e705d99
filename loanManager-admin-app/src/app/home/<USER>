import {Component, OnInit} from '@angular/core';
import {LoanService} from "../service/loan.service";
import {DatePipe} from "@angular/common";
import {Storage} from "@ionic/storage-angular";
import {LoadingController, ViewWillEnter} from "@ionic/angular";
import {MonthlyPaymentStats} from "../model/monthlyPaymentStats";
import {StatService} from "../service/stat.service";
import {LoanCountStat} from "../model/loanStatusCount";
import {LoanStat} from "../model/loanStat";
import {environment} from "../../environments/environment";

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit, ViewWillEnter {

  loanCountStat: LoanCountStat = new LoanCountStat();
  monthlyStats: MonthlyPaymentStats = new MonthlyPaymentStats();
  ongoingLoanStat: LoanStat = new LoanStat();
  settledLoanStat: LoanStat = new LoanStat();
  allLoanStat: LoanStat = new LoanStat();
  cashInHand: number = 0;

  constructor(private bookingService: LoanService, private datePipe: DatePipe,
              private storage: Storage, private loadingCtrl: LoadingController,
              private statService: StatService) {
    this.monthlyStats = new MonthlyPaymentStats();
  }

  async ngOnInit() {
    await this.storage.create();
  }

  ionViewWillEnter(): void {
    this.getMonthlyStat();
    this.getOngoingLoanStats();
    this.getLoanCountStat();
    this.getSettledLoanStat();
    this.getAllLoanStat();
    this.getCashInHand();
  }

  getOngoingLoanStats() {
    this.statService.getOngoingLoanStats().subscribe((data: LoanStat) => {
      if (data != null) {
        this.ongoingLoanStat = data;
      }
    });
  }

  getLoanCountStat() {
    this.statService.getLoanCountStats().subscribe((data: LoanCountStat) => {
      if (data != null) {
        this.loanCountStat = data;
      }
    });
  }

  getMonthlyStat() {
    this.statService.getMonthlyStats().subscribe((data: MonthlyPaymentStats) => {
      if (data != null) {
        this.monthlyStats = data;
      }
    });
  }

  getSettledLoanStat() {
    this.statService.getSettledStats().subscribe((data: LoanStat) => {
      if (data != null) {
        this.settledLoanStat = data;
      }
    });
  }

  getAllLoanStat() {
    this.statService.getAllLoanStat().subscribe((data: LoanStat) => {
      if (data != null) {
        this.allLoanStat = data;
      }
    });
  }

  getCashInHand() {
    this.statService.getCashInHand(environment.appNo).subscribe((data: any) => {
      if (data != null) {
        this.cashInHand = data.currentBalance;
      }
    });
  }

}
