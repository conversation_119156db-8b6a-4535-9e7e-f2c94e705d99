import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {AppConstant} from '../app.constant';
import {Loan} from "../model/loan";

@Injectable({
  providedIn: 'root'
})
export class LoanService {

  constructor(private http: HttpClient) {
  }

  public save(loan: Loan): any {
    return this.http.post(AppConstant.SAVE_LOAN, loan);
  }

  public findActiveLoanByNic(nic: string): any {
    return this.http.get(AppConstant.FIND_ACTIVE_LOAN_BY_NIC, {params: {nic: nic}});
  }

  public findByLoanNo(loanNo: string): any {
    return this.http.get(AppConstant.FIND_LOAN_BY_LOAN_NO, {params: {loanNo: loanNo}});
  }
  public settleLoan(loanNo: string): any {
    return this.http.get(AppConstant.SETTLE_LOAN_BY_LOAN_NO, {params: {loanNo: loanNo}});
  }

}
