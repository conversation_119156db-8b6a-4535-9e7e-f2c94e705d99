import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {AppConstant} from '../app.constant';
import {LedgerRecord} from "../model/ledgerRecord";

@Injectable({
  providedIn: 'root'
})

export class LedgerRecordService {

  constructor(private http: HttpClient) {
  }

  public save(record: LedgerRecord): any {
    return this.http.post(AppConstant.SAVE_LEDGER_RECORD, record);
  }

  public findRecords(appNo: string, fromDate: string, toDate: string): any {
    return this.http.get(AppConstant.FIND_LEDGER_REC_BY_DATE_AND_APP_NO,
      {
        params: {
          appNo: appNo,
          fromDate: fromDate,
          toDate: toDate,
        }
      });
  }

  public findByType(appNo: string, type: string, fromDate: string, toDate: string): any {
    return this.http.get(AppConstant.FIND_LEDGER_REC_BY_TYPE_DATE_AND_APP_NO,
      {
        params: {
          appNo: appNo,
          type: type,
          fromDate: fromDate,
          toDate: toDate,
        }
      });
  }

}
