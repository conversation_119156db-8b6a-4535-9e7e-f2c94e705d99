import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {AppConstant} from '../app.constant';

@Injectable({
  providedIn: 'root'
})
export class LedgerService {

  constructor(private http: HttpClient) {
  }

  public correctLedger(appNo: string, amount: number): any {
    return this.http.get(AppConstant.CORRECT_LEDGER, {params: {appNo: appNo, amount: amount}});
  }

  public findLedger(appNo: string): any {
    return this.http.get(AppConstant.GET_LEDGER_BY_APP_NO, {params: {appNo: appNo}});
  }

}
