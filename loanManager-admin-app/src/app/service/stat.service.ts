import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {AppConstant} from '../app.constant';


@Injectable({
  providedIn: 'root'
})
export class StatService {

  constructor(private http: HttpClient) {
  }

  public getOngoingLoanStats(): any {
    return this.http.get(AppConstant.GET_ONGOING_LOAN_STATS);
  }

  public getLoanCountStats(): any {
    return this.http.get(AppConstant.GET_LOAN_COUNT_STATS);
  }

  public getMonthlyStats(): any {
    return this.http.get(AppConstant.GET_MONTHLY_PAYMENT_STATS);
  }

  public getSettledStats(): any {
    return this.http.get(AppConstant.GET_SETTLED_LOAN_STATS);
  }

  public getAllLoanStat(): any {
    return this.http.get(AppConstant.GET_ALL_LOAN_STATS);
  }

  public getCashInHand(appNo: string): any {
    return this.http.get(AppConstant.GET_LEDGER_BY_APP_NO, {params: {appNo: appNo}});
  }

}
