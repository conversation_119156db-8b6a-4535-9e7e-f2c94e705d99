import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {AppConstant} from '../app.constant';
import {Loan} from "../model/loan";

@Injectable({
  providedIn: 'root'
})
export class LoanRecordService {

  constructor(private http: HttpClient) {
  }

  public findTodayList(): any {
    return this.http.get(AppConstant.FIND_TODAY_LIST);
  }

  public findTodayLoanByName(name: string): any {
    return this.http.get(AppConstant.FIND_TODAY_LOAN_BY_NAME, {params: {name: name}});
  }

  public findTodayLoanByNic(nic: string): any {
    return this.http.get(AppConstant.FIND_TODAY_LOAN_BY_NIC, {params: {nic: nic}});
  }

  public findById(id: string): any {
    return this.http.get(AppConstant.FIND_BY_LOAN_RECORD_ID, {params: {id: id}});
  }

  public findArrearsRecordsByNic(nic: string): any {
    return this.http.get(AppConstant.FIND_ARREARS_LOAN_RECORD_BY_NIC, {params: {nic: nic}});
  }

  public findTodayRecordsByNic(nic: string): any {
    return this.http.get(AppConstant.FIND_TODAY_LOAN_RECORD_BY_NIC, {params: {nic: nic}});
  }

  public findRecordsByLoanNo(loanNo: string): any {
    return this.http.get(AppConstant.FIND_ALL_RECORDS_BY_LOAN_NO, {params: {loanNo: loanNo}});
  }

  public findPendingRecordsByLoanNo(loanNo: string): any {
    return this.http.get(AppConstant.FIND_PENDING_LOAN_RECORDS_BY_LOAN_NO, {params: {loanNo: loanNo}});
  }

  public findArrearsList(): any {
    return this.http.get(AppConstant.FIND_ARREARS_LIST);
  }

  public findArrearsLoanByName(name: string): any {
    return this.http.get(AppConstant.FIND_ARREARS_LOAN_BY_NAME, {params: {name: name}});
  }

  public pay(loanRecordId: string, payment: number): any {
    return this.http.get(AppConstant.PAY_LOAN_RECORD, {params: {loanRecordId: loanRecordId, payment: payment}});
  }

}
