import {NgModule} from '@angular/core';
import {BrowserModule} from '@angular/platform-browser';
import {RouteReuseStrategy} from '@angular/router';

import {IonicModule, IonicRouteStrategy} from '@ionic/angular';

import {AppComponent} from './app.component';
import {AppRoutingModule} from './app-routing.module';
import {HomeComponent} from "./home/<USER>";
import {ArrearsListComponent} from "./arrears-list/arrears-list.component";
import {ProfileComponent} from "./profile/profile.component";
import {LoanLookupComponent} from "./loan-lookup/loan-lookup.component";
import {LoanDetailsComponent} from "./loan-details/loan-details.component";
import {PaymentListComponent} from "./payment-list/payment-list.component";
import {HttpClientModule} from "@angular/common/http";
import {FormsModule} from "@angular/forms";
import {CommonModule, DatePipe, NgForOf} from "@angular/common";
import {IonicStorageModule} from "@ionic/storage-angular";
import {LedgerComponent} from "./ledger/ledger.component";

@NgModule({
  declarations: [AppComponent, HomeComponent, ArrearsListComponent, ProfileComponent,
    LoanLookupComponent, LoanDetailsComponent, LedgerComponent, PaymentListComponent],
    imports: [BrowserModule, IonicModule.forRoot(), AppRoutingModule, CommonModule,
        HttpClientModule, FormsModule, IonicStorageModule.forRoot(), FormsModule, IonicModule, NgForOf],
  providers: [{provide: RouteReuseStrategy, useClass: IonicRouteStrategy}, DatePipe],
  bootstrap: [AppComponent],
})
export class AppModule {
}
