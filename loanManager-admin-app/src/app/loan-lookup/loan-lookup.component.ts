import {Component, OnInit} from '@angular/core';
import {<PERSON><PERSON><PERSON>} from "../model/borrower";
import {BorrowerService} from "../service/borrower.service";
import {RouteService} from "../service/route.service";
import {<PERSON><PERSON><PERSON>ontroller, IonModal, LoadingController, ModalController, NavParams, ViewWillEnter} from "@ionic/angular";
import {LoanRecord} from "../model/loanRecord";
import {LoanRecordService} from "../service/loanRecord.service";
import {Loan} from "../model/loan";
import {LoanService} from "../service/loan.service";

@Component({
  selector: 'app-loan-look-up',
  templateUrl: './loan-lookup.component.html',
  styleUrls: ['./loan-lookup.component.scss']
})
export class LoanLookupComponent implements OnInit, ViewWillEnter {

  keyName: string = "";
  keyNic: string = "";
  keyTp: string = "";

  borrowerList: Array<Borrower> = [];
  borrower: Borrower = new Borrower();

  loans: Array<Loan> = [];

  loan: Loan = new Loan();
  loanRecords: Array<LoanRecord> = [];

  loanNo: string = "";
  modal: IonModal | undefined;

  constructor(private borrowerService: BorrowerService, private routeService: RouteService,
              private alertCtrl: AlertController, private loanRecordService: LoanRecordService,
              private loanService: LoanService, private modalController: ModalController,
              private loadingCtrl: LoadingController) {
  }

  ngOnInit() {
  }

  ionViewWillEnter() {
    this.clear();
  }

  searchBorrowerByTp() {
    if (this.keyTp.length == 10) {
      this.clear();
      this.showLoading();
      this.borrowerService.findByMobile(this.keyTp).subscribe((data: Borrower) => {
        if (data != null) {
          this.clearRecords();
          this.borrower = data;
          this.findActiveLoanByNic(this.borrower.nic);
          this.loadingCtrl.dismiss();
        } else {
          this.loadingCtrl.dismiss();
          this.showAlert("ණය ගැනුම් කරු සොයා ගැනීමට නොහැකි ය");
          this.clear();
        }
      });
    }
  }

  searchBorrowerByNic() {
    if (this.keyNic.length > 8) {
      this.clearRecords();
      this.showLoading();
      this.borrowerService.findByNic(this.keyNic).subscribe((data: Borrower) => {
        if (data != null) {
          this.clearRecords();
          this.borrower = data;
          this.findActiveLoanByNic(this.borrower.nic);
          this.loadingCtrl.dismiss();
        } else {
          this.loadingCtrl.dismiss();
          this.showAlert("ණය ගැනුම් කරු සොයා ගැනීමට නොහැකි ය");
          this.clear();
        }
      });
    }
  }

  searchBorrowerByNameLike() {
    if (this.keyName.length > 2) {
      this.showLoading();
      this.borrowerService.findByNameLike(this.keyName).subscribe((data: Array<Borrower>) => {
        if (data != null) {
          this.clearRecords();
          this.borrowerList = data;
          this.loadingCtrl.dismiss();
        } else {
          this.loadingCtrl.dismiss();
          this.showAlert("ණය ගැනුම් කරු සොයා ගැනීමට නොහැකි ය");
          this.clear();
        }
      });
    }
  }

  selectBorrower(borrower: Borrower) {
    this.clear();
    this.findActiveLoanByNic(borrower.nic);
  }

  findLoans(nic: string) {
    this.findByLoanNo(nic);
  }

  findActiveLoanByNic(nic: string) {
    this.showLoading();
    this.loanService.findActiveLoanByNic(nic).subscribe((data: any) => {
      if (data) {
        this.loans = data;
        this.loadingCtrl.dismiss();
      } else {
        this.showAlert("ණය දීම සොයා ගත නොහැකි ය");
        this.clear();
        this.loadingCtrl.dismiss();
      }
    });
  }

  findByLoanNo(loanNo: string) {
    this.showLoading();
    this.loanService.findByLoanNo(loanNo).subscribe((data: any) => {
      if (data) {
        this.loan = data;
        this.findPendingLoanRecords();
        this.loadingCtrl.dismiss();
      } else {
        this.showAlert("ණය දීම සොයා ගත නොහැකි ය");
        this.clear();
        this.loadingCtrl.dismiss();
      }
    });
  }

  public alertButtons = [
    {
      text: 'එපා',
      role: 'cancel',
      handler: () => {
      },
    },
    {
      text: 'ඔව්',
      role: 'confirm',
      handler: () => {
        this.settleLoan(this.loan.loanNo);
      },
    },
  ];

  settleLoan(loanNo: string) {
    this.loanService.settleLoan(loanNo).subscribe((data: any) => {
      if (data) {
        this.showAlert("ණය මුදල පියවන ලදී");
      } else {
        this.showAlert("ණය මුදල පියවීම අසාර්ථකයි");
      }
    });
  }

  findPendingLoanRecords() {
    this.loanRecordService.findPendingRecordsByLoanNo(this.loan.loanNo).subscribe((data: Array<LoanRecord>) => {
      if (data) {
        this.loanRecords = data;
      }
    });
  }

  clear() {
    this.keyName = "";
    this.keyNic = "";
    this.keyTp = "";
    this.clearRecords();
  }

  clearRecords() {
    this.borrower = new Borrower();
    this.borrowerList = [];
    this.loans = [];
    this.loan = new Loan();
    this.loanRecords = [];
  }

  async showLoading() {
    const loading = await this.loadingCtrl.create({
      message: 'Loading...',
      spinner: 'bubbles',
      backdropDismiss: true
    });
    await loading.present();
  }

  async showAlert(message: string) {
    const alert = await this.alertCtrl.create({
      header: 'දැනුම් දීම',
      message: message,
      buttons: ['වසන්න']
    });
    await alert.present();
  }

}
