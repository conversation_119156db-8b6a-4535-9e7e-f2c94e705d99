<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>
      <h3>SmartLoan</h3>
    </ion-title>
    <ion-title slot="end">ණය සෙවීම්</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">ණය සෙවීම්</ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-list class="ion-justify-content-center">
    <ion-item>
      <ion-input label="දුරකථන අංකය" required [(ngModel)]="keyTp">
        <ion-button (click)="searchBorrowerByTp()" slot="end">
          <ion-icon name="search-outline"></ion-icon>
        </ion-button>
      </ion-input>
    </ion-item>
    <ion-item>
      <ion-input label="හැඳුනුම් පත" required [(ngModel)]="keyNic">
        <ion-button (click)="searchBorrowerByNic()" slot="end">
          <ion-icon name="search-outline"></ion-icon>
        </ion-button>
      </ion-input>
    </ion-item>
    <ion-item>
      <ion-input label="නම" required [(ngModel)]="keyName">
        <ion-button (click)="searchBorrowerByNameLike()" slot="end">
          <ion-icon name="search-outline"></ion-icon>
        </ion-button>
      </ion-input>
    </ion-item>
  </ion-list>

  <ion-list class="ion-justify-content-center" lines="none" *ngIf="borrowerList.length > 0">
    <ng-container *ngFor="let record of borrowerList; let i = index">
      <ion-item (click)="selectBorrower(record)">
        <ion-label>{{ i + 1 }}</ion-label>
        <ion-text style="width: 80%;">
          <p>{{ record?.name }}</p>
          <p>{{ record?.telephone1 + '/' + record?.telephone2 }}</p>
          <p>{{ record?.address }}</p>
        </ion-text>
      </ion-item>
      <ion-item-divider></ion-item-divider>
    </ng-container>
  </ion-list>

  <ion-list class="ion-justify-content-center" lines="none" *ngIf="loans.length > 0">
    <ng-container *ngFor="let ln of loans; let i = index">
      <ion-item ion-ripple-effect (click)="findLoans(ln.loanNo)">
        <ion-label>{{ (i + 1) + '.' + ln.loanPlan?.name }}</ion-label>
        <ion-text slot="end">{{ 'Amount - ' + ln.loanAmount }}</ion-text>
      </ion-item>
      <ion-item>
        <ion-text slot="end">{{ 'Balance - ' + ln.balance }}</ion-text>
      </ion-item>
      <ion-item-divider></ion-item-divider>
    </ng-container>
  </ion-list>

  <ion-list *ngIf="loan?.loanNo" lines="none">
    <ion-item>
      <ion-label>ණය අංකය</ion-label>
      <ion-text slot="end">{{ loan.loanNo }}</ion-text>
    </ion-item>
    <ion-item style="color: blue;">
      <ion-label>ණය ගැනුම් කරු</ion-label>
      <ion-text slot="end">{{ loan.borrower.name }}</ion-text>
    </ion-item>
    <ion-item style="color: blue;">
      <ion-label>ණය සැලැස්ම</ion-label>
      <ion-text slot="end">{{ loan.loanPlan?.name }}</ion-text>
    </ion-item>
    <ion-item style="color: blue;">
      <ion-label>ණය මුදල</ion-label>
      <ion-text slot="end">{{ loan.loanAmount | number: '1.2-2' }}</ion-text>
    </ion-item>
    <ion-item style="color: blue;">
      <ion-label>පොලිය සමග ණය මුදල</ion-label>
      <ion-text slot="end">{{ loan.loanAmountWithInterest | number: '1.2-2' }}</ion-text>
    </ion-item>
    <ion-item style="color: red;">
      <ion-label>ශේෂය</ion-label>
      <ion-text slot="end">{{ loan.balance | number: '1.2-2' }}</ion-text>
    </ion-item>
    <ion-item style="color: red;">
      <ion-label>ගෙවීමට ඇති වාරික ගණන</ion-label>
      <ion-text slot="end">{{ loan.installmentLeft }}</ion-text>
    </ion-item>
    <ion-item style="color: #00ff6f;">
      <ion-label>වැඩි පුර ගෙවූ මුදල</ion-label>
      <ion-text slot="end">{{ loan.overPaidAmount | number: '1.2-2' }}</ion-text>
    </ion-item>
    <ion-item>
      <ion-button slot="end" size="medium" id="settlement-alert">අවසන් කරන්න
      </ion-button>
      <ion-alert
        trigger="settlement-alert"
        header="ණය අවසන් කිරීම තහවුරු කරන්න ද?"
        [buttons]="alertButtons">
      </ion-alert>
    </ion-item>
    <ion-item-divider></ion-item-divider>
    <ion-list class="ion-justify-content-center ion-margin-start" *ngFor="let record of loanRecords" lines="none">
      <ion-item>
        <ion-label>දිනය</ion-label>
        <ion-text>{{ record.installmentDate }}</ion-text>
      </ion-item>
      <ion-item>
        <ion-label>වාරිකය</ion-label>
        <ion-text>{{ record.installmentAmount | number: '1.2-2' }}</ion-text>
      </ion-item>
      <ion-item>
        <ion-label>ගෙවීම</ion-label>
        <ion-text>{{ record.paidAmount | number: '1.2-2' }}</ion-text>
      </ion-item>
      <ion-item>
        <ion-label>ශේෂය</ion-label>
        <ion-text>{{ record.balance | number: '1.2-2' }}</ion-text>
      </ion-item>
      <ion-item-divider></ion-item-divider>
    </ion-list>
  </ion-list>

</ion-content>
