<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>
      <h3>SmartLoan</h3>
    </ion-title>
    <ion-title slot="end">ලෙජරය</ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="findLedger();findTodayRecords()">
        <ion-icon slot="icon-only" name="reload-outline" size="" color="primary"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">ලෙජරය</ion-title>
    </ion-toolbar>
  </ion-header>
  <ion-item lines="none">
    <ion-label>යෙදුමේ අංකය</ion-label>
    <ion-text>{{ ledger.appNo }}</ion-text>
  </ion-item>
  <ion-item lines="none">
    <ion-label>අතැති මුදල</ion-label>
    <ion-button (click)="correctLedgerPopUp()" class="ion-margin-end">නිවැරදි කරන්න</ion-button>
    <ion-text>{{ ledger.currentBalance | number: '1.2-2' }}</ion-text>
  </ion-item>
  <ion-item-divider></ion-item-divider>
  <ion-item lines="none">
    <ion-label>දින සිට</ion-label>
    <ion-datetime-button name="ledgerDateFrom" label="Select Date" required datetime="ledgerDatePickerFrom"></ion-datetime-button>
  </ion-item>
  <ion-item lines="none">
    <ion-label>දින දක්වා</ion-label>
    <ion-datetime-button name="ledgerDateTo" label="Select Date" required datetime="ledgerDatePickerTo"></ion-datetime-button>
  </ion-item>
  <ion-item lines="none">
    <ion-select label="කාණ්ඩය මගින් සොයන්න" [(ngModel)]="selectedType" (ionChange)="filterByTypeAndDate()">
      <ion-select-option *ngFor="let expense of types" [value]="expense">{{ expense }}</ion-select-option>
    </ion-select>
  </ion-item>
  <ion-modal [keepContentsMounted]="true">
    <ng-template>
      <ion-datetime name="ledgerDateFrom" presentation="date" id="ledgerDatePickerFrom" [(ngModel)]="ledgerDateFrom"></ion-datetime>
    </ng-template>
  </ion-modal>
  <ion-modal [keepContentsMounted]="true">
    <ng-template>
      <ion-datetime name="ledgerDateTo" presentation="date" id="ledgerDatePickerTo" [(ngModel)]="ledgerDateTo"
                    (ionChange)="findPaymentByDate()"></ion-datetime>
    </ng-template>
  </ion-modal>
  <ion-list class="ion-justify-content-center" *ngFor="let rec of records; let i = index" lines="none"
            [inset]="true">
    <ion-item>
      <ion-label class="ion-no-margin">දිනය හා වෙලාව</ion-label>
      <ion-text>{{ rec.dateTime | date: 'yyyy-MM-dd HH:mm:ss' }}</ion-text>
    </ion-item>
    <ion-item>
      <ion-label class="ion-no-margin">මුදල</ion-label>
      <ion-text>{{ rec.amount }}</ion-text>
    </ion-item>
    <ion-item>
      <ion-label class="ion-no-margin">එම අවස්ථාවේ ලේජර අගය</ion-label>
      <ion-text>{{ rec.ledgerValue | number: '1.2-2' }}</ion-text>
    </ion-item>
    <ion-item>
      <ion-label class="ion-no-margin">හේතුව</ion-label>
      <ion-text>{{ rec.reason }}</ion-text>
    </ion-item>
    <ion-item *ngIf="rec.remark">
      <ion-label class="ion-no-margin">සටහන</ion-label>
      <ion-text>{{ rec.remark }}</ion-text>
    </ion-item>
  </ion-list>

</ion-content>
