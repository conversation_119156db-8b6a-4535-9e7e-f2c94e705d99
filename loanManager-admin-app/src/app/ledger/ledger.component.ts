import {Component} from '@angular/core';
import {DatePipe} from "@angular/common";
import {LedgerService} from "../service/ledger.service";
import {LedgerRecordService} from "../service/ledgerRecord.service";
import {environment} from "../../environments/environment";
import {LedgerRecord} from "../model/ledgerRecord";
import {Ledger} from "../model/ledger";
import {AlertController, ModalController, ViewWillEnter} from "@ionic/angular";

@Component({
  selector: 'app-ledger',
  templateUrl: './ledger.component.html',
  styleUrls: ['./ledger.component.scss']
})
export class LedgerComponent implements ViewWillEnter {

  records: Array<LedgerRecord> = [];
  ledgerDateFrom: string;
  ledgerDateTo: string;
  ledger: Ledger = new Ledger();
  types: Array<String> = [];
  selectedType: string = "";

  constructor(private ledgerService: LedgerService, private ledgerRecordService: LedgerRecordService,
              private datePipe: DatePipe, private alertCtrl: AlertController,
              private modalController: ModalController) {
    this.ledgerDateFrom = new Date().toISOString();
    this.ledgerDateTo = new Date().toISOString();
  }

  async ionViewWillEnter() {
    this.findLedger();
    this.findTodayRecords();
    this.ledgerDateFrom = new Date().toISOString();
    this.ledgerDateTo = new Date().toISOString();
    this.types = ["Salary", "Fuel", "Internet", "Bank", "Stationary", "Other"];
  }

  findLedger() {
    this.ledgerService.findLedger(environment.appNo).subscribe((data: any) => {
      if (data != null) {
        this.ledger = data;
      }
    });
  }

  filterByTypeAndDate() {
    this.records = [];
    this.ledgerRecordService.findByType(environment.appNo, this.selectedType,
      this.datePipe.transform(this.ledgerDateFrom, 'yyyy-MM-dd')!.toString(),
      this.datePipe.transform(this.ledgerDateTo, 'yyyy-MM-dd')!.toString())
      .subscribe((data: any) => {
        if (data != null) {
          this.records = data;
        }
      });
  }

  findTodayRecords() {
    this.records = [];
    this.ledgerRecordService.findRecords(environment.appNo,
      this.datePipe.transform(new Date(), 'yyyy-MM-dd')!.toString(),
      this.datePipe.transform(new Date(), 'yyyy-MM-dd')!.toString())
      .subscribe((data: any) => {
        if (data != null) {
          this.records = data;
        }
      });
  }

  findPaymentByDate() {
    this.records = [];
    this.modalController.dismiss();
    this.ledgerRecordService.findRecords(environment.appNo,
      this.datePipe.transform(this.ledgerDateFrom, 'yyyy-MM-dd')!.toString(),
      this.datePipe.transform(this.ledgerDateTo, 'yyyy-MM-dd')!.toString())
      .subscribe((data: any) => {
        if (data != null) {
          this.records = data;
        }
      });
  }

  correctLedger(appNo: string, amount: number) {
    this.ledgerService.correctLedger(appNo, amount).subscribe((data: any) => {
      if (data === true) {
        this.findLedger();
        this.findTodayRecords();
      }
    });
  }

  async correctLedgerPopUp() {
    const alert = await this.alertCtrl.create({
      header: 'වර්ථමාන ශේෂය ඇතුල් කරන්න',
      inputs: [
        {
          name: 'inputField',
          type: 'text',
          placeholder: ''
        }
      ],
      buttons: [
        {
          text: 'අවලංගු කරන්න',
          role: 'cancel',
          handler: () => {
            console.log('Cancel clicked');
          }
        },
        {
          text: 'හරි',
          handler: (data) => {
            this.correctLedger(environment.appNo, data.inputField);
          }
        }
      ]
    });

    await alert.present();
  }

}
