import {Component, OnInit} from '@angular/core';
import {Storage} from "@ionic/storage-angular";
import {AlertController, ViewWillEnter} from "@ionic/angular";
import {User} from "../model/user";
import {UserService} from "../service/user.service";

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit, ViewWillEnter {

  user: User = new User();

  ready: boolean = true;

  constructor(private storage: Storage, private userService: UserService,
              private alertCtrl: AlertController) {
  }

  async ngOnInit() {
    await this.storage.create();
  }

  async ionViewWillEnter() {
    let user = await this.storage.get('user');
    if (user) {
      this.user = user;
    } else {
      this.ready = false;
    }
  }

  login() {

  }

  async showMessage() {
    const alert = await this.alertCtrl.create({
      header: 'Notification',
      message: 'Please Fill the Form'
    });
    await alert.present();
  }

  async showAlert() {
    const alert = await this.alertCtrl.create({
      header: 'Notification',
      message: "Successfully Added",
      buttons: ['Close']
    });
    await alert.present();
  }

}
