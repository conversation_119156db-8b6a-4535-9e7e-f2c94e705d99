/* Use Ionic utility classes only - no custom CSS */

ion-item.search-item ion-button {
  --background: var(--app-gradient-primary);
  --color: white;
  --border-radius: 8px;
  --box-shadow: 0 2px 8px rgba(89, 12, 48, 0.3);
  margin-left: 8px;
}

/* Enhanced accordion styling */
ion-accordion-group {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin: 12px 0;
  background: white;
}

ion-accordion {
  --background: white;
  border-bottom: 1px solid #f0f0f0;
}

ion-accordion:last-child {
  border-bottom: none;
}

/* Accordion header styling */
ion-item[slot="header"] {
  --background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  --color: var(--ion-color-primary);
  --padding-start: 20px;
  --padding-end: 20px;
  --min-height: 60px;
  font-weight: 600;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

ion-accordion.accordion-expanding ion-item[slot="header"],
ion-accordion.accordion-expanded ion-item[slot="header"] {
  --background: var(--app-gradient-primary);
  --color: white;
  border-left-color: var(--ion-color-secondary);
  box-shadow: 0 2px 8px rgba(89, 12, 48, 0.2);
}

/* Accordion content styling */
div[slot="content"] {
  padding: 20px;
  background: #fafafa;
}

/* Contact information styling */
ion-grid {
  padding: 0;
}

ion-row {
  margin-bottom: 16px;
}

ion-col {
  padding: 0 8px;
}

ion-label.address-label {
  font-size: 0.9rem;
  color: #6c757d;
  line-height: 1.4;
  font-weight: 400;
}

ion-note a {
  color: var(--ion-color-primary);
  text-decoration: none;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  background: rgba(89, 12, 48, 0.1);
  transition: all 0.3s ease;
  display: inline-block;
  margin: 2px 0;
}

ion-note a:hover {
  background: var(--ion-color-primary);
  color: white;
  transform: scale(1.05);
}

/* Loan details styling */
ion-item-group {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin: 16px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-left: 4px solid var(--ion-color-warning);
}

ion-item-group.margin-top {
  margin-top: 24px;
  border-left-color: var(--ion-color-danger);
}

ion-item-group ion-item {
  --background: transparent;
  --padding-start: 0;
  --padding-end: 0;
  --min-height: 40px;
  margin: 4px 0;
  border-radius: 6px;
  transition: all 0.3s ease;
}

ion-item-group ion-item:hover {
  --background: #f8f9fa;
  transform: translateX(4px);
}

ion-item-group ion-label {
  font-weight: 500;
  color: #495057;
  font-size: 0.9rem;
}

ion-item-group ion-text {
  font-weight: 600;
  color: var(--ion-color-primary);
  font-size: 0.95rem;
}

/* Action buttons styling */
ion-row.ion-float-end {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
}

ion-button.details-button {
  --background: var(--app-gradient-primary);
  --color: white;
  --border-radius: 12px;
  --box-shadow: 0 4px 15px rgba(89, 12, 48, 0.3);
  --padding-start: 20px;
  --padding-end: 20px;
  font-weight: 600;
  text-transform: none;
  letter-spacing: 0.5px;
}
