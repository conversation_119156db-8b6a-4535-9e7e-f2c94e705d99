import {Component, OnInit} from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON>roller, LoadingController, ModalController, ViewWillEnter} from "@ionic/angular";
import {LoanRecord} from "../model/loanRecord";
import {LoanRecordService} from "../service/loanRecord.service";
import {LoanDetailsComponent} from "../loan-details/loan-details.component";
import {GroupedLoanRecord} from "../model/groupedLoanRecord";

@Component({
  selector: 'app-arrears-list',
  templateUrl: './arrears-list.component.html',
  styleUrls: ['./arrears-list.component.scss']
})
export class ArrearsListComponent implements OnInit, ViewWillEnter {

  loanRecordGroup: Array<GroupedLoanRecord> = [];
  loanRecords: Array<LoanRecord> = [];
  keyName: string = "";

  tempNic: string | undefined = ""

  loading: any;

  constructor(private loanRecordService: LoanRecordService, private alertCtrl: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
              private modalController: ModalController, private loadingCtrl: LoadingController) {
  }

  async ngOnInit() {

  }

  async ionViewWillEnter() {
    this.findArrearsList();
  }

  findArrearsList() {
    this.loanRecordService.findArrearsList().subscribe((data: any) => {
      if (data != null) {
        this.loanRecordGroup = data;
      }
    });
  }

  findLoanRecord(nic: string | undefined) {
    this.loanRecords = [];
    if (this.tempNic != nic) {
      this.tempNic = nic;
      this.showLoading();
      if (nic != undefined) {
        this.loanRecordService.findArrearsRecordsByNic(nic).subscribe((data: any) => {
          if (data != null) {
            this.loanRecords = data;
          }
          this.loadingCtrl.dismiss();
        });
      }
    }
  }

  searchByName() {
    this.loanRecordService.findArrearsLoanByName(this.keyName).subscribe((data: any) => {
      if (data != null) {
        this.loanRecordGroup = data;
      }
    });
  }

  async openLoan(loanNo: any) {
    const modal = await this.modalController.create({
      component: LoanDetailsComponent,
      componentProps: {loanNo}
    });
    modal.onDidDismiss().then((data) => {
      //this.findArrearsList();
    });
    return await modal.present();
  }

  async showMessage() {
    const alert = await this.alertCtrl.create({
      header: 'දැනුම් දීම',
      message: 'පෝරමය සම්පූර්ණ කරන්න',
      cssClass: 'custom-alert'
    });
    await alert.present();
  }

  async showAlert() {
    const alert = await this.alertCtrl.create({
      header: 'දැනුම් දීම',
      message: "සාර්ථකව සුරකින ලදී",
      buttons: ['Close'],
      cssClass: 'custom-alert'
    });
    await alert.present();
  }

  async showLoading() {
    this.loading = await this.loadingCtrl.create({
      message: 'Loading...',
      spinner: 'bubbles',
      backdropDismiss: true
    });
    await this.loading.present();
  }

}
