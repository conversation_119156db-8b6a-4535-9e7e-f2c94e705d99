import {Component} from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>er, LoadingController, ModalController, ViewWillEnter} from "@ionic/angular";
import {PaymentService} from "../service/payment.service";
import {Payment} from "../model/payment";
import {DatePipe} from "@angular/common";

@Component({
  selector: 'app-payments-list',
  templateUrl: './payment-list.component.html',
  styleUrls: ['./payment-list.component.scss']
})
export class PaymentListComponent implements ViewWillEnter {

  payments: Array<Payment> = [];
  paymentDate: string;
  keyName: string = "";
  totalAmount = 0;

  loading: any;

  constructor(private paymentService: PaymentService, private datePipe: DatePipe,
              private modalController: ModalController, private alertCtrl: AlertController,
              private loadingCtrl: LoadingController) {
    this.paymentDate = new Date().toISOString();
  }

  async ionViewWillEnter() {
    this.findTodayPayments();
    this.paymentDate = new Date().toISOString();
  }

  findTodayPayments() {
    this.payments = [];
    this.paymentService.findTodayPayments().subscribe((data: any) => {
      if (data != null) {
        this.payments = data;
        this.calcTotalAmount();
      }
    });
  }

  findPaymentByDate() {
    this.payments = [];
    this.modalController.dismiss();
    this.paymentService.findPaymentsByDate(this.datePipe.transform(this.paymentDate,
      'yyyy-MM-dd')!.toString()).subscribe((data: any) => {
      if (data != null) {
        this.payments = data;
        this.calcTotalAmount();
      }
    });
  }

  calcTotalAmount() {
    this.totalAmount = 0;
    for (let payment of this.payments) {
      this.totalAmount = this.totalAmount + payment.amount;
    }
  }

  amend(loanRecordId: string, paymentId: string) {
    this.showLoading();
    this.paymentService.amend(loanRecordId, paymentId).subscribe((data: any) => {
      if (data.code === 200) {
        this.showAlert("සාර්ථකව අවලංගු කරණ ලදී");
        this.findTodayPayments();
      } else {
        this.showAlert("අසාර්ථක විය");
      }
      this.loading.dismiss();
    });
  }

  async showLoading() {
    this.loading = await this.loadingCtrl.create({
      message: 'Loading...',
      spinner: 'bubbles',
      backdropDismiss: true
    });
    await this.loading.present();
  }

  async showAlert(message: string) {
    const alert = await this.alertCtrl.create({
      header: 'Notification',
      message: message,
      buttons: ['Close']
    });
    await alert.present();
  }

  async showConfirmation(loanRecordId: string, paymentId: string) {
    const alert = await this.alertCtrl.create({
      header: 'තහවුරු කිරීම',
      message: 'ඔබට මෙම ගෙවීම අවලංගු කිරීමට අවශ්‍ය ද?',
      buttons: [
        {
          text: 'නැහැ',
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {

          }
        }, {
          text: 'තහවුරු කරන්න',
          handler: () => {
            this.amend(loanRecordId, paymentId);
          }
        }
      ]
    });

    await alert.present();
  }

}
