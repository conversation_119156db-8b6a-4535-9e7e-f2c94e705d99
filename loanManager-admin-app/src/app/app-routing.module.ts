import {NgModule} from '@angular/core';
import {PreloadAllModules, RouterModule, Routes} from '@angular/router';
import {ArrearsListComponent} from "./arrears-list/arrears-list.component";
import {PaymentListComponent} from "./payment-list/payment-list.component";
import {LoanLookupComponent} from "./loan-lookup/loan-lookup.component";
import {ProfileComponent} from "./profile/profile.component";
import {HomeComponent} from "./home/<USER>";
import {LedgerComponent} from "./ledger/ledger.component";

const routes: Routes = [
  {
    path: '',
    redirectTo: 'home',
    pathMatch: 'full',
  },
  {
    path: 'arrears-list',
    component: ArrearsListComponent
  },
  {
    path: 'payment-list',
    component: PaymentListComponent
  },
  {
    path: 'borrower-lookup',
    component: LoanLookupComponent
  },
  {
    path: 'ledger',
    component: LedgerComponent
  },
  {
    path: 'profile',
    component: ProfileComponent
  },
  {
    path: 'home',
    component: HomeComponent
  }
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {preloadingStrategy: PreloadAllModules})
  ],
  exports: [RouterModule]
})
export class AppRoutingModule {
}
